--c99 --gnu -c --cpu Cortex-M0+ -D__MICROLIB -g -O1 --apcs=interwork --split_sections -I ..\app\include -I ..\drivers\include -I ..\peripheral\include -I ..\rtthread\bsp -I ..\rtthread\include -I ..\stm32L0xx_hal\Inc -I ..\system\include -I ..\lcd12864 -I ..\protocol\include -I ..\net -I ..\net\EC600x\include -I ..\stm32L0xx_hal\Inc\Legacy --locale=english
-D__UVISION_VERSION="540" -DSTM32L072xx -DSTM32L072xx -DUSE_HAL_DRIVER
-o .\objects\stm32l0xx_hal_adc_ex.o --omf_browse .\objects\stm32l0xx_hal_adc_ex.crf --depend .\objects\stm32l0xx_hal_adc_ex.d "..\stm32L0xx_hal\Src\stm32l0xx_hal_adc_ex.c"