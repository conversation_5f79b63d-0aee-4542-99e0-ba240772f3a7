/**
 * 快速硬件资源测试函数
 * 简化版本，用于快速验证硬件资源状态
 */

#include "main.h"
#include "stm32l0xx_hal.h"
#include <stdio.h>

/**
 * 快速硬件资源测试
 * 在系统启动时进行基本的硬件验证
 */
void quick_hardware_test(void)
{
    printf("\n========== 快速硬件测试开始 ==========\r\n");
    
    // 1. 系统时钟检查
    printf("1. 系统时钟检查:\r\n");
    uint32_t sysclk = HAL_RCC_GetSysClockFreq();
    uint32_t hclk = HAL_RCC_GetHCLKFreq();
    printf("   系统时钟: %lu Hz\r\n", sysclk);
    printf("   HCLK: %lu Hz\r\n", hclk);
    printf("   状态: %s\r\n", (hclk > 1000000) ? "正常" : "异常");
    
    // 2. GPIO基本测试
    printf("2. GPIO基本测试:\r\n");
    printf("   LED GPIO (PB12) 闪烁测试...\r\n");
    for(int i = 0; i < 3; i++) {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_SET);
        HAL_Delay(200);
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_RESET);
        HAL_Delay(200);
    }
    printf("   LED闪烁完成\r\n");
    
    // 3. 系统Tick测试
    printf("3. 系统Tick测试:\r\n");
    uint32_t tick_start = HAL_GetTick();
    HAL_Delay(1000);
    uint32_t tick_end = HAL_GetTick();
    uint32_t elapsed = tick_end - tick_start;
    printf("   延时1000ms，实际用时: %lu ms\r\n", elapsed);
    printf("   状态: %s\r\n", (elapsed >= 950 && elapsed <= 1050) ? "正常" : "异常");
    
    // 4. 串口输出测试
    printf("4. 串口输出测试:\r\n");
    printf("   如果您能看到这条消息，说明调试串口工作正常\r\n");
    printf("   状态: 正常\r\n");
    
    // 5. 电源电压检查（如果ADC已初始化）
    printf("5. 电源电压检查:\r\n");
    extern float vdda_voltage;
    if(vdda_voltage > 0) {
        printf("   当前电源电压: %.2f V\r\n", vdda_voltage);
        printf("   状态: %s\r\n", (vdda_voltage > 2.0f && vdda_voltage < 4.0f) ? "正常" : "异常");
    } else {
        printf("   电源电压: 未检测到\r\n");
        printf("   状态: 待检测\r\n");
    }
    
    // 6. 按键状态检查
    printf("6. 按键状态检查:\r\n");
    GPIO_PinState key_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_13);
    printf("   按键状态: %s\r\n", key_state == GPIO_PIN_SET ? "未按下" : "按下");
    printf("   状态: 正常\r\n");
    
    printf("========== 快速硬件测试完成 ==========\r\n\n");
}

/**
 * 硬件状态监控函数
 * 定期检查关键硬件状态
 */
void hardware_status_monitor(void)
{
    static uint32_t last_monitor_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 每10秒监控一次
    if(current_time - last_monitor_time >= 10000) {
        printf("\n--- 硬件状态监控 ---\r\n");
        
        // 检查系统运行时间
        printf("系统运行时间: %lu ms\r\n", current_time);
        
        // 检查电源电压
        extern float vdda_voltage;
        if(vdda_voltage > 0) {
            printf("电源电压: %.2f V", vdda_voltage);
            if(vdda_voltage < 2.5f) {
                printf(" [警告: 电压偏低]");
            } else if(vdda_voltage > 3.6f) {
                printf(" [警告: 电压偏高]");
            }
            printf("\r\n");
        }
        
        // 检查按键状态
        GPIO_PinState key_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_13);
        if(key_state == GPIO_PIN_RESET) {
            printf("按键被按下\r\n");
        }
        
        printf("--- 监控完成 ---\r\n\n");
        last_monitor_time = current_time;
    }
}

/**
 * 硬件错误检测函数
 */
void hardware_error_check(void)
{
    // 检查系统时钟
    uint32_t hclk = HAL_RCC_GetHCLKFreq();
    if(hclk < 1000000) {
        printf("[错误] 系统时钟异常: %lu Hz\r\n", hclk);
    }
    
    // 检查电源电压
    extern float vdda_voltage;
    if(vdda_voltage > 0) {
        if(vdda_voltage < 2.0f) {
            printf("[错误] 电源电压过低: %.2f V\r\n", vdda_voltage);
        } else if(vdda_voltage > 4.0f) {
            printf("[错误] 电源电压过高: %.2f V\r\n", vdda_voltage);
        }
    }
}

/**
 * 硬件诊断信息输出
 */
void hardware_diagnostic_info(void)
{
    printf("\n========== 硬件诊断信息 ==========\r\n");
    
    // 系统信息
    printf("MCU型号: STM32L072CBT6\r\n");
    printf("Flash大小: 128KB\r\n");
    printf("RAM大小: 20KB\r\n");
    printf("EEPROM大小: 6KB\r\n");
    
    // 时钟信息
    printf("\n时钟配置:\r\n");
    printf("  系统时钟: %lu Hz\r\n", HAL_RCC_GetSysClockFreq());
    printf("  HCLK: %lu Hz\r\n", HAL_RCC_GetHCLKFreq());
    printf("  PCLK1: %lu Hz\r\n", HAL_RCC_GetPCLK1Freq());
    printf("  PCLK2: %lu Hz\r\n", HAL_RCC_GetPCLK2Freq());
    
    // GPIO配置信息
    printf("\nGPIO配置:\r\n");
    printf("  PB12: LED指示灯\r\n");
    printf("  PB1: CAT1电源控制\r\n");
    printf("  PB10: CAT1复位控制\r\n");
    printf("  PC13: 按键输入\r\n");
    printf("  PA4: ADC电压检测\r\n");
    
    // 外设状态
    printf("\n外设状态:\r\n");
    printf("  RTC: %s\r\n", "已初始化");
    printf("  ADC: %s\r\n", "已初始化");
    printf("  LCD: %s\r\n", "已初始化");
    printf("  UART: %s\r\n", "已初始化");
    printf("  EEPROM: %s\r\n", "已初始化");
    
    // 运行状态
    printf("\n运行状态:\r\n");
    printf("  系统运行时间: %lu ms\r\n", HAL_GetTick());
    extern float vdda_voltage;
    if(vdda_voltage > 0) {
        printf("  电源电压: %.2f V\r\n", vdda_voltage);
    }
    
    printf("========== 诊断信息结束 ==========\r\n\n");
}
