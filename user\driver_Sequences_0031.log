/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : E:\project\source-application\user\driver_Sequences_0031.log
 *  Created     : 11:46:19 (29/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : D:/software/Keil/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : E:\project\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[11:46:19.579]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:46:19.579]  
[11:46:19.608]  <debugvars>
[11:46:19.636]    // Pre-defined
[11:46:19.657]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:46:19.686]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:46:19.687]    __dp=0x00000000
[11:46:19.687]    __ap=0x00000000
[11:46:19.687]    __traceout=0x00000000      (Trace Disabled)
[11:46:19.688]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:46:19.688]    __FlashAddr=0x00000000
[11:46:19.688]    __FlashLen=0x00000000
[11:46:19.689]    __FlashArg=0x00000000
[11:46:19.689]    __FlashOp=0x00000000
[11:46:19.689]    __Result=0x00000000
[11:46:19.690]    
[11:46:19.690]    // User-defined
[11:46:19.690]    DbgMCU_CR=0x00000007
[11:46:19.691]    DbgMCU_APB1_Fz=0x00000000
[11:46:19.692]    DbgMCU_APB2_Fz=0x00000000
[11:46:19.692]    DoOptionByteLoading=0x00000000
[11:46:19.693]  </debugvars>
[11:46:19.693]  
[11:46:19.693]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:46:19.694]    <block atomic="false" info="">
[11:46:19.694]      Sequence("CheckID");
[11:46:19.694]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:46:19.695]          <block atomic="false" info="">
[11:46:19.695]            __var pidr1 = 0;
[11:46:19.695]              // -> [pidr1 <= 0x00000000]
[11:46:19.696]            __var pidr2 = 0;
[11:46:19.696]              // -> [pidr2 <= 0x00000000]
[11:46:19.696]            __var jep106id = 0;
[11:46:19.697]              // -> [jep106id <= 0x00000000]
[11:46:19.697]            __var ROMTableBase = 0;
[11:46:19.697]              // -> [ROMTableBase <= 0x00000000]
[11:46:19.697]            __ap = 0;      // AHB-AP
[11:46:19.698]              // -> [__ap <= 0x00000000]
[11:46:19.698]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:46:19.703]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:46:19.703]              // -> [ROMTableBase <= 0xF0000000]
[11:46:19.703]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:46:19.711]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:46:19.712]              // -> [pidr1 <= 0x00000004]
[11:46:19.713]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:46:19.720]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:46:19.722]              // -> [pidr2 <= 0x0000000A]
[11:46:19.723]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:46:19.723]              // -> [jep106id <= 0x00000020]
[11:46:19.723]          </block>
[11:46:19.723]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:46:19.724]            // if-block "jep106id != 0x20"
[11:46:19.724]              // =>  FALSE
[11:46:19.724]            // skip if-block "jep106id != 0x20"
[11:46:19.725]          </control>
[11:46:19.725]        </sequence>
[11:46:19.725]    </block>
[11:46:19.726]  </sequence>
[11:46:19.726]  
[11:46:19.832]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:46:19.832]  
[11:46:19.834]  <debugvars>
[11:46:19.834]    // Pre-defined
[11:46:19.835]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:46:19.835]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:46:19.836]    __dp=0x00000000
[11:46:19.836]    __ap=0x00000000
[11:46:19.836]    __traceout=0x00000000      (Trace Disabled)
[11:46:19.837]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:46:19.837]    __FlashAddr=0x00000000
[11:46:19.837]    __FlashLen=0x00000000
[11:46:19.838]    __FlashArg=0x00000000
[11:46:19.838]    __FlashOp=0x00000000
[11:46:19.839]    __Result=0x00000000
[11:46:19.839]    
[11:46:19.839]    // User-defined
[11:46:19.839]    DbgMCU_CR=0x00000007
[11:46:19.840]    DbgMCU_APB1_Fz=0x00000000
[11:46:19.840]    DbgMCU_APB2_Fz=0x00000000
[11:46:19.840]    DoOptionByteLoading=0x00000000
[11:46:19.841]  </debugvars>
[11:46:19.841]  
[11:46:19.841]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:46:19.841]    <block atomic="false" info="">
[11:46:19.842]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:46:19.848]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:19.850]    </block>
[11:46:19.850]    <block atomic="false" info="DbgMCU registers">
[11:46:19.851]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:46:19.857]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:46:19.865]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:19.867]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:46:19.873]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:19.875]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:46:19.883]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:19.884]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:46:19.890]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:46:19.891]    </block>
[11:46:19.891]  </sequence>
[11:46:19.892]  
[11:46:36.608]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:46:36.608]  
[11:46:36.609]  <debugvars>
[11:46:36.610]    // Pre-defined
[11:46:36.611]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:46:36.611]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:46:36.612]    __dp=0x00000000
[11:46:36.612]    __ap=0x00000000
[11:46:36.613]    __traceout=0x00000000      (Trace Disabled)
[11:46:36.613]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:46:36.613]    __FlashAddr=0x00000000
[11:46:36.614]    __FlashLen=0x00000000
[11:46:36.614]    __FlashArg=0x00000000
[11:46:36.614]    __FlashOp=0x00000000
[11:46:36.615]    __Result=0x00000000
[11:46:36.615]    
[11:46:36.615]    // User-defined
[11:46:36.615]    DbgMCU_CR=0x00000007
[11:46:36.616]    DbgMCU_APB1_Fz=0x00000000
[11:46:36.616]    DbgMCU_APB2_Fz=0x00000000
[11:46:36.616]    DoOptionByteLoading=0x00000000
[11:46:36.617]  </debugvars>
[11:46:36.617]  
[11:46:36.617]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:46:36.618]    <block atomic="false" info="">
[11:46:36.618]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:46:36.618]        // -> [connectionFlash <= 0x00000001]
[11:46:36.618]      __var FLASH_BASE = 0x40022000 ;
[11:46:36.619]        // -> [FLASH_BASE <= 0x40022000]
[11:46:36.619]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:46:36.619]        // -> [FLASH_CR <= 0x40022004]
[11:46:36.621]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:46:36.621]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:46:36.621]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:46:36.622]        // -> [LOCK_BIT <= 0x00000001]
[11:46:36.622]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:46:36.622]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:46:36.622]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:46:36.623]        // -> [FLASH_KEYR <= 0x4002200C]
[11:46:36.623]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:46:36.623]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:46:36.624]      __var FLASH_KEY2 = 0x02030405 ;
[11:46:36.624]        // -> [FLASH_KEY2 <= 0x02030405]
[11:46:36.624]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:46:36.625]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:46:36.625]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:46:36.625]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:46:36.626]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:46:36.626]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:46:36.626]      __var FLASH_CR_Value = 0 ;
[11:46:36.626]        // -> [FLASH_CR_Value <= 0x00000000]
[11:46:36.627]      __var DoDebugPortStop = 1 ;
[11:46:36.627]        // -> [DoDebugPortStop <= 0x00000001]
[11:46:36.627]      __var DP_CTRL_STAT = 0x4 ;
[11:46:36.627]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:46:36.627]      __var DP_SELECT = 0x8 ;
[11:46:36.629]        // -> [DP_SELECT <= 0x00000008]
[11:46:36.629]    </block>
[11:46:36.629]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:46:36.630]      // if-block "connectionFlash && DoOptionByteLoading"
[11:46:36.630]        // =>  FALSE
[11:46:36.630]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:46:36.631]    </control>
[11:46:36.631]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:46:36.632]      // if-block "DoDebugPortStop"
[11:46:36.632]        // =>  TRUE
[11:46:36.632]      <block atomic="false" info="">
[11:46:36.633]        WriteDP(DP_SELECT, 0x00000000);
[11:46:36.643]  
[11:46:36.643]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:46:36.643]  
[11:46:36.646]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:46:36.647]      </block>
[11:46:36.647]      // end if-block "DoDebugPortStop"
[11:46:36.647]    </control>
[11:46:36.648]  </sequence>
[11:46:36.648]  
[11:48:32.593]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[11:48:32.593]  
[11:48:32.594]  <debugvars>
[11:48:32.595]    // Pre-defined
[11:48:32.595]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:48:32.595]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:48:32.596]    __dp=0x00000000
[11:48:32.596]    __ap=0x00000000
[11:48:32.596]    __traceout=0x00000000      (Trace Disabled)
[11:48:32.597]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:48:32.597]    __FlashAddr=0x00000000
[11:48:32.597]    __FlashLen=0x00000000
[11:48:32.598]    __FlashArg=0x00000000
[11:48:32.598]    __FlashOp=0x00000000
[11:48:32.598]    __Result=0x00000000
[11:48:32.599]    
[11:48:32.599]    // User-defined
[11:48:32.601]    DbgMCU_CR=0x00000007
[11:48:32.601]    DbgMCU_APB1_Fz=0x00000000
[11:48:32.602]    DbgMCU_APB2_Fz=0x00000000
[11:48:32.602]    DoOptionByteLoading=0x00000000
[11:48:32.602]  </debugvars>
[11:48:32.602]  
[11:48:32.603]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[11:48:32.603]    <block atomic="false" info="">
[11:48:32.603]      Sequence("CheckID");
[11:48:32.604]        <sequence name="CheckID" Pname="" disable="false" info="">
[11:48:32.604]          <block atomic="false" info="">
[11:48:32.604]            __var pidr1 = 0;
[11:48:32.604]              // -> [pidr1 <= 0x00000000]
[11:48:32.605]            __var pidr2 = 0;
[11:48:32.605]              // -> [pidr2 <= 0x00000000]
[11:48:32.605]            __var jep106id = 0;
[11:48:32.606]              // -> [jep106id <= 0x00000000]
[11:48:32.606]            __var ROMTableBase = 0;
[11:48:32.606]              // -> [ROMTableBase <= 0x00000000]
[11:48:32.606]            __ap = 0;      // AHB-AP
[11:48:32.607]              // -> [__ap <= 0x00000000]
[11:48:32.607]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[11:48:32.612]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[11:48:32.612]              // -> [ROMTableBase <= 0xF0000000]
[11:48:32.613]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[11:48:32.620]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[11:48:32.622]              // -> [pidr1 <= 0x00000004]
[11:48:32.622]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[11:48:32.630]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[11:48:32.631]              // -> [pidr2 <= 0x0000000A]
[11:48:32.632]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[11:48:32.633]              // -> [jep106id <= 0x00000020]
[11:48:32.633]          </block>
[11:48:32.633]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[11:48:32.634]            // if-block "jep106id != 0x20"
[11:48:32.634]              // =>  FALSE
[11:48:32.634]            // skip if-block "jep106id != 0x20"
[11:48:32.635]          </control>
[11:48:32.635]        </sequence>
[11:48:32.636]    </block>
[11:48:32.636]  </sequence>
[11:48:32.636]  
[11:48:32.742]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[11:48:32.742]  
[11:48:32.743]  <debugvars>
[11:48:32.744]    // Pre-defined
[11:48:32.744]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:48:32.745]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:48:32.745]    __dp=0x00000000
[11:48:32.745]    __ap=0x00000000
[11:48:32.746]    __traceout=0x00000000      (Trace Disabled)
[11:48:32.746]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:48:32.747]    __FlashAddr=0x00000000
[11:48:32.747]    __FlashLen=0x00000000
[11:48:32.748]    __FlashArg=0x00000000
[11:48:32.748]    __FlashOp=0x00000000
[11:48:32.748]    __Result=0x00000000
[11:48:32.749]    
[11:48:32.749]    // User-defined
[11:48:32.749]    DbgMCU_CR=0x00000007
[11:48:32.750]    DbgMCU_APB1_Fz=0x00000000
[11:48:32.750]    DbgMCU_APB2_Fz=0x00000000
[11:48:32.750]    DoOptionByteLoading=0x00000000
[11:48:32.751]  </debugvars>
[11:48:32.751]  
[11:48:32.752]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[11:48:32.752]    <block atomic="false" info="">
[11:48:32.752]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[11:48:32.759]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[11:48:32.760]    </block>
[11:48:32.761]    <block atomic="false" info="DbgMCU registers">
[11:48:32.761]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[11:48:32.768]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[11:48:32.776]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[11:48:32.777]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[11:48:32.782]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[11:48:32.783]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[11:48:32.790]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:48:32.792]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[11:48:32.797]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[11:48:32.798]    </block>
[11:48:32.798]  </sequence>
[11:48:32.798]  
[11:48:50.201]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[11:48:50.201]  
[11:48:50.203]  <debugvars>
[11:48:50.203]    // Pre-defined
[11:48:50.204]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[11:48:50.204]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[11:48:50.204]    __dp=0x00000000
[11:48:50.205]    __ap=0x00000000
[11:48:50.205]    __traceout=0x00000000      (Trace Disabled)
[11:48:50.206]    __errorcontrol=0x00000000  (Skip Errors="False")
[11:48:50.206]    __FlashAddr=0x00000000
[11:48:50.206]    __FlashLen=0x00000000
[11:48:50.206]    __FlashArg=0x00000000
[11:48:50.207]    __FlashOp=0x00000000
[11:48:50.207]    __Result=0x00000000
[11:48:50.207]    
[11:48:50.207]    // User-defined
[11:48:50.208]    DbgMCU_CR=0x00000007
[11:48:50.208]    DbgMCU_APB1_Fz=0x00000000
[11:48:50.208]    DbgMCU_APB2_Fz=0x00000000
[11:48:50.208]    DoOptionByteLoading=0x00000000
[11:48:50.209]  </debugvars>
[11:48:50.209]  
[11:48:50.209]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[11:48:50.210]    <block atomic="false" info="">
[11:48:50.210]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[11:48:50.210]        // -> [connectionFlash <= 0x00000001]
[11:48:50.211]      __var FLASH_BASE = 0x40022000 ;
[11:48:50.211]        // -> [FLASH_BASE <= 0x40022000]
[11:48:50.212]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[11:48:50.212]        // -> [FLASH_CR <= 0x40022004]
[11:48:50.212]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[11:48:50.213]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[11:48:50.213]      __var LOCK_BIT = ( 1 << 0 ) ;
[11:48:50.213]        // -> [LOCK_BIT <= 0x00000001]
[11:48:50.213]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[11:48:50.214]        // -> [OPTLOCK_BIT <= 0x00000004]
[11:48:50.214]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[11:48:50.214]        // -> [FLASH_KEYR <= 0x4002200C]
[11:48:50.215]      __var FLASH_KEY1 = 0x89ABCDEF ;
[11:48:50.215]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[11:48:50.215]      __var FLASH_KEY2 = 0x02030405 ;
[11:48:50.216]        // -> [FLASH_KEY2 <= 0x02030405]
[11:48:50.216]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[11:48:50.216]        // -> [FLASH_OPTKEYR <= 0x40022014]
[11:48:50.216]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[11:48:50.217]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[11:48:50.217]      __var FLASH_OPTKEY2 = 0x24252627 ;
[11:48:50.217]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[11:48:50.218]      __var FLASH_CR_Value = 0 ;
[11:48:50.218]        // -> [FLASH_CR_Value <= 0x00000000]
[11:48:50.218]      __var DoDebugPortStop = 1 ;
[11:48:50.219]        // -> [DoDebugPortStop <= 0x00000001]
[11:48:50.219]      __var DP_CTRL_STAT = 0x4 ;
[11:48:50.219]        // -> [DP_CTRL_STAT <= 0x00000004]
[11:48:50.220]      __var DP_SELECT = 0x8 ;
[11:48:50.220]        // -> [DP_SELECT <= 0x00000008]
[11:48:50.220]    </block>
[11:48:50.221]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[11:48:50.221]      // if-block "connectionFlash && DoOptionByteLoading"
[11:48:50.221]        // =>  FALSE
[11:48:50.222]      // skip if-block "connectionFlash && DoOptionByteLoading"
[11:48:50.222]    </control>
[11:48:50.222]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[11:48:50.223]      // if-block "DoDebugPortStop"
[11:48:50.223]        // =>  TRUE
[11:48:50.224]      <block atomic="false" info="">
[11:48:50.224]        WriteDP(DP_SELECT, 0x00000000);
[11:48:50.257]  
[11:48:50.257]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[11:48:50.257]  
[11:48:50.261]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[11:48:50.262]      </block>
[11:48:50.262]      // end if-block "DoDebugPortStop"
[11:48:50.263]    </control>
[11:48:50.263]  </sequence>
[11:48:50.264]  
