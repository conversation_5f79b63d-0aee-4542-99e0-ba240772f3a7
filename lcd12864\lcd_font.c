/**
 * （一）UTF - 8 的编码规则：
单字节字符（与 ASCII 兼容） ：对于码点在 0 - 127 之间的字符（也就是 ASCII 码中的字符），
UTF - 8 用一个字节表示，且字节的最高位为 0。例如，字符 'A' 的 ASCII 码是 65，在 UTF - 8 中同样表示为 01000001。


多字节字符：
对于码点大于 127 的字符，UTF - 8 采用多个字节来表示。具体规则如下：

2 字节： 第一个字节的前两位是 110，后面六位是码点的一部分；第二个字节的前两位是 10，后面六位是码点的另一部分。
3 字节： 第一个字节的前三位是 1110，后面五位是码点的一部分；第二个字节的前两位是 10，后面六位是码点的一部分；第三个字节的前两位是 10，后面六位是码点的一部分。
4 字节： 第一个字节的前四位是 11110，后面四位是码点的一部分；后面三个字节的前两位都是 10，后面六位是码点的一部分

MDK5采用UTF-8，提示很多个这样的error:#8：missing closing quote 的错误信息
在KEIL中Options for Target 'Flash' -> C/C++ -> Misc Controls添加“--locale=english”。
 * */
#include "lcd_font.h"
#include <string.h>

//  !(0) "(1) #(2) $(3) %(4) &(5) '(6) ((7) )(8) *(9) +(10) ,(11) -(12) .(13) /(14) 0(15) 1(16) 2(17) 3(18) 4(19)
//  5(20) 6(21) 7(22) 8(23) 9(24) :(25) ;(26) <(27) =(28) >(29) ?(30) @(31) A(32) B(33) C(34) D(35) E(36) F(37) G(38) H(39)
//  I(40) J(41) K(42) L(43) M(44) N(45) O(46) P(47) Q(48) R(49) S(50) T(51) U(52) V(53) W(54) X(55) Y(56) Z(57) [(58) \(59)
//  ](60) ^(61) _(62) `(63) a(64) b(65) c(66) d(67) e(68) f(69) g(70) h(71) i(72) j(73) k(74) l(75) m(76) n(77) o(78) p(79)
//  q(80) r(81) s(82) t(83) u(84) v(85) w(86) x(87) y(88) z(89) {(90) |(91) }(92) ~(93)
//   (0) !(1) "(2) #(3) $(4) %(5) &(6) '(7) ((8) )(9) *(10) +(11) ,(12) -(13) .(14) /(15) 0(16) 1(17) 2(18) 3(19)
//  4(20) 5(21) 6(22) 7(23) 8(24) 9(25) :(26) ;(27) <(28) =(29) >(30) ?(31) @(32) A(33) B(34) C(35) D(36) E(37) F(38) G(39)
//  H(40) I(41) J(42) K(43) L(44) M(45) N(46) O(47) P(48) Q(49) R(50) S(51) T(52) U(53) V(54) W(55) X(56) Y(57) Z(58) [(59)
//  \(60) ](61) ^(62) _(63) `(64) a(65) b(66) c(67) d(68) e(69) f(70) g(71) h(72) i(73) j(74) k(75) l(76) m(77) n(78) o(79)
//  p(80) q(81) r(82) s(83) t(84) u(85) v(86) w(87) x(88) y(89) z(90) {(91) |(92) }(93) ~(94)

/**
 * @brief 8x8 ASCII 字体数据
 */
static const uint8_t ascii_08_08[][8] = {
{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},/*" ",0*/
{0x04,0x04,0x04,0x04,0x04,0x00,0x04,0x00},/*"!",1*/
{0x0A,0x0A,0x00,0x00,0x00,0x00,0x00,0x00},/*""",2*/
{0x0A,0x0A,0x1F,0x0A,0x1F,0x0A,0x0A,0x00},/*"#",3*/
{0x08,0x1C,0x0A,0x0E,0x14,0x14,0x0E,0x00},/*"$",4*/
{0x00,0x16,0x16,0x08,0x04,0x1A,0x1A,0x00},/*"%",5*/
{0x0C,0x14,0x0C,0x0E,0x12,0x12,0x0C,0x00},/*"&",6*/
{0x08,0x08,0x00,0x00,0x00,0x00,0x00,0x00},/*"'",7*/
{0x08,0x04,0x04,0x04,0x04,0x04,0x08,0x00},/*"(",8*/
{0x04,0x08,0x08,0x08,0x08,0x08,0x04,0x00},/*")",9*/
{0x00,0x04,0x0E,0x0E,0x0E,0x04,0x00,0x00},/*"*",10*/
{0x00,0x04,0x04,0x0E,0x04,0x04,0x00,0x00},/*"+",11*/
{0x00,0x00,0x00,0x00,0x00,0x08,0x08,0x00},/*",",12*/
{0x00,0x00,0x00,0x1E,0x00,0x00,0x00,0x00},/*"-",13*/
{0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x00},/*".",14*/
{0x10,0x10,0x08,0x08,0x04,0x04,0x02,0x00},/*"/",15*/
{0x0C,0x12,0x12,0x12,0x12,0x12,0x0C,0x00},/*"0",16*/
{0x04,0x06,0x04,0x04,0x04,0x04,0x0E,0x00},/*"1",17*/
{0x0C,0x12,0x10,0x08,0x04,0x02,0x1E,0x00},/*"2",18*/
{0x0C,0x12,0x10,0x0C,0x10,0x12,0x0C,0x00},/*"3",19*/
{0x08,0x0C,0x0C,0x0A,0x0A,0x1E,0x08,0x00},/*"4",20*/
{0x1E,0x02,0x02,0x0E,0x10,0x10,0x0E,0x00},/*"5",21*/
{0x0C,0x12,0x02,0x0E,0x12,0x12,0x0C,0x00},/*"6",22*/
{0x1E,0x12,0x10,0x08,0x04,0x04,0x04,0x00},/*"7",23*/
{0x0C,0x12,0x12,0x0C,0x12,0x12,0x0C,0x00},/*"8",24*/
{0x0C,0x12,0x12,0x1C,0x10,0x12,0x0C,0x00},/*"9",25*/
{0x00,0x00,0x04,0x00,0x00,0x04,0x00,0x00},/*":",26*/
{0x00,0x00,0x04,0x00,0x00,0x04,0x02,0x00},/*";",27*/
{0x10,0x08,0x04,0x02,0x04,0x08,0x10,0x00},/*"<",28*/
{0x00,0x00,0x1E,0x00,0x1E,0x00,0x00,0x00},/*"=",29*/
{0x02,0x04,0x08,0x10,0x08,0x04,0x02,0x00},/*">",30*/
{0x0C,0x12,0x10,0x08,0x04,0x00,0x04,0x00},/*"?",31*/
{0x0C,0x12,0x12,0x1A,0x14,0x14,0x1C,0x00},/*"@",32*/
{0x0C,0x12,0x12,0x12,0x1E,0x12,0x12,0x00},/*"A",33*/
{0x0E,0x12,0x12,0x0E,0x12,0x12,0x0E,0x00},/*"B",34*/
{0x0C,0x12,0x02,0x02,0x02,0x12,0x0C,0x00},/*"C",35*/
{0x0E,0x12,0x12,0x12,0x12,0x12,0x0E,0x00},/*"D",36*/
{0x1E,0x02,0x02,0x0E,0x02,0x02,0x1E,0x00},/*"E",37*/
{0x1E,0x02,0x02,0x0E,0x02,0x02,0x02,0x00},/*"F",38*/
{0x0C,0x12,0x02,0x1A,0x12,0x12,0x0C,0x00},/*"G",39*/
{0x12,0x12,0x12,0x1E,0x12,0x12,0x12,0x00},/*"H",40*/
{0x0E,0x04,0x04,0x04,0x04,0x04,0x0E,0x00},/*"I",41*/
{0x10,0x10,0x10,0x10,0x12,0x12,0x0C,0x00},/*"J",42*/
{0x12,0x12,0x0A,0x06,0x0A,0x12,0x12,0x00},/*"K",43*/
{0x02,0x02,0x02,0x02,0x02,0x02,0x1E,0x00},/*"L",44*/
{0x12,0x1E,0x1E,0x12,0x12,0x12,0x12,0x00},/*"M",45*/
{0x12,0x16,0x16,0x1A,0x1A,0x12,0x12,0x00},/*"N",46*/
{0x1E,0x12,0x12,0x12,0x12,0x12,0x1E,0x00},/*"O",47*/
{0x0E,0x12,0x12,0x0E,0x02,0x02,0x02,0x00},/*"P",48*/
{0x0C,0x12,0x12,0x12,0x16,0x1A,0x1C,0x00},/*"Q",49*/
{0x0E,0x12,0x12,0x0E,0x06,0x0A,0x12,0x00},/*"R",50*/
{0x0C,0x12,0x02,0x0C,0x10,0x12,0x0C,0x00},/*"S",51*/
{0x1E,0x04,0x04,0x04,0x04,0x04,0x04,0x00},/*"T",52*/
{0x12,0x12,0x12,0x12,0x12,0x12,0x0C,0x00},/*"U",53*/
{0x12,0x12,0x12,0x12,0x0C,0x0C,0x0C,0x00},/*"V",54*/
{0x12,0x12,0x12,0x12,0x1E,0x1E,0x12,0x00},/*"W",55*/
{0x12,0x12,0x0C,0x0C,0x0C,0x12,0x12,0x00},/*"X",56*/
{0x11,0x11,0x0A,0x04,0x04,0x04,0x04,0x00},/*"Y",57*/
{0x1E,0x10,0x08,0x0C,0x04,0x02,0x1E,0x00},/*"Z",58*/
{0x0C,0x04,0x04,0x04,0x04,0x04,0x0C,0x00},/*"[",59*/
{0x01,0x01,0x02,0x02,0x04,0x04,0x08,0x00},/*"\",60*/
{0x0C,0x08,0x08,0x08,0x08,0x08,0x0C,0x00},/*"]",61*/
{0x04,0x0A,0x00,0x00,0x00,0x00,0x00,0x00},/*"^",62*/
{0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00},/*"_",63*/
{0x04,0x04,0x00,0x00,0x00,0x00,0x00,0x00},/*"`",64*/
{0x00,0x00,0x00,0x1C,0x1C,0x12,0x1C,0x00},/*"a",65*/
{0x02,0x02,0x02,0x0E,0x12,0x12,0x0E,0x00},/*"b",66*/
{0x00,0x00,0x00,0x1C,0x02,0x02,0x1C,0x00},/*"c",67*/
{0x10,0x10,0x10,0x1C,0x12,0x12,0x1C,0x00},/*"d",68*/
{0x00,0x00,0x00,0x0C,0x1E,0x02,0x0C,0x00},/*"e",69*/
{0x00,0x08,0x04,0x1E,0x04,0x04,0x04,0x00},/*"f",70*/
{0x00,0x00,0x00,0x1C,0x12,0x1C,0x10,0x0C},/*"g",71*/
{0x02,0x02,0x02,0x0E,0x0A,0x0A,0x0A,0x00},/*"h",72*/
{0x00,0x00,0x04,0x00,0x04,0x04,0x04,0x00},/*"i",73*/
{0x00,0x00,0x08,0x00,0x08,0x08,0x08,0x06},/*"j",74*/
{0x02,0x02,0x02,0x1A,0x06,0x0A,0x12,0x00},/*"k",75*/
{0x04,0x04,0x04,0x04,0x04,0x04,0x0E,0x00},/*"l",76*/
{0x00,0x00,0x00,0x1E,0x1A,0x1A,0x1A,0x00},/*"m",77*/
{0x00,0x00,0x00,0x0E,0x12,0x12,0x12,0x00},/*"n",78*/
{0x00,0x00,0x00,0x0C,0x12,0x12,0x0C,0x00},/*"o",79*/
{0x00,0x00,0x00,0x0E,0x12,0x12,0x0E,0x02},/*"p",80*/
{0x00,0x00,0x00,0x1C,0x12,0x12,0x1C,0x10},/*"q",81*/
{0x00,0x00,0x00,0x1A,0x06,0x02,0x02,0x00},/*"r",82*/
{0x00,0x00,0x00,0x1C,0x06,0x18,0x0E,0x00},/*"s",83*/
{0x00,0x00,0x04,0x0E,0x04,0x04,0x0C,0x00},/*"t",84*/
{0x00,0x00,0x00,0x12,0x12,0x12,0x1C,0x00},/*"u",85*/
{0x00,0x00,0x00,0x12,0x12,0x0C,0x0C,0x00},/*"v",86*/
{0x00,0x00,0x00,0x15,0x15,0x0E,0x0A,0x00},/*"w",87*/
{0x00,0x00,0x00,0x12,0x0C,0x0C,0x12,0x00},/*"x",88*/
{0x00,0x00,0x00,0x12,0x12,0x1C,0x10,0x0C},/*"y",89*/
{0x00,0x00,0x00,0x1E,0x08,0x04,0x1E,0x00},/*"z",90*/
{0x0C,0x04,0x04,0x02,0x04,0x04,0x0C,0x00},/*"{",91*/
{0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x00},/*"|",92*/
{0x06,0x04,0x04,0x08,0x04,0x04,0x06,0x00},/*"}",93*/
{0x14,0x0A,0x00,0x00,0x00,0x00,0x00,0x00},/*"~",94*/
};



/**
 * @brief 16x8 ASCII 字体数据
 */
static const uint8_t ascii_16_08[][16] = {
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* " ",0 */
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0x33, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* "!",1 */
    {0x00, 0x00, 0x10, 0x00, 0x0C, 0x00, 0x02, 0x00, 0x10, 0x00, 0x0C, 0x00, 0x02, 0x00, 0x00, 0x00}, /* """,2 */
    {0x00, 0x00, 0x40, 0x04, 0xC0, 0x3F, 0x78, 0x04, 0x40, 0x04, 0xC0, 0x3F, 0x78, 0x04, 0x00, 0x00}, /* "#",3 */
    {0x00, 0x00, 0x70, 0x18, 0x88, 0x20, 0x88, 0x20, 0xFC, 0xFF, 0x08, 0x21, 0x30, 0x1E, 0x00, 0x00}, /* "$",4 */
    {0xF0, 0x00, 0x08, 0x31, 0xF0, 0x0C, 0x80, 0x03, 0x60, 0x1E, 0x18, 0x21, 0x00, 0x1E, 0x00, 0x00}, /* "%",5 */
    {0x00, 0x1E, 0xF0, 0x21, 0x08, 0x23, 0x88, 0x2C, 0x70, 0x19, 0x00, 0x27, 0x00, 0x21, 0x00, 0x10}, /* "&",6 */
    {0x00, 0x00, 0x12, 0x00, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* "'",7 */
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x07, 0x18, 0x18, 0x04, 0x20, 0x02, 0x40, 0x00, 0x00}, /* "(",8 */
    {0x00, 0x00, 0x02, 0x40, 0x04, 0x20, 0x18, 0x18, 0xE0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* ")",9 */
    {0x40, 0x02, 0x40, 0x02, 0x80, 0x01, 0xF0, 0x0F, 0x80, 0x01, 0x40, 0x02, 0x40, 0x02, 0x00, 0x00}, /* "*",10 */
    {0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x01, 0xE0, 0x0F, 0x00, 0x01, 0x00, 0x01, 0x00, 0x01}, /* "+",11 */
    {0x00, 0x00, 0x00, 0x90, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* ",",12 */
    {0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00}, /* "-",13 */
    {0x00, 0x00, 0x00, 0x30, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* ".",14 */
    {0x00, 0x00, 0x00, 0x60, 0x00, 0x18, 0x00, 0x07, 0xC0, 0x00, 0x38, 0x00, 0x04, 0x00, 0x00, 0x00}, /* "/",15 */
    {0x00, 0x00, 0xE0, 0x0F, 0x10, 0x10, 0x08, 0x20, 0x08, 0x20, 0x10, 0x10, 0xE0, 0x0F, 0x00, 0x00}, /* "0",16 */
    {0x00, 0x00, 0x00, 0x00, 0x10, 0x20, 0x10, 0x20, 0xF8, 0x3F, 0x00, 0x20, 0x00, 0x20, 0x00, 0x00}, /* "1",17 */
    {0x00, 0x00, 0x70, 0x30, 0x08, 0x28, 0x08, 0x24, 0x08, 0x22, 0x08, 0x21, 0xF0, 0x30, 0x00, 0x00}, /* "2",18 */
    {0x00, 0x00, 0x30, 0x18, 0x08, 0x20, 0x08, 0x21, 0x08, 0x21, 0x88, 0x22, 0x70, 0x1C, 0x00, 0x00}, /* "3",19 */
    {0x00, 0x00, 0x00, 0x06, 0x80, 0x05, 0x40, 0x24, 0x30, 0x24, 0xF8, 0x3F, 0x00, 0x24, 0x00, 0x24}, /* "4",20 */
    {0x00, 0x00, 0xF8, 0x19, 0x88, 0x20, 0x88, 0x20, 0x88, 0x20, 0x08, 0x11, 0x08, 0x0E, 0x00, 0x00}, /* "5",21 */
    {0x00, 0x00, 0xE0, 0x0F, 0x10, 0x11, 0x88, 0x20, 0x88, 0x20, 0x90, 0x20, 0x00, 0x1F, 0x00, 0x00}, /* "6",22 */
    {0x00, 0x00, 0x18, 0x00, 0x08, 0x00, 0x08, 0x3E, 0x88, 0x01, 0x68, 0x00, 0x18, 0x00, 0x00, 0x00}, /* "7",23 */
    {0x00, 0x00, 0x70, 0x1C, 0x88, 0x22, 0x08, 0x21, 0x08, 0x21, 0x88, 0x22, 0x70, 0x1C, 0x00, 0x00}, /* "8",24 */
    {0x00, 0x00, 0xF0, 0x01, 0x08, 0x12, 0x08, 0x22, 0x08, 0x22, 0x10, 0x11, 0xE0, 0x0F, 0x00, 0x00}, /* "9",25 */
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x30, 0xC0, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* ":",26 */
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /* ";",27 */
    {0x00, 0x00, 0x00, 0x01, 0x80, 0x02, 0x40, 0x04, 0x20, 0x08, 0x10, 0x10, 0x08, 0x20, 0x00, 0x00}, /* "<",28 */
    {0x00, 0x00, 0x40, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x02, 0x00, 0x00}, /* "=",29 */
    {0x00, 0x00, 0x08, 0x20, 0x10, 0x10, 0x20, 0x08, 0x40, 0x04, 0x80, 0x02, 0x00, 0x01, 0x00, 0x00}, /* ">",30 */
    {0x00, 0x00, 0x70, 0x00, 0x48, 0x00, 0x08, 0x30, 0x08, 0x37, 0x88, 0x00, 0x70, 0x00, 0x00, 0x00}, /*"?",31*/
    {0xC0, 0x07, 0x30, 0x18, 0xC8, 0x27, 0x28, 0x28, 0xE8, 0x2F, 0x10, 0x28, 0xE0, 0x17, 0x00, 0x00}, /*"@",32*/
    {0x00, 0x20, 0x00, 0x3C, 0xC0, 0x23, 0x38, 0x02, 0xE0, 0x02, 0x00, 0x27, 0x00, 0x38, 0x00, 0x20}, /*"A",33*/
    {0x08, 0x20, 0xF8, 0x3F, 0x88, 0x20, 0x88, 0x20, 0x88, 0x20, 0x70, 0x11, 0x00, 0x0E, 0x00, 0x00}, /*"B",34*/
    {0xC0, 0x07, 0x30, 0x18, 0x08, 0x20, 0x08, 0x20, 0x08, 0x20, 0x08, 0x10, 0x38, 0x08, 0x00, 0x00}, /*"C",35*/
    {0x08, 0x20, 0xF8, 0x3F, 0x08, 0x20, 0x08, 0x20, 0x08, 0x20, 0x10, 0x10, 0xE0, 0x0F, 0x00, 0x00}, /*"D",36*/
    {0x08, 0x20, 0xF8, 0x3F, 0x88, 0x20, 0x88, 0x20, 0xE8, 0x23, 0x08, 0x20, 0x10, 0x18, 0x00, 0x00}, /*"E",37*/
    {0x08, 0x20, 0xF8, 0x3F, 0x88, 0x20, 0x88, 0x00, 0xE8, 0x03, 0x08, 0x00, 0x10, 0x00, 0x00, 0x00}, /*"F",38*/
    {0xC0, 0x07, 0x30, 0x18, 0x08, 0x20, 0x08, 0x20, 0x08, 0x22, 0x38, 0x1E, 0x00, 0x02, 0x00, 0x00}, /*"G",39*/
    {0x08, 0x20, 0xF8, 0x3F, 0x08, 0x21, 0x00, 0x01, 0x00, 0x01, 0x08, 0x21, 0xF8, 0x3F, 0x08, 0x20}, /*"H",40*/
    {0x00, 0x00, 0x08, 0x20, 0x08, 0x20, 0xF8, 0x3F, 0x08, 0x20, 0x08, 0x20, 0x00, 0x00, 0x00, 0x00}, /*"I",41*/
    {0x00, 0xC0, 0x00, 0x80, 0x08, 0x80, 0x08, 0x80, 0xF8, 0x7F, 0x08, 0x00, 0x08, 0x00, 0x00, 0x00}, /*"J",42*/
    {0x08, 0x20, 0xF8, 0x3F, 0x88, 0x20, 0xC0, 0x01, 0x28, 0x26, 0x18, 0x38, 0x08, 0x20, 0x00, 0x00}, /*"K",43*/
    {0x08, 0x20, 0xF8, 0x3F, 0x08, 0x20, 0x00, 0x20, 0x00, 0x20, 0x00, 0x20, 0x00, 0x30, 0x00, 0x00}, /*"L",44*/
    {0x08, 0x20, 0xF8, 0x3F, 0xF8, 0x01, 0x00, 0x3E, 0xF8, 0x01, 0xF8, 0x3F, 0x08, 0x20, 0x00, 0x00}, /*"M",45*/
    {0x08, 0x20, 0xF8, 0x3F, 0x30, 0x20, 0xC0, 0x00, 0x00, 0x07, 0x08, 0x18, 0xF8, 0x3F, 0x08, 0x00}, /*"N",46*/
    {0xE0, 0x0F, 0x10, 0x10, 0x08, 0x20, 0x08, 0x20, 0x08, 0x20, 0x10, 0x10, 0xE0, 0x0F, 0x00, 0x00}, /*"O",47*/
    {0x08, 0x20, 0xF8, 0x3F, 0x08, 0x21, 0x08, 0x01, 0x08, 0x01, 0x08, 0x01, 0xF0, 0x00, 0x00, 0x00}, /*"P",48*/
    {0xE0, 0x0F, 0x10, 0x10, 0x08, 0x28, 0x08, 0x28, 0x08, 0x30, 0x10, 0x50, 0xE0, 0x4F, 0x00, 0x00}, /*"Q",49*/
    {0x08, 0x20, 0xF8, 0x3F, 0x88, 0x20, 0x88, 0x00, 0x88, 0x03, 0x88, 0x0C, 0x70, 0x30, 0x00, 0x20}, /*"R",50*/
    {0x00, 0x00, 0x70, 0x38, 0x88, 0x20, 0x08, 0x21, 0x08, 0x21, 0x08, 0x22, 0x38, 0x1C, 0x00, 0x00}, /*"S",51*/
    {0x18, 0x00, 0x08, 0x00, 0x08, 0x20, 0xF8, 0x3F, 0x08, 0x20, 0x08, 0x00, 0x18, 0x00, 0x00, 0x00}, /*"T",52*/
    {0x08, 0x00, 0xF8, 0x1F, 0x08, 0x20, 0x00, 0x20, 0x00, 0x20, 0x08, 0x20, 0xF8, 0x1F, 0x08, 0x00}, /*"U",53*/
    {0x08, 0x00, 0x78, 0x00, 0x88, 0x07, 0x00, 0x38, 0x00, 0x0E, 0xC8, 0x01, 0x38, 0x00, 0x08, 0x00}, /*"V",54*/
    {0x08, 0x00, 0xF8, 0x03, 0x00, 0x3E, 0xF8, 0x01, 0x00, 0x3E, 0xF8, 0x03, 0x08, 0x00, 0x00, 0x00}, /*"W",55*/
    {0x08, 0x20, 0x18, 0x30, 0x68, 0x2C, 0x80, 0x03, 0x80, 0x03, 0x68, 0x2C, 0x18, 0x30, 0x08, 0x20}, /*"X",56*/
    {0x08, 0x00, 0x38, 0x00, 0xC8, 0x20, 0x00, 0x3F, 0xC8, 0x20, 0x38, 0x00, 0x08, 0x00, 0x00, 0x00}, /*"Y",57*/
    {0x10, 0x20, 0x08, 0x38, 0x08, 0x26, 0x08, 0x21, 0xC8, 0x20, 0x38, 0x20, 0x08, 0x18, 0x00, 0x00}, /*"Z",58*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE, 0x7F, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x00, 0x00}, /*"[",59*/
    {0x00, 0x00, 0x04, 0x00, 0x38, 0x00, 0xC0, 0x01, 0x00, 0x06, 0x00, 0x38, 0x00, 0xC0, 0x00, 0x00}, /*"\",60*/
    {0x00, 0x00, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0xFE, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"]",61*/
    {0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x02, 0x00, 0x02, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"^",62*/
    {0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80}, /*"_",63*/
    {0x00, 0x00, 0x02, 0x00, 0x02, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"`",64*/
    {0x00, 0x00, 0x00, 0x19, 0x80, 0x24, 0x80, 0x24, 0x80, 0x12, 0x00, 0x3F, 0x00, 0x20, 0x00, 0x00}, /*"a",65*/
    {0x10, 0x00, 0xF0, 0x3F, 0x00, 0x11, 0x80, 0x20, 0x80, 0x20, 0x00, 0x11, 0x00, 0x0E, 0x00, 0x00}, /*"b",66*/
    {0x00, 0x00, 0x00, 0x0E, 0x00, 0x11, 0x80, 0x20, 0x80, 0x20, 0x80, 0x20, 0x00, 0x11, 0x00, 0x00}, /*"c",67*/
    {0x00, 0x00, 0x00, 0x1F, 0x80, 0x20, 0x80, 0x20, 0x80, 0x20, 0x90, 0x10, 0xF0, 0x3F, 0x00, 0x20}, /*"d",68*/
    {0x00, 0x00, 0x00, 0x1F, 0x80, 0x24, 0x80, 0x24, 0x80, 0x24, 0x80, 0x24, 0x00, 0x17, 0x00, 0x00}, /*"e",69*/
    {0x00, 0x00, 0x80, 0x20, 0x80, 0x20, 0xE0, 0x3F, 0x90, 0x20, 0x90, 0x20, 0x20, 0x00, 0x00, 0x00}, /*"f",70*/
    {0x00, 0x00, 0x00, 0x6B, 0x80, 0x94, 0x80, 0x94, 0x80, 0x94, 0x80, 0x93, 0x80, 0x60, 0x00, 0x00}, /*"g",71*/
    {0x10, 0x20, 0xF0, 0x3F, 0x00, 0x21, 0x80, 0x00, 0x80, 0x00, 0x80, 0x20, 0x00, 0x3F, 0x00, 0x20}, /*"h",72*/
    {0x00, 0x00, 0x80, 0x20, 0x98, 0x20, 0x98, 0x3F, 0x00, 0x20, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00}, /*"i",73*/
    {0x00, 0x00, 0x00, 0xC0, 0x00, 0x80, 0x80, 0x80, 0x98, 0x80, 0x98, 0x7F, 0x00, 0x00, 0x00, 0x00}, /*"j",74*/
    {0x10, 0x20, 0xF0, 0x3F, 0x00, 0x24, 0x00, 0x06, 0x80, 0x29, 0x80, 0x30, 0x80, 0x20, 0x00, 0x00}, /*"k",75*/
    {0x00, 0x00, 0x10, 0x20, 0x10, 0x20, 0xF8, 0x3F, 0x00, 0x20, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00}, /*"l",76*/
    {0x80, 0x20, 0x80, 0x3F, 0x80, 0x20, 0x80, 0x00, 0x80, 0x3F, 0x80, 0x20, 0x80, 0x00, 0x00, 0x3F}, /*"m",77*/
    {0x80, 0x20, 0x80, 0x3F, 0x00, 0x21, 0x80, 0x00, 0x80, 0x00, 0x80, 0x20, 0x00, 0x3F, 0x00, 0x20}, /*"n",78*/
    {0x00, 0x00, 0x00, 0x1F, 0x80, 0x20, 0x80, 0x20, 0x80, 0x20, 0x80, 0x20, 0x00, 0x1F, 0x00, 0x00}, /*"o",79*/
    {0x80, 0x80, 0x80, 0xFF, 0x00, 0x91, 0x80, 0x20, 0x80, 0x20, 0x00, 0x11, 0x00, 0x0E, 0x00, 0x00}, /*"p",80*/
    {0x00, 0x00, 0x00, 0x0E, 0x00, 0x11, 0x80, 0x20, 0x80, 0x20, 0x00, 0x91, 0x80, 0xFF, 0x00, 0x80}, /*"q",81*/
    {0x80, 0x20, 0x80, 0x20, 0x80, 0x3F, 0x00, 0x21, 0x80, 0x20, 0x80, 0x00, 0x80, 0x01, 0x00, 0x00}, /*"r",82*/
    {0x00, 0x00, 0x00, 0x33, 0x80, 0x24, 0x80, 0x24, 0x80, 0x24, 0x80, 0x24, 0x80, 0x19, 0x00, 0x00}, /*"s",83*/
    {0x00, 0x00, 0x80, 0x00, 0x80, 0x00, 0xE0, 0x1F, 0x80, 0x20, 0x80, 0x20, 0x00, 0x10, 0x00, 0x00}, /*"t",84*/
    {0x80, 0x00, 0x80, 0x1F, 0x00, 0x20, 0x00, 0x20, 0x00, 0x20, 0x80, 0x10, 0x80, 0x3F, 0x00, 0x20}, /*"u",85*/
    {0x80, 0x00, 0x80, 0x03, 0x80, 0x0C, 0x00, 0x30, 0x80, 0x0C, 0x80, 0x03, 0x80, 0x00, 0x00, 0x00}, /*"v",86*/
    {0x80, 0x01, 0x80, 0x0E, 0x00, 0x30, 0x80, 0x0C, 0x80, 0x07, 0x00, 0x38, 0x80, 0x06, 0x80, 0x01}, /*"w",87*/
    {0x00, 0x00, 0x80, 0x20, 0x80, 0x31, 0x80, 0x0E, 0x00, 0x2E, 0x80, 0x31, 0x80, 0x20, 0x00, 0x00}, /*"x",88*/
    {0x80, 0x00, 0x80, 0x81, 0x80, 0x86, 0x00, 0x78, 0x00, 0x18, 0x80, 0x06, 0x80, 0x01, 0x80, 0x00}, /*"y",89*/
    {0x00, 0x00, 0x80, 0x21, 0x80, 0x30, 0x80, 0x2C, 0x80, 0x22, 0x80, 0x21, 0x80, 0x30, 0x00, 0x00}, /*"z",90*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFC, 0x3E, 0x02, 0x40, 0x02, 0x40}, /*"{",91*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"|",92*/
    {0x02, 0x40, 0x02, 0x40, 0xFC, 0x3E, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"}",93*/
    {0x00, 0x00, 0x02, 0x00, 0x01, 0x00, 0x02, 0x00, 0x02, 0x00, 0x04, 0x00, 0x02, 0x00, 0x00, 0x00}, /*"~",94*/
};

static const uint8_t ascii_24_24[][36] = {
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*" ",0*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x77, 0x1C, 0xF0, 0x0F, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"!",1*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x60, 0x00, 0x00, 0x30, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x8C, 0x00, 0x00, 0x60, 0x00, 0x00, 0x30, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00}, /*""",2*/
    {0x00, 0x00, 0x00, 0x00, 0x83, 0x01, 0x00, 0x83, 0x1F, 0x00, 0xFF, 0x01, 0xF0, 0x83, 0x01, 0x00, 0x83, 0x01, 0x00, 0x83, 0x01, 0x00, 0x83, 0x01, 0x00, 0xFB, 0x1F, 0xF0, 0x87, 0x01, 0x00, 0x83, 0x01, 0x00, 0x00, 0x00}, /*"#",3*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x03, 0x07, 0xC0, 0x07, 0x0F, 0x60, 0x0C, 0x12, 0x20, 0x18, 0x10, 0xF8, 0xFF, 0x7F, 0x20, 0x71, 0x10, 0xE0, 0xE1, 0x0F, 0xC0, 0x81, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"$",4*/
    {0xC0, 0x07, 0x00, 0x30, 0x18, 0x00, 0x10, 0x10, 0x18, 0x30, 0x18, 0x07, 0xC0, 0xCF, 0x00, 0x00, 0x38, 0x00, 0x00, 0xC6, 0x07, 0xC0, 0x31, 0x18, 0x30, 0x10, 0x10, 0x00, 0x30, 0x18, 0x00, 0xC0, 0x07, 0x00, 0x00, 0x00}, /*"%",5*/
    {0x00, 0xC0, 0x07, 0x00, 0xE0, 0x0F, 0xE0, 0x17, 0x18, 0xF0, 0x3F, 0x10, 0x10, 0xF8, 0x10, 0xF0, 0xC7, 0x13, 0xE0, 0x03, 0x0F, 0x00, 0x08, 0x0E, 0x00, 0xF8, 0x19, 0x00, 0x08, 0x10, 0x00, 0x08, 0x10, 0x00, 0x00, 0x0C}, /*"&",6*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8C, 0x00, 0x00, 0x4C, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"'",7*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x80, 0x01, 0x03, 0x60, 0x00, 0x0C, 0x10, 0x00, 0x10, 0x08, 0x00, 0x20, 0x04, 0x00, 0x40, 0x00, 0x00, 0x00}, /*"(",8*/
    {0x00, 0x00, 0x00, 0x04, 0x00, 0x40, 0x08, 0x00, 0x20, 0x10, 0x00, 0x10, 0x60, 0x00, 0x0C, 0x80, 0x01, 0x03, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*")",9*/
    {0x00, 0x00, 0x00, 0x00, 0x66, 0x00, 0x00, 0x66, 0x00, 0x00, 0x24, 0x00, 0x00, 0x38, 0x00, 0x00, 0x10, 0x00, 0xC0, 0xFF, 0x03, 0x80, 0x18, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x24, 0x00, 0x00, 0x66, 0x00, 0x00, 0x66, 0x00}, /*"*",10*/
    {0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x80, 0xFF, 0x03, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00}, /*"+",11*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8C, 0x00, 0x00, 0x4C, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*",",12*/
    {0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00}, /*"-",13*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*".",14*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x38, 0x00, 0x00, 0x0E, 0x00, 0x80, 0x01, 0x00, 0x70, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x03, 0x00, 0xE0, 0x00, 0x00, 0x38, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"/",15*/
    {0x00, 0x00, 0x00, 0x00, 0xFE, 0x01, 0x80, 0xFF, 0x07, 0xC0, 0x01, 0x0E, 0x60, 0x00, 0x18, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0x60, 0x00, 0x18, 0xC0, 0x01, 0x0E, 0x80, 0xFF, 0x07, 0x00, 0xFE, 0x01, 0x00, 0x00, 0x00}, /*"0",16*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x10, 0x40, 0x00, 0x10, 0x40, 0x00, 0x10, 0xC0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"1",17*/
    {0x00, 0x00, 0x00, 0x80, 0x03, 0x18, 0x40, 0x03, 0x1C, 0x20, 0x00, 0x12, 0x20, 0x00, 0x11, 0x20, 0xC0, 0x10, 0x20, 0x60, 0x10, 0x60, 0x30, 0x10, 0xC0, 0x1F, 0x10, 0x80, 0x07, 0x1E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"2",18*/
    {0x00, 0x00, 0x00, 0xC0, 0x01, 0x0E, 0xC0, 0x01, 0x0E, 0x20, 0x00, 0x10, 0x20, 0x10, 0x10, 0x20, 0x10, 0x10, 0x60, 0x18, 0x10, 0xC0, 0x2F, 0x18, 0x80, 0xE7, 0x0F, 0x00, 0x80, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"3",19*/
    {0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xA0, 0x00, 0x00, 0x98, 0x00, 0x00, 0x84, 0x00, 0x00, 0x83, 0x10, 0x80, 0x80, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x00, 0x80, 0x10, 0x00, 0x80, 0x10, 0x00, 0x80, 0x00}, /*"4",20*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xE0, 0x3F, 0x0B, 0x20, 0x10, 0x10, 0x20, 0x08, 0x10, 0x20, 0x08, 0x10, 0x20, 0x08, 0x10, 0x20, 0x18, 0x1C, 0x20, 0xF0, 0x0F, 0x20, 0xE0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"5",21*/
    {0x00, 0x00, 0x00, 0x00, 0xFC, 0x01, 0x80, 0xFF, 0x07, 0xC0, 0x21, 0x0C, 0x40, 0x10, 0x18, 0x20, 0x08, 0x10, 0x20, 0x08, 0x10, 0x20, 0x08, 0x10, 0xE0, 0x18, 0x08, 0xC0, 0xF0, 0x0F, 0x00, 0xE0, 0x03, 0x00, 0x00, 0x00}, /*"6",22*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x01, 0x00, 0x60, 0x00, 0x00, 0x20, 0x00, 0x00, 0x20, 0x00, 0x1F, 0x20, 0xE0, 0x1F, 0x20, 0x18, 0x00, 0x20, 0x07, 0x00, 0xE0, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"7",23*/
    {0x00, 0x00, 0x00, 0x80, 0x83, 0x07, 0xC0, 0xCF, 0x0F, 0x60, 0x6C, 0x08, 0x20, 0x18, 0x10, 0x20, 0x10, 0x10, 0x20, 0x30, 0x10, 0x20, 0x30, 0x10, 0x60, 0x68, 0x18, 0xC0, 0xCF, 0x0F, 0x80, 0x83, 0x07, 0x00, 0x00, 0x00}, /*"8",24*/
    {0x00, 0x00, 0x00, 0x00, 0x1F, 0x00, 0xC0, 0x3F, 0x0C, 0xC0, 0x60, 0x1C, 0x20, 0x40, 0x10, 0x20, 0x40, 0x10, 0x20, 0x40, 0x10, 0x20, 0x20, 0x08, 0xC0, 0x10, 0x0F, 0x80, 0xFF, 0x03, 0x00, 0xFE, 0x00, 0x00, 0x00, 0x00}, /*"9",25*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1C, 0x1C, 0x00, 0x1C, 0x1C, 0x00, 0x1C, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*":",26*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xD8, 0x00, 0x0C, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*";",27*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x28, 0x00, 0x00, 0x44, 0x00, 0x00, 0x82, 0x00, 0x00, 0x01, 0x01, 0x80, 0x00, 0x02, 0x40, 0x00, 0x04, 0x20, 0x00, 0x08, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00}, /*"<",28*/
    {0x00, 0x00, 0x00, 0x00, 0x44, 0x00, 0x00, 0x44, 0x00, 0x00, 0x44, 0x00, 0x00, 0x44, 0x00, 0x00, 0x44, 0x00, 0x00, 0x44, 0x00, 0x00, 0x44, 0x00, 0x00, 0x44, 0x00, 0x00, 0x44, 0x00, 0x00, 0x44, 0x00, 0x00, 0x00, 0x00}, /*"=",29*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x10, 0x20, 0x00, 0x08, 0x40, 0x00, 0x04, 0x80, 0x00, 0x02, 0x00, 0x01, 0x01, 0x00, 0x82, 0x00, 0x00, 0x44, 0x00, 0x00, 0x28, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00}, /*">",30*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x07, 0x00, 0x40, 0x06, 0x00, 0x40, 0x00, 0x00, 0x20, 0x00, 0x1C, 0x20, 0xE0, 0x1C, 0x20, 0x10, 0x1C, 0x20, 0x18, 0x00, 0x60, 0x08, 0x00, 0xC0, 0x0F, 0x00, 0x80, 0x07, 0x00}, /*"?",31*/
    {0x00, 0x00, 0x00, 0x00, 0xFE, 0x00, 0xC0, 0xFF, 0x07, 0xE0, 0x00, 0x0E, 0x20, 0xF8, 0x18, 0x10, 0xFF, 0x11, 0x90, 0x03, 0x11, 0x90, 0xFC, 0x11, 0xA0, 0x07, 0x09, 0x60, 0x80, 0x0C, 0x80, 0x7F, 0x02, 0x00, 0x00, 0x00}, /*"@",32*/
    {0x00, 0x00, 0x10, 0x00, 0x00, 0x1C, 0x00, 0x80, 0x13, 0x00, 0x78, 0x10, 0x80, 0x47, 0x00, 0x60, 0x40, 0x00, 0xE0, 0x41, 0x00, 0x00, 0x5F, 0x00, 0x00, 0xF0, 0x11, 0x00, 0x80, 0x1F, 0x00, 0x00, 0x18, 0x00, 0x00, 0x10}, /*"A",33*/
    {0x00, 0x00, 0x00, 0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x20, 0x10, 0x10, 0x20, 0x10, 0x10, 0x20, 0x10, 0x10, 0x60, 0x18, 0x10, 0xC0, 0x2F, 0x18, 0xC0, 0xE7, 0x0F, 0x00, 0x80, 0x07, 0x00, 0x00, 0x00}, /*"B",34*/
    {0x00, 0x00, 0x00, 0x00, 0xFC, 0x01, 0x80, 0xFF, 0x07, 0xC0, 0x01, 0x0E, 0x40, 0x00, 0x18, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0x60, 0x00, 0x08, 0xE0, 0x00, 0x0C, 0x80, 0x03, 0x03, 0x00, 0x00, 0x00}, /*"C",35*/
    {0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0x40, 0x00, 0x08, 0xC0, 0x01, 0x0E, 0x80, 0xFF, 0x07, 0x00, 0xFE, 0x01, 0x00, 0x00, 0x00}, /*"D",36*/
    {0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x20, 0x10, 0x10, 0x20, 0x10, 0x10, 0x20, 0x10, 0x10, 0x20, 0x10, 0x10, 0x20, 0xFC, 0x10, 0x60, 0x00, 0x10, 0x60, 0x00, 0x18, 0x80, 0x01, 0x06, 0x00, 0x00, 0x00}, /*"E",37*/
    {0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x20, 0x10, 0x10, 0x20, 0x10, 0x10, 0x20, 0x10, 0x00, 0x20, 0x10, 0x00, 0x20, 0xFC, 0x00, 0x60, 0x00, 0x00, 0x60, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00}, /*"F",38*/
    {0x00, 0x00, 0x00, 0x00, 0xFC, 0x01, 0x80, 0xFF, 0x07, 0xC0, 0x01, 0x0E, 0x60, 0x00, 0x18, 0x20, 0x00, 0x10, 0x20, 0x40, 0x10, 0x20, 0x40, 0x10, 0x40, 0xC0, 0x0F, 0x80, 0xC3, 0x0F, 0x00, 0x40, 0x00, 0x00, 0x40, 0x00}, /*"G",39*/
    {0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x20, 0x10, 0x10, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x20, 0x10, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x20, 0x00, 0x10}, /*"H",40*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"I",41*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0xE0, 0x20, 0x00, 0x80, 0x20, 0x00, 0x80, 0x20, 0x00, 0xC0, 0xE0, 0xFF, 0x7F, 0xE0, 0xFF, 0x3F, 0x20, 0x00, 0x00, 0x20, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"J",42*/
    {0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x20, 0x30, 0x10, 0x00, 0x0C, 0x00, 0x00, 0x3A, 0x00, 0xA0, 0xE1, 0x00, 0xE0, 0x80, 0x13, 0x60, 0x00, 0x1E, 0x20, 0x00, 0x18, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00}, /*"K",43*/
    {0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x18, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00}, /*"L",44*/
    {0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0x0F, 0x10, 0x00, 0xFF, 0x00, 0x00, 0xE0, 0x0F, 0x00, 0x80, 0x07, 0x00, 0x7C, 0x00, 0xC0, 0x03, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x20, 0x00, 0x10, 0x00, 0x00, 0x00}, /*"M",45*/
    {0x00, 0x00, 0x10, 0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0x01, 0x10, 0x80, 0x07, 0x10, 0x00, 0x1E, 0x00, 0x00, 0x78, 0x00, 0x20, 0xC0, 0x03, 0x20, 0x00, 0x0F, 0xE0, 0xFF, 0x1F, 0x20, 0x00, 0x00, 0x20, 0x00, 0x00}, /*"N",46*/
    {0x00, 0x00, 0x00, 0x00, 0xFE, 0x01, 0x80, 0xFF, 0x07, 0xC0, 0x01, 0x0E, 0x60, 0x00, 0x18, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0x60, 0x00, 0x18, 0xC0, 0x00, 0x0C, 0x80, 0xFF, 0x07, 0x00, 0xFE, 0x01, 0x00, 0x00, 0x00}, /*"O",47*/
    {0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x20, 0x20, 0x10, 0x20, 0x20, 0x10, 0x20, 0x20, 0x00, 0x20, 0x20, 0x00, 0x20, 0x30, 0x00, 0x40, 0x10, 0x00, 0xC0, 0x1F, 0x00, 0x80, 0x0F, 0x00, 0x00, 0x00, 0x00}, /*"P",48*/
    {0x00, 0x00, 0x00, 0x00, 0xFE, 0x01, 0x80, 0xFF, 0x07, 0xC0, 0x01, 0x0E, 0x60, 0x00, 0x19, 0x20, 0x00, 0x11, 0x20, 0x00, 0x17, 0x60, 0x00, 0x3C, 0xC0, 0x00, 0x6C, 0x80, 0xFF, 0x67, 0x00, 0xFE, 0x21, 0x00, 0x00, 0x00}, /*"Q",49*/
    {0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x20, 0x10, 0x10, 0x20, 0x10, 0x00, 0x20, 0x10, 0x00, 0x20, 0x70, 0x00, 0x20, 0xD0, 0x01, 0x60, 0x08, 0x07, 0xC0, 0x0F, 0x1E, 0x80, 0x07, 0x18, 0x00, 0x00, 0x10}, /*"R",50*/
    {0x00, 0x00, 0x00, 0x80, 0x07, 0x07, 0xC0, 0x0F, 0x0C, 0x60, 0x1C, 0x08, 0x20, 0x18, 0x10, 0x20, 0x38, 0x10, 0x20, 0x30, 0x10, 0x20, 0x70, 0x10, 0x40, 0xE0, 0x18, 0xE0, 0xE1, 0x0F, 0x00, 0x80, 0x07, 0x00, 0x00, 0x00}, /*"S",51*/
    {0x80, 0x01, 0x00, 0x60, 0x00, 0x00, 0x20, 0x00, 0x00, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xE0, 0xFF, 0x1F, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0x20, 0x00, 0x00, 0x60, 0x00, 0x00, 0x80, 0x01, 0x00}, /*"T",52*/
    {0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0xE0, 0xFF, 0x07, 0xE0, 0xFF, 0x0F, 0x20, 0x00, 0x18, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x20, 0x00, 0x08, 0xE0, 0xFF, 0x07, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"U",53*/
    {0x20, 0x00, 0x00, 0x60, 0x00, 0x00, 0xE0, 0x07, 0x00, 0x20, 0x7E, 0x00, 0x00, 0xE0, 0x07, 0x00, 0x00, 0x1E, 0x00, 0x80, 0x03, 0x00, 0x78, 0x00, 0xA0, 0x07, 0x00, 0x60, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"V",54*/
    {0x20, 0x00, 0x00, 0xE0, 0x03, 0x00, 0x20, 0xFF, 0x00, 0x00, 0xC0, 0x1F, 0x20, 0xC0, 0x03, 0x60, 0x3C, 0x00, 0xE0, 0x3F, 0x00, 0x20, 0xF0, 0x1F, 0x00, 0x80, 0x07, 0x20, 0x7C, 0x00, 0xE0, 0x03, 0x00, 0x20, 0x00, 0x00}, /*"W",55*/
    {0x00, 0x00, 0x00, 0x20, 0x00, 0x10, 0x60, 0x00, 0x18, 0xE0, 0x01, 0x16, 0x20, 0x87, 0x01, 0x00, 0x7C, 0x00, 0x00, 0xE8, 0x00, 0x20, 0x86, 0x13, 0xE0, 0x01, 0x1E, 0x20, 0x00, 0x18, 0x20, 0x00, 0x10, 0x00, 0x00, 0x00}, /*"X",56*/
    {0x20, 0x00, 0x00, 0x60, 0x00, 0x00, 0xE0, 0x01, 0x00, 0x20, 0x0F, 0x10, 0x00, 0x3C, 0x10, 0x00, 0xF8, 0x1F, 0x00, 0xE0, 0x1F, 0x00, 0x18, 0x10, 0x20, 0x06, 0x10, 0xA0, 0x01, 0x00, 0x60, 0x00, 0x00, 0x20, 0x00, 0x00}, /*"Y",57*/
    {0x00, 0x00, 0x00, 0x80, 0x01, 0x10, 0xE0, 0x00, 0x1C, 0x60, 0x00, 0x17, 0x20, 0x80, 0x11, 0x20, 0x60, 0x10, 0x20, 0x18, 0x10, 0x20, 0x06, 0x10, 0xA0, 0x03, 0x10, 0xE0, 0x00, 0x18, 0x20, 0x00, 0x06, 0x00, 0x00, 0x00}, /*"Z",58*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0xFF, 0x7F, 0x04, 0x00, 0x40, 0x04, 0x00, 0x40, 0x04, 0x00, 0x40, 0x04, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"[",59*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x18, 0x00, 0x00, 0xE0, 0x00, 0x00, 0x80, 0x03, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x70, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00}, /*"\",60*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x40, 0x04, 0x00, 0x40, 0x04, 0x00, 0x40, 0x04, 0x00, 0x40, 0xFC, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"]",61*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x04, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x04, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"^",62*/
    {0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80, 0x00, 0x00, 0x80}, /*"_",63*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x08, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"`",64*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x98, 0x1F, 0x00, 0x98, 0x19, 0x00, 0x84, 0x10, 0x00, 0x44, 0x10, 0x00, 0x44, 0x10, 0x00, 0x44, 0x08, 0x00, 0xFC, 0x0F, 0x00, 0xF8, 0x1F, 0x00, 0x00, 0x10, 0x00, 0x00, 0x18}, /*"a",65*/
    {0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0xE0, 0xFF, 0x1F, 0xF0, 0xFF, 0x0F, 0x00, 0x18, 0x08, 0x00, 0x08, 0x10, 0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x00, 0x0C, 0x18, 0x00, 0xF8, 0x0F, 0x00, 0xF0, 0x03, 0x00, 0x00, 0x00}, /*"b",66*/
    {0x00, 0x00, 0x00, 0x00, 0xE0, 0x03, 0x00, 0xF8, 0x0F, 0x00, 0x18, 0x0C, 0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x00, 0x3C, 0x10, 0x00, 0x38, 0x08, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"c",67*/
    {0x00, 0x00, 0x00, 0x00, 0xE0, 0x03, 0x00, 0xF8, 0x0F, 0x00, 0x1C, 0x18, 0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x20, 0x04, 0x10, 0x20, 0x08, 0x08, 0xE0, 0xFF, 0x1F, 0xF0, 0xFF, 0x0F, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00}, /*"d",68*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x03, 0x00, 0xF8, 0x0F, 0x00, 0x88, 0x0C, 0x00, 0x84, 0x10, 0x00, 0x84, 0x10, 0x00, 0x84, 0x10, 0x00, 0x8C, 0x10, 0x00, 0xF8, 0x08, 0x00, 0xE0, 0x04, 0x00, 0x00, 0x00}, /*"e",69*/
    {0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x80, 0xFF, 0x1F, 0xC0, 0xFF, 0x1F, 0x60, 0x04, 0x10, 0x20, 0x04, 0x10, 0x20, 0x04, 0x10, 0xE0, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"f",70*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x70, 0x7E, 0x00, 0xF8, 0xCF, 0x00, 0x8C, 0x85, 0x00, 0x04, 0x85, 0x00, 0x0C, 0x8D, 0x00, 0xFC, 0xCD, 0x00, 0xF4, 0x78, 0x00, 0x0C, 0x78, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00}, /*"g",71*/
    {0x00, 0x00, 0x00, 0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xF0, 0xFF, 0x1F, 0x00, 0x08, 0x10, 0x00, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x04, 0x10, 0x00, 0xFC, 0x1F, 0x00, 0xF8, 0x1F, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00}, /*"h",72*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x60, 0xFC, 0x1F, 0x60, 0xFE, 0x1F, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"i",73*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0xC0, 0x00, 0x04, 0x80, 0x00, 0x04, 0x80, 0x60, 0x04, 0xC0, 0x60, 0xFC, 0x7F, 0x60, 0xFE, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"j",74*/
    {0x00, 0x00, 0x00, 0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xF0, 0xFF, 0x1F, 0x00, 0x80, 0x10, 0x00, 0x40, 0x00, 0x00, 0xE0, 0x00, 0x00, 0x1C, 0x13, 0x00, 0x04, 0x1E, 0x00, 0x04, 0x18, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00}, /*"k",75*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0x20, 0x00, 0x10, 0xE0, 0xFF, 0x1F, 0xF0, 0xFF, 0x1F, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"l",76*/
    {0x00, 0x04, 0x10, 0x00, 0xFC, 0x1F, 0x00, 0xFC, 0x1F, 0x00, 0x08, 0x10, 0x00, 0x04, 0x10, 0x00, 0xFC, 0x1F, 0x00, 0xF8, 0x1F, 0x00, 0x08, 0x10, 0x00, 0x04, 0x10, 0x00, 0xFC, 0x1F, 0x00, 0xFC, 0x1F, 0x00, 0x00, 0x10}, /*"m",77*/
    {0x00, 0x00, 0x00, 0x00, 0x04, 0x10, 0x00, 0xFC, 0x1F, 0x00, 0xFC, 0x1F, 0x00, 0x08, 0x10, 0x00, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x04, 0x10, 0x00, 0xFC, 0x1F, 0x00, 0xF8, 0x1F, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00}, /*"n",78*/
    {0x00, 0x00, 0x00, 0x00, 0xE0, 0x03, 0x00, 0xF0, 0x0F, 0x00, 0x18, 0x0C, 0x00, 0x0C, 0x10, 0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x00, 0x0C, 0x10, 0x00, 0x18, 0x0C, 0x00, 0xF0, 0x0F, 0x00, 0xE0, 0x03, 0x00, 0x00, 0x00}, /*"o",79*/
    {0x00, 0x00, 0x00, 0x00, 0x04, 0x80, 0x00, 0xFC, 0xFF, 0x00, 0xFC, 0xFF, 0x00, 0x08, 0x88, 0x00, 0x04, 0x90, 0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x00, 0x0C, 0x1C, 0x00, 0xF8, 0x0F, 0x00, 0xF0, 0x03, 0x00, 0x00, 0x00}, /*"p",80*/
    {0x00, 0x00, 0x00, 0x00, 0xE0, 0x03, 0x00, 0xF8, 0x0F, 0x00, 0x1C, 0x18, 0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x00, 0x04, 0x90, 0x00, 0x08, 0x88, 0x00, 0xF8, 0xFF, 0x00, 0xFC, 0xFF, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00}, /*"q",81*/
    {0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x00, 0xFC, 0x1F, 0x00, 0xFC, 0x1F, 0x00, 0x10, 0x10, 0x00, 0x08, 0x10, 0x00, 0x04, 0x10, 0x00, 0x04, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00}, /*"r",82*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x1E, 0x00, 0x78, 0x18, 0x00, 0xCC, 0x10, 0x00, 0xC4, 0x10, 0x00, 0x84, 0x11, 0x00, 0x84, 0x19, 0x00, 0x0C, 0x1F, 0x00, 0x3C, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"s",83*/
    {0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0xFF, 0x0F, 0xC0, 0xFF, 0x1F, 0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x00, 0x04, 0x10, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"t",84*/
    {0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0xFC, 0x0F, 0x00, 0xFE, 0x1F, 0x00, 0x00, 0x18, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x04, 0x08, 0x00, 0xFC, 0x1F, 0x00, 0xFE, 0x0F, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00}, /*"u",85*/
    {0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x7C, 0x00, 0x00, 0xC4, 0x03, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x0C, 0x00, 0x80, 0x03, 0x00, 0x74, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00}, /*"v",86*/
    {0x00, 0x04, 0x00, 0x00, 0x1C, 0x00, 0x00, 0xF4, 0x01, 0x00, 0x80, 0x1F, 0x00, 0x04, 0x0F, 0x00, 0xEC, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x84, 0x0F, 0x00, 0x00, 0x0F, 0x00, 0xE4, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x04, 0x00}, /*"w",87*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x04, 0x10, 0x00, 0x0C, 0x1C, 0x00, 0x3C, 0x12, 0x00, 0xE4, 0x01, 0x00, 0xC0, 0x01, 0x00, 0x24, 0x13, 0x00, 0x1C, 0x1C, 0x00, 0x04, 0x18, 0x00, 0x04, 0x10, 0x00, 0x00, 0x00}, /*"x",88*/
    {0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x04, 0xC0, 0x00, 0x3C, 0x80, 0x00, 0xC4, 0x81, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x0E, 0x00, 0xC4, 0x01, 0x00, 0x3C, 0x00, 0x00, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00}, /*"y",89*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C, 0x18, 0x00, 0x04, 0x1E, 0x00, 0x04, 0x13, 0x00, 0xC4, 0x11, 0x00, 0x74, 0x10, 0x00, 0x1C, 0x10, 0x00, 0x0C, 0x18, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"z",90*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x28, 0x00, 0xF8, 0xC7, 0x3F, 0x04, 0x00, 0x40, 0x04, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"{",91*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"|",92*/
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x40, 0x04, 0x00, 0x40, 0xF8, 0xC7, 0x3F, 0x00, 0x28, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"}",93*/
    {0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x02, 0x00, 0x00, 0x04, 0x00, 0x00, 0x08, 0x00, 0x00, 0x18, 0x00, 0x00, 0x10, 0x00, 0x00, 0x10, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"~",94*/
};

// 显(0) 示(1) 正(2) 累(3) 积(4) 负(5) 净(6) 计(7) 瞬(8) 时(9) 流(10) 量(11) 速(12) 起(13) 始(14) 时(15) 间(16) 戳(17) 网(18) 络(19)
// 无(20) 线(21) 信(22) 号(23) 强(24) 度(25) 电(26) 池(27) 压(28) 终(29) 端(30) 钟(31) 基(32) 表(33) 上(34) 报(35) 数(36) 据(37) 确(38) 认(39)
static const uint8_t chiness_16_16[][32] = {

    {0x00, 0x40, 0x00, 0x42, 0x00, 0x44, 0xFE, 0x58, 0x92, 0x40, 0x92, 0x7F, 0x92, 0x40, 0x92, 0x40,
     0x92, 0x40, 0x92, 0x7F, 0x92, 0x40, 0xFE, 0x50, 0x00, 0x48, 0x00, 0x46, 0x00, 0x40, 0x00, 0x00}, /*"显",0*/
    {0x40, 0x20, 0x40, 0x10, 0x42, 0x08, 0x42, 0x06, 0x42, 0x00, 0x42, 0x40, 0x42, 0x80, 0xC2, 0x7F,
     0x42, 0x00, 0x42, 0x00, 0x42, 0x00, 0x42, 0x02, 0x42, 0x04, 0x40, 0x08, 0x40, 0x30, 0x00, 0x00}, /*"示",1*/
    {0x00, 0x40, 0x02, 0x40, 0x02, 0x40, 0xC2, 0x7F, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0xFE, 0x7F,
     0x82, 0x40, 0x82, 0x40, 0x82, 0x40, 0x82, 0x40, 0x82, 0x40, 0x02, 0x40, 0x00, 0x40, 0x00, 0x00}, /*"正",2*/
    {0x00, 0x00, 0x00, 0x80, 0x3E, 0x48, 0x2A, 0x29, 0x2A, 0x09, 0xAA, 0x4D, 0x6A, 0x8D, 0x3E, 0x7B,
     0x2A, 0x0B, 0x2A, 0x09, 0xAA, 0x28, 0x2A, 0x4C, 0x3E, 0x98, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"累",3*/
    {0x20, 0x10, 0x24, 0x08, 0x24, 0x06, 0xA4, 0x01, 0xFE, 0xFF, 0x23, 0x01, 0x22, 0x06, 0x20, 0x80,
     0xFC, 0x63, 0x04, 0x19, 0x04, 0x01, 0x04, 0x01, 0x04, 0x09, 0xFC, 0x33, 0x00, 0xC0, 0x00, 0x00}, /*"积",4*/
    {0x00, 0x00, 0x20, 0x80, 0x10, 0x80, 0xE8, 0x4F, 0x24, 0x40, 0x27, 0x20, 0x24, 0x18, 0xA4, 0x07,
     0x24, 0x10, 0x34, 0x10, 0x2C, 0x20, 0xE0, 0x4F, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, /*"负",5*/
    {0x00, 0x04, 0x02, 0x04, 0x0C, 0x7F, 0xE0, 0x00, 0x00, 0x01, 0x28, 0x09, 0x24, 0x09, 0x27, 0x49,
     0x24, 0x89, 0xE4, 0x7F, 0x34, 0x09, 0x2C, 0x09, 0x20, 0x09, 0xE0, 0x1F, 0x00, 0x01, 0x00, 0x00}, /*"净",6*/
    {0x40, 0x00, 0x40, 0x00, 0x42, 0x00, 0xCC, 0x7F, 0x00, 0x20, 0x40, 0x10, 0x40, 0x00, 0x40, 0x00,
     0x40, 0x00, 0xFF, 0xFF, 0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x00, 0x00}, /*"计",7*/
    {0xFC, 0x3F, 0x24, 0x11, 0x24, 0x11, 0xFC, 0x3F, 0x80, 0x88, 0x62, 0x44, 0xAA, 0x2B, 0x32, 0x12,
     0x22, 0x0E, 0x26, 0x00, 0x29, 0x1A, 0x21, 0x12, 0xB1, 0xFF, 0x2D, 0x12, 0x60, 0x12, 0x00, 0x00}, /*"瞬",8*/
    {0x00, 0x00, 0xFC, 0x3F, 0x84, 0x10, 0x84, 0x10, 0x84, 0x10, 0xFC, 0x3F, 0x00, 0x00, 0x10, 0x00,
     0x10, 0x01, 0x10, 0x06, 0x10, 0x40, 0x10, 0x80, 0xFF, 0x7F, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00}, /*"时",9*/
    {0x10, 0x04, 0x60, 0x04, 0x02, 0x7E, 0x8C, 0x01, 0x00, 0x80, 0x44, 0x40, 0x64, 0x3E, 0x54, 0x00,
     0x4D, 0x00, 0x46, 0xFE, 0x44, 0x00, 0x54, 0x00, 0x64, 0x7E, 0xC4, 0x80, 0x04, 0xE0, 0x00, 0x00}, /*"流",10*/
    {0x20, 0x00, 0x20, 0x80, 0x20, 0x80, 0xBE, 0xAF, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xFF,
     0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xBE, 0xAF, 0x20, 0x80, 0x20, 0x80, 0x20, 0x00, 0x00, 0x00}, /*"量",11*/
    {0x40, 0x00, 0x40, 0x40, 0x42, 0x20, 0xCC, 0x1F, 0x00, 0x20, 0x04, 0x48, 0xF4, 0x44, 0x94, 0x42,
     0x94, 0x41, 0xFF, 0x5F, 0x94, 0x41, 0x94, 0x42, 0xF4, 0x44, 0x04, 0x48, 0x00, 0x40, 0x00, 0x00}, /*"速",12*/
    {0x40, 0x80, 0x48, 0x60, 0x48, 0x1F, 0x48, 0x20, 0xFF, 0x7F, 0x48, 0x44, 0x48, 0x44, 0x00, 0x40,
     0xC4, 0x4F, 0x44, 0x50, 0x44, 0x50, 0x44, 0x50, 0xFC, 0x50, 0x00, 0x5C, 0x00, 0x40, 0x00, 0x00}, /*"起",13*/
    {0x10, 0x40, 0x10, 0x22, 0xF0, 0x15, 0x1F, 0x08, 0x10, 0x16, 0xF0, 0x21, 0x00, 0x00, 0x40, 0x00,
     0xE0, 0xFE, 0x58, 0x42, 0x47, 0x42, 0x40, 0x42, 0x50, 0x42, 0x60, 0xFE, 0xC0, 0x00, 0x00, 0x00}, /*"始",14*/
    {0x00, 0x00, 0xFC, 0x3F, 0x84, 0x10, 0x84, 0x10, 0x84, 0x10, 0xFC, 0x3F, 0x00, 0x00, 0x10, 0x00,
     0x10, 0x01, 0x10, 0x06, 0x10, 0x40, 0x10, 0x80, 0xFF, 0x7F, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00}, /*"时",15*/
    {0x00, 0x00, 0xF8, 0xFF, 0x01, 0x00, 0x06, 0x00, 0x00, 0x00, 0xF0, 0x1F, 0x12, 0x11, 0x12, 0x11,
     0x12, 0x11, 0xF2, 0x1F, 0x02, 0x00, 0x02, 0x40, 0x02, 0x80, 0xFE, 0x7F, 0x00, 0x00, 0x00, 0x00}, /*"间",16*/
    {0x2A, 0x02, 0x12, 0xFF, 0xBE, 0x55, 0x00, 0x55, 0x2A, 0x7F, 0x92, 0x55, 0x3E, 0x55, 0x40, 0xC1,
     0x40, 0x40, 0x40, 0x20, 0xFF, 0x17, 0x20, 0x18, 0x22, 0x26, 0xAC, 0x41, 0x20, 0xF0, 0x00, 0x00}, /*"戳",17*/
    {0x00, 0x00, 0xFE, 0xFF, 0x02, 0x10, 0x22, 0x08, 0x42, 0x06, 0x82, 0x01, 0x72, 0x0E, 0x02, 0x10,
     0x22, 0x08, 0x42, 0x06, 0x82, 0x01, 0x72, 0x4E, 0x02, 0x80, 0xFE, 0x7F, 0x00, 0x00, 0x00, 0x00}, /*"网",18*/
    {0x20, 0x22, 0x30, 0x67, 0xAC, 0x22, 0x63, 0x12, 0x30, 0x12, 0x20, 0x04, 0x10, 0x02, 0x18, 0xFD,
     0xA7, 0x44, 0x44, 0x44, 0xA4, 0x44, 0x14, 0x45, 0x0C, 0xFD, 0x00, 0x02, 0x00, 0x02, 0x00, 0x00}, /*"络",19*/
    {0x00, 0x80, 0x40, 0x40, 0x42, 0x20, 0x42, 0x10, 0x42, 0x0C, 0xC2, 0x03, 0x7E, 0x00, 0x42, 0x00,
     0xC2, 0x3F, 0x42, 0x40, 0x42, 0x40, 0x42, 0x40, 0x40, 0x40, 0x40, 0x70, 0x00, 0x00, 0x00, 0x00}, /*"无",20*/
    {0x20, 0x22, 0x30, 0x67, 0xAC, 0x22, 0x63, 0x12, 0x20, 0x12, 0x18, 0x12, 0x80, 0x40, 0x90, 0x40,
     0x90, 0x20, 0xFF, 0x13, 0x90, 0x0C, 0x49, 0x14, 0x4A, 0x22, 0x48, 0x41, 0x40, 0xF8, 0x00, 0x00}, /*"线",21*/
    {0x00, 0x01, 0x80, 0x00, 0x60, 0x00, 0xF8, 0xFF, 0x07, 0x00, 0x00, 0x00, 0x04, 0x00, 0x24, 0xF9,
     0x24, 0x49, 0x25, 0x49, 0x26, 0x49, 0x24, 0x49, 0x24, 0x49, 0x24, 0xF9, 0x04, 0x00, 0x00, 0x00}, /*"信",22*/
    {0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0xBE, 0x06, 0xA2, 0x05, 0xA2, 0x04, 0xA2, 0x04, 0xA2, 0x04,
     0xA2, 0x44, 0xA2, 0x84, 0xA2, 0x44, 0xBE, 0x3C, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x00, 0x00}, /*"号",23*/
    {0x02, 0x00, 0xE2, 0x43, 0x22, 0x82, 0x22, 0x42, 0x3E, 0x3E, 0x00, 0x40, 0x80, 0x47, 0x9E, 0x44,
     0x92, 0x44, 0x92, 0x44, 0xF2, 0x7F, 0x92, 0x44, 0x92, 0x44, 0x9E, 0x54, 0x80, 0xE7, 0x00, 0x00}, /*"强",24*/
    {0x00, 0x40, 0x00, 0x30, 0xFC, 0x8F, 0x24, 0x80, 0x24, 0x84, 0x24, 0x4C, 0xFC, 0x55, 0x25, 0x25,
     0x26, 0x25, 0x24, 0x25, 0xFC, 0x55, 0x24, 0x4C, 0x24, 0x80, 0x24, 0x80, 0x04, 0x80, 0x00, 0x00}, /*"度",25*/
    {0x00, 0x00, 0x00, 0x00, 0xF8, 0x1F, 0x88, 0x08, 0x88, 0x08, 0x88, 0x08, 0x88, 0x08, 0xFF, 0x7F,
     0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0xF8, 0x9F, 0x00, 0x80, 0x00, 0xF0, 0x00, 0x00}, /*"电",26*/
    {0x10, 0x04, 0x60, 0x04, 0x02, 0x7E, 0xCC, 0x01, 0x80, 0x00, 0x80, 0x00, 0xFC, 0x3F, 0x40, 0x40,
     0x20, 0x40, 0xFF, 0x4F, 0x10, 0x40, 0x08, 0x44, 0xF8, 0x47, 0x00, 0x40, 0x00, 0x78, 0x00, 0x00}, /*"池",27*/
    {0x00, 0x80, 0x00, 0x60, 0xFE, 0x1F, 0x02, 0x40, 0x82, 0x40, 0x82, 0x40, 0x82, 0x40, 0x82, 0x40,
     0xFA, 0x7F, 0x82, 0x40, 0x82, 0x40, 0x82, 0x44, 0x82, 0x58, 0x82, 0x40, 0x02, 0x40, 0x00, 0x00}, /*"压",28*/
    {0x20, 0x22, 0x30, 0x67, 0xAC, 0x22, 0x63, 0x12, 0x30, 0x12, 0x20, 0x02, 0x10, 0x01, 0x18, 0x21,
     0xA7, 0x24, 0x44, 0x44, 0xA4, 0x48, 0x14, 0x91, 0x0C, 0x01, 0x00, 0x02, 0x00, 0x02, 0x00, 0x00}, /*"终",29*/
    {0xC8, 0x20, 0x08, 0x6F, 0x09, 0x20, 0x0E, 0x1C, 0xE8, 0x13, 0x08, 0x10, 0x40, 0x00, 0x5E, 0xFE,
     0x50, 0x02, 0x50, 0x7F, 0xDF, 0x02, 0x50, 0x7E, 0x50, 0x82, 0x5E, 0xFE, 0x40, 0x00, 0x00, 0x00}, /*"端",30*/
    {0x20, 0x01, 0x10, 0x01, 0x2C, 0x01, 0xE7, 0x7F, 0x24, 0x21, 0x24, 0x11, 0x00, 0x00, 0xF0, 0x07,
     0x10, 0x02, 0x10, 0x02, 0xFF, 0xFF, 0x10, 0x02, 0x10, 0x02, 0xF0, 0x07, 0x00, 0x00, 0x00, 0x00}, /*"钟",31*/
    {0x00, 0x11, 0x04, 0x11, 0x04, 0x89, 0x04, 0x85, 0xFF, 0x93, 0x54, 0x91, 0x54, 0x91, 0x54, 0xFD,
     0x54, 0x91, 0x54, 0x91, 0xFF, 0x93, 0x04, 0x85, 0x04, 0x89, 0x04, 0x11, 0x00, 0x11, 0x00, 0x00}, /*"基",32*/
    {0x00, 0x21, 0x04, 0x21, 0x24, 0x11, 0x24, 0x09, 0x24, 0xFD, 0x24, 0x83, 0x24, 0x41, 0xFF, 0x23,
     0x24, 0x05, 0x24, 0x09, 0x24, 0x11, 0x24, 0x29, 0x24, 0x25, 0x04, 0x41, 0x00, 0x41, 0x00, 0x00}, /*"表",33*/
    {0x00, 0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x40, 0xFF, 0x7F, 0x40, 0x40,
     0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x00, 0x40, 0x00, 0x40, 0x00, 0x00}, /*"上",34*/
    {0x10, 0x04, 0x10, 0x44, 0x10, 0x82, 0xFF, 0x7F, 0x10, 0x01, 0x90, 0x00, 0x00, 0x00, 0xFE, 0xFF,
     0x82, 0x80, 0x82, 0x43, 0x82, 0x2C, 0x92, 0x10, 0xA2, 0x2C, 0x9E, 0x43, 0x00, 0x80, 0x00, 0x00}, /*"报",35*/
    {0x90, 0x82, 0x52, 0x9A, 0x34, 0x56, 0x10, 0x63, 0xFF, 0x22, 0x10, 0x52, 0x34, 0x8E, 0x52, 0x00,
     0x80, 0x80, 0x70, 0x40, 0x8F, 0x33, 0x08, 0x0C, 0x08, 0x33, 0xF8, 0x40, 0x08, 0x80, 0x00, 0x00}, /*"数",36*/
    {0x10, 0x42, 0x10, 0x82, 0xFF, 0x7F, 0x10, 0x01, 0x90, 0x80, 0x00, 0x60, 0xFE, 0x1F, 0x92, 0x00,
     0x92, 0xFC, 0x92, 0x44, 0xF2, 0x47, 0x92, 0x44, 0x92, 0x44, 0x9E, 0xFC, 0x80, 0x00, 0x00, 0x00}, /*"据",37*/
    {0x04, 0x02, 0x84, 0x01, 0xE4, 0x7F, 0x5C, 0x10, 0x44, 0x10, 0xC4, 0x3F, 0x20, 0x80, 0x10, 0x60,
     0xE8, 0x1F, 0x27, 0x09, 0x24, 0x09, 0xE4, 0x3F, 0x34, 0x49, 0x2C, 0x89, 0xE0, 0x7F, 0x00, 0x00}, /*"确",38*/
    {0x40, 0x00, 0x40, 0x00, 0x42, 0x00, 0xCC, 0x3F, 0x00, 0x90, 0x00, 0x48, 0x00, 0x20, 0x00, 0x18,
     0x00, 0x07, 0xFF, 0x00, 0x00, 0x07, 0x00, 0x18, 0x00, 0x20, 0x00, 0x40, 0x00, 0x80, 0x00, 0x00}, /*"认",39*/

};

static const uint8_t chiness_24_24[][72] = {
    {0x00, 0x00, 0x00, 0x40, 0x80, 0x00, 0xC0, 0x80, 0x00, 0x40, 0x80, 0x00, 0x40, 0x80, 0x00, 0x40, 0x80, 0x10, 0xFC, 0xFF, 0x3F, 0x40, 0xC0, 0x01, 0x40, 0xC0, 0x01, 0xE0, 0xC0, 0x02, 0x60, 0xE1, 0x02, 0x60, 0xA2, 0x02, 0x50, 0xB6, 0x04, 0x50, 0x92, 0x04, 0x48, 0x88, 0x08, 0x48, 0x88, 0x18, 0x44, 0x84, 0x70, 0x42, 0x82, 0x00, 0x40, 0x80, 0x00, 0x40, 0x80, 0x00, 0x40, 0x80, 0x00, 0x40, 0x80, 0x00, 0x40, 0x80, 0x00, 0x00, 0x00, 0x00}, /*"林",0*/

};

/**
 * 要显示的汉字库表，索引需要和chiness_16_16，chiness_24_24，保持一致
 * “”
 */
static const uint8_t font_table[][4] = {
    {"显"},
    {"示"},
    {"正"},
    {"累"},
    {"积"},
    {"负"},
    {"净"},
    {"计"},
    {"瞬"},
    {"时"},
    {"流"},
    {"量"},
    {"速"},
    {"起"},
    {"始"},
    {"时"},
    {"间"},
    {"戳"},
    {"网"},
    {"络"},
    {"无"},
    {"线"},
    {"信"},
    {"号"},
    {"强"},
    {"度"},
    {"电"},
    {"池"},
    {"压"},
    {"终"},
    {"端"},
    {"钟"},
    {"基"},
    {"表"},
    {"上"},
    {"报"},
    {"数"},
    {"据"},
    {"确"},
    {"认"},
};
/*显(0) 示(1) 正(2) 累(3) 积(4) 负(5) 净(6) 计(7) 瞬(8) 时(9) 流(10) 量(11) 速(12) 起(13) 始(14) 时(15) 间(16) 戳(17) 网(18) 络(19)
 无(20) 线(21) 信(22) 号(23) 强(24) 度(25) 电(26) 池(27) 电(28) 压(29) 终(30) 端(31) 钟(32) 基(33) 表(34) 上(35) 报(36) 数(37) 据(38) 确(39)
 认(40)*/

/**
 * @brief 获取字体大小
 *
 * 根据传入的字体数组的第一个字节，判断并返回字体的大小。
 *
 * @param font 字体数组指针
 *
 * @return 返回字体的大小，值为1、2或4，分别表示字体大小为1、2或4
 */
uint8_t get_font_size(uint8_t *font)
{
  if (font[0] < 0x80)
    return 1;
  else if ((font[0] & 0xF0) == 0xF0)
    return 4;
  else if ((font[0] & 0xE0) == 0xE0)
    return 3;
  else if ((font[0] & 0xC0) == 0xC0)
    return 2;
  //	if(font[0] > 0x80)return 2;
  //	else return 1;
  else
    return 0;
}

/**
 * @brief 获取字体类型
 *
 * 根据传入的字体大小，返回对应的字体类型。
 *
 * @param size 字体大小
 * @return 返回对应的字体类型
 */
FONT_TYPE get_font_type(FONT_TYPE size)
{
  switch (size)
  {
  case FONT_0808B:
  case FONT_1616B:
  case FONT_2424B:
    return size;
  default:
    return FONT_DEFAULT;
  }
}

/**
 * @brief 获取字体信息
 *
 * 根据给定的字体数组和字体类型，获取对应的字体数据及其长度，并存储在输出参数中。
 *
 * @param font 字体数据数组指针
 * @param out_buf 输出字体数据缓冲区指针的指针
 * @param out_len 输出字体数据长度的指针
 * @param size 字体类型
 *
 * @return 返回字体数据的偏移量，如果为0表示获取失败
 */
uint8_t get_font_info(uint8_t *font, uint8_t **out_buf, uint8_t *out_len, FONT_TYPE size)
{
  // 获取字体大小偏移量
  uint8_t offset = get_font_size(font);
  // 如果偏移量为0，则直接返回偏移量
  if (offset == 0)
    return offset;
  // 获取字体类型
  size = get_font_type(size);

  // 如果字体第一个字节小于0x80
  if (font[0] < 0x80)
  {
    if (size == FONT_0808B)
    {
      // 设置输出缓冲区长度和输出缓冲区指针
      *out_len = sizeof(ascii_08_08[0]);
      *out_buf = (uint8_t *)ascii_08_08[font[0] - ' '];
    }
    else if (size == FONT_1616B)
    {
      // 设置输出缓冲区长度和输出缓冲区指针 16x16

      // 如果字体类型为16x16字体类型，则设置输出缓冲区长度和输出缓冲区指针为16x8大小
      // 设置输出缓冲区长度和输出缓冲区指针
      *out_len = sizeof(ascii_16_08[0]);
      *out_buf = (uint8_t *)ascii_16_08[font[0] - ' '];
    }
    // 如果字体类型为24x24
    else if (size == FONT_2424B)
    {
      // 设置输出缓冲区长度和输出缓冲区指针
      *out_len = sizeof(ascii_24_24[0]);
      *out_buf = (uint8_t *)ascii_24_24[font[0] - ' '];
    }
  }
  // 如果字体第一个字节大于等于0x80
  else
  {
    // 获取字体表的大小
    uint8_t count = sizeof(font_table) / sizeof(font_table[0]);
    // 遍历字体表
    for (uint8_t i = 0; i < count; i++)
    {
      // 如果当前字体与字体表中的字体匹配
      if (memcmp(font, font_table[i], offset) == 0)
      {
        
        if (size == FONT_1616B)
        {
          // 设置输出缓冲区长度和输出缓冲区指针
          *out_len = sizeof(chiness_16_16[0]);
          *out_buf = (uint8_t *)chiness_16_16[i];
        }
        // 如果字体类型为24x24
        else if (size == FONT_2424B)
        {
          // 设置输出缓冲区长度和输出缓冲区指针
          *out_len = sizeof(chiness_24_24[0]);
          *out_buf = (uint8_t *)chiness_24_24[i];
        }
      }
    }
  }

  // 返回字体大小偏移量
  return offset;
}
