Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.MX_GPIO_Init) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    main.o(i.auto_ctrl_task_process) refers to auto_ctrl_task.o(i.auto_ctrl) for auto_ctrl
    main.o(i.main) refers to stm32l0xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to system.o(i.system_clock_init) for system_clock_init
    main.o(i.main) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    main.o(i.main) refers to uidiv.o(.text) for __aeabi_uidivmod
    main.o(i.main) refers to stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    main.o(i.main) refers to stm32_flash.o(i.EEPROM_Init) for EEPROM_Init
    main.o(i.main) refers to user_config.o(i.read_config) for read_config
    main.o(i.main) refers to bsp_board.o(i.bsp_board_init) for bsp_board_init
    main.o(i.main) refers to uart.o(i.serial_485_init) for serial_485_init
    main.o(i.main) refers to debug.o(i.debug_init) for debug_init
    main.o(i.main) refers to rtc.o(i.rtc_init) for rtc_init
    main.o(i.main) refers to lcd12864.o(i.lcd_init) for lcd_init
    main.o(i.main) refers to lcd12864.o(i.lcd_clear) for lcd_clear
    main.o(i.main) refers to adc.o(i.ADC_Voltage_Init) for ADC_Voltage_Init
    main.o(i.main) refers to lcd12864.o(i.display_flow_rate) for display_flow_rate
    main.o(i.main) refers to main.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.main) refers to stm32l0xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to main.o(i.net_task_init) for net_task_init
    main.o(i.main) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.main) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.main) refers to main.o(i.net_task_process) for net_task_process
    main.o(i.main) refers to main.o(i.auto_ctrl_task_process) for auto_ctrl_task_process
    main.o(i.main) refers to protocol_common.o(i.protocol_parsing) for protocol_parsing
    main.o(i.main) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    main.o(i.main) refers to adc.o(i.Get_Power_Voltage) for Get_Power_Voltage
    main.o(i.main) refers to lcd12864.o(i.display_rotate) for display_rotate
    main.o(i.main) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.main) refers to user_config.o(.bss) for config
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.net_task_init) refers to net.o(i.net_init) for net_init
    main.o(i.net_task_init) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.net_task_init) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(i.net_task_init) refers to rtc.o(i.rtc_set_time) for rtc_set_time
    main.o(i.net_task_init) refers to main.o(.bss) for .bss
    main.o(i.net_task_init) refers to user_config.o(.bss) for config
    main.o(i.net_task_init) refers to main.o(.data) for .data
    main.o(i.net_task_process) refers to rtc.o(i.rtc_get_calendar) for rtc_get_calendar
    main.o(i.net_task_process) refers to net_protocol.o(i.net_protocol_parsing) for net_protocol_parsing
    main.o(i.net_task_process) refers to main.o(.data) for .data
    user_config.o(i.config_refresh) refers to stm32_flash.o(i.stm32_flash_write) for stm32_flash_write
    user_config.o(i.config_refresh) refers to stm32_flash.o(i.stm32_flash_read) for stm32_flash_read
    user_config.o(i.config_refresh) refers to user_config.o(.bss) for .bss
    user_config.o(i.read_config) refers to stm32_flash.o(i.EEPROM_ReadBuffer) for EEPROM_ReadBuffer
    user_config.o(i.read_config) refers to memseta.o(.text) for __aeabi_memclr4
    user_config.o(i.read_config) refers to stm32_flash.o(i.EEPROM_WriteBuffer) for EEPROM_WriteBuffer
    user_config.o(i.read_config) refers to user_config.o(.bss) for .bss
    protocol_common.o(i.get_serial_port) refers to memseta.o(.text) for __aeabi_memclr
    protocol_common.o(i.get_serial_port) refers to memcpya.o(.text) for __aeabi_memcpy
    protocol_common.o(i.get_serial_port) refers to uart.o(.bss) for serial_485
    protocol_common.o(i.get_serial_port) refers to protocol_common.o(.bss) for .bss
    protocol_common.o(i.protocol_parsing) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    protocol_common.o(i.protocol_parsing) refers to protocol_common.o(i.get_serial_port) for get_serial_port
    protocol_common.o(i.protocol_parsing) refers to memcpya.o(.text) for __aeabi_memcpy
    protocol_common.o(i.protocol_parsing) refers to pc_protocol.o(i.pc_protocol_parsing) for pc_protocol_parsing
    protocol_common.o(i.protocol_parsing) refers to printfa.o(i.__0printf) for __2printf
    protocol_common.o(i.protocol_parsing) refers to flow_meter_protocol.o(i.flow_meter_protocol_parsing) for flow_meter_protocol_parsing
    protocol_common.o(i.protocol_parsing) refers to water_meter_protocol.o(i.water_meter_protocol_parsing) for water_meter_protocol_parsing
    protocol_common.o(i.protocol_parsing) refers to memseta.o(.text) for __aeabi_memclr
    protocol_common.o(i.protocol_parsing) refers to daosheng_protocol.o(i.get_water_value) for get_water_value
    protocol_common.o(i.protocol_parsing) refers to protocol_common.o(.data) for .data
    protocol_common.o(i.protocol_parsing) refers to protocol_common.o(.bss) for .bss
    protocol_common.o(i.protocol_parsing) refers to user_config.o(.bss) for config
    daosheng_protocol.o(i.adjust_long_value) refers to dmul.o(.text) for __aeabi_dmul
    daosheng_protocol.o(i.adjust_long_value) refers to ddiv.o(.text) for __aeabi_ddiv
    daosheng_protocol.o(i.adjust_long_value) refers to dflti.o(.text) for __aeabi_i2d
    daosheng_protocol.o(i.build_read_command) refers to modbus_rtu.o(i.modbus_crc16) for modbus_crc16
    daosheng_protocol.o(i.daoSheng_protocol_parsing) refers to daosheng_protocol.o(i.mbus_crc16) for mbus_crc16
    daosheng_protocol.o(i.daoSheng_protocol_parsing) refers to daosheng_protocol.o(i.parseWaterMeterData) for parseWaterMeterData
    daosheng_protocol.o(i.daoSheng_protocol_parsing) refers to printfa.o(i.__0printf) for __2printf
    daosheng_protocol.o(i.daoSheng_protocol_parsing) refers to daosheng_protocol.o(.data) for .data
    daosheng_protocol.o(i.get_water_value) refers to daosheng_protocol.o(i.schedule_commands) for schedule_commands
    daosheng_protocol.o(i.parseWaterMeterData) refers to printfa.o(i.__0printf) for __2printf
    daosheng_protocol.o(i.parseWaterMeterData) refers to ffixi.o(.text) for __aeabi_f2iz
    daosheng_protocol.o(i.parse_modbus_data) refers to daosheng_protocol.o(i.parse_ieee754) for parse_ieee754
    daosheng_protocol.o(i.parse_modbus_data) refers to daosheng_protocol.o(i.parse_long) for parse_long
    daosheng_protocol.o(i.parse_modbus_data) refers to dflti.o(.text) for __aeabi_i2d
    daosheng_protocol.o(i.parse_modbus_data) refers to pow.o(i.pow) for pow
    daosheng_protocol.o(i.parse_modbus_data) refers to dmul.o(.text) for __aeabi_dmul
    daosheng_protocol.o(i.parse_modbus_data) refers to memcpya.o(.text) for __aeabi_memcpy
    daosheng_protocol.o(i.process_meter_data) refers to f2d.o(.text) for __aeabi_f2d
    daosheng_protocol.o(i.process_meter_data) refers to printfa.o(i.__0sprintf) for __2sprintf
    daosheng_protocol.o(i.process_meter_data) refers to lcd12864.o(i.lcd_show_string) for lcd_show_string
    daosheng_protocol.o(i.schedule_commands) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    daosheng_protocol.o(i.schedule_commands) refers to daosheng_protocol.o(i.build_read_command) for build_read_command
    daosheng_protocol.o(i.schedule_commands) refers to uart.o(i.serial_485_send_dat) for serial_485_send_dat
    daosheng_protocol.o(i.schedule_commands) refers to daosheng_protocol.o(.data) for .data
    daosheng_protocol.o(i.schedule_commands) refers to daosheng_protocol.o(.bss) for .bss
    modbus_rtu.o(i.modbus_rtu_read) refers to modbus_rtu.o(i.modbus_rtu_send_buffer_init) for modbus_rtu_send_buffer_init
    modbus_rtu.o(i.modbus_rtu_read) refers to modbus_rtu.o(i.modbus_rtu_send) for modbus_rtu_send
    pc_protocol.o(i.pc_protocol_parsing) refers to pc_protocol.o(i.__ARM_common_switch8) for __ARM_common_switch8
    pc_protocol.o(i.pc_protocol_parsing) refers to daosheng_protocol.o(i.mbus_crc16) for mbus_crc16
    pc_protocol.o(i.pc_protocol_parsing) refers to uart.o(i.serial_485_send_dat) for serial_485_send_dat
    pc_protocol.o(i.pc_protocol_parsing) refers to pc_protocol.o(.bss) for .bss
    pc_protocol.o(i.pc_protocol_parsing) refers to user_config.o(.bss) for config
    pc_protocol.o(i.pc_protocol_parsing) refers to pc_protocol.o(.constdata) for .constdata
    net_protocol.o(i._cmd_get_ip_sim_) refers to memseta.o(.text) for __aeabi_memclr4
    net_protocol.o(i._cmd_get_ip_sim_) refers to net_protocol.o(i.set_net_info) for set_net_info
    net_protocol.o(i._cmd_get_ip_sim_) refers to net_protocol.o(i.net_send_packet) for net_send_packet
    net_protocol.o(i._cmd_get_ip_sim_) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i._cmd_get_ip_sim_) refers to user_config.o(.bss) for config
    net_protocol.o(i._cmd_get_param_) refers to net_protocol.o(i.dec_to_hex) for dec_to_hex
    net_protocol.o(i._cmd_get_param_) refers to net_protocol.o(i.net_send_packet) for net_send_packet
    net_protocol.o(i._cmd_get_param_) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i._cmd_get_param_) refers to user_config.o(.bss) for config
    net_protocol.o(i._cmd_get_time_) refers to net_protocol.o(i.net_send_packet) for net_send_packet
    net_protocol.o(i._cmd_get_time_) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i._cmd_init_) refers to user_config.o(i.read_config) for read_config
    net_protocol.o(i._cmd_init_) refers to net_protocol.o(i.net_send_packet) for net_send_packet
    net_protocol.o(i._cmd_init_) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i._cmd_set_address_) refers to memcpya.o(.text) for __aeabi_memcpy
    net_protocol.o(i._cmd_set_address_) refers to user_config.o(i.config_refresh) for config_refresh
    net_protocol.o(i._cmd_set_address_) refers to net_protocol.o(i.net_send_packet) for net_send_packet
    net_protocol.o(i._cmd_set_address_) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i._cmd_set_address_) refers to user_config.o(.bss) for config
    net_protocol.o(i._cmd_set_ip_sim_) refers to net_protocol.o(i.get_net_info) for get_net_info
    net_protocol.o(i._cmd_set_ip_sim_) refers to user_config.o(i.config_refresh) for config_refresh
    net_protocol.o(i._cmd_set_ip_sim_) refers to memseta.o(.text) for __aeabi_memclr4
    net_protocol.o(i._cmd_set_ip_sim_) refers to net_protocol.o(i.set_net_info) for set_net_info
    net_protocol.o(i._cmd_set_ip_sim_) refers to net_protocol.o(i.net_send_packet) for net_send_packet
    net_protocol.o(i._cmd_set_ip_sim_) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i._cmd_set_ip_sim_) refers to user_config.o(.bss) for config
    net_protocol.o(i._cmd_set_param_) refers to net_protocol.o(i.hex_to_dec) for hex_to_dec
    net_protocol.o(i._cmd_set_param_) refers to user_config.o(i.config_refresh) for config_refresh
    net_protocol.o(i._cmd_set_param_) refers to net_protocol.o(i.dec_to_hex) for dec_to_hex
    net_protocol.o(i._cmd_set_param_) refers to net_protocol.o(i.net_send_packet) for net_send_packet
    net_protocol.o(i._cmd_set_param_) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i._cmd_set_param_) refers to user_config.o(.bss) for config
    net_protocol.o(i._cmd_set_time_) refers to rtc.o(i.rtc_set_time) for rtc_set_time
    net_protocol.o(i._cmd_set_time_) refers to net_protocol.o(i.net_send_packet) for net_send_packet
    net_protocol.o(i._cmd_set_time_) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i._cmd_timing_upload_) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    net_protocol.o(i._cmd_timing_upload_) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i._cmd_timing_upload_) refers to net_protocol.o(.data) for .data
    net_protocol.o(i._cmd_timing_upload_) refers to main.o(.bss) for net_info
    net_protocol.o(i.dec_to_hex) refers to uidiv.o(.text) for __aeabi_uidivmod
    net_protocol.o(i.dec_to_hex_to_byte) refers to uidiv.o(.text) for __aeabi_uidivmod
    net_protocol.o(i.get_hex_data) refers to memseta.o(.text) for __aeabi_memclr
    net_protocol.o(i.get_hex_data) refers to memcpya.o(.text) for __aeabi_memcpy
    net_protocol.o(i.get_hex_data) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i.get_hex_data) refers to main.o(.bss) for net_info
    net_protocol.o(i.get_net_info) refers to net_protocol.o(i.hex_to_dec) for hex_to_dec
    net_protocol.o(i.get_net_info) refers to uldiv.o(.text) for __aeabi_uldivmod
    net_protocol.o(i.get_net_info) refers to memcmp.o(.text) for memcmp
    net_protocol.o(i.get_net_info) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i.get_net_info) refers to user_config.o(.bss) for config
    net_protocol.o(i.hex_to_dec) refers to llmul.o(.text) for __aeabi_lmul
    net_protocol.o(i.net_data_parsing) refers to net_protocol.o(i.get_hex_data) for get_hex_data
    net_protocol.o(i.net_data_parsing) refers to memseta.o(.text) for __aeabi_memclr
    net_protocol.o(i.net_data_parsing) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i.net_data_parsing) refers to user_config.o(.bss) for config
    net_protocol.o(i.net_heartbeat) refers to memseta.o(.text) for __aeabi_memclr4
    net_protocol.o(i.net_heartbeat) refers to net_protocol.o(i.dec_to_hex_to_byte) for dec_to_hex_to_byte
    net_protocol.o(i.net_heartbeat) refers to net_protocol.o(i.dec_to_hex) for dec_to_hex
    net_protocol.o(i.net_heartbeat) refers to net_protocol.o(i.net_send_packet) for net_send_packet
    net_protocol.o(i.net_heartbeat) refers to net_protocol.o(.data) for .data
    net_protocol.o(i.net_heartbeat) refers to main.o(.data) for calendar
    net_protocol.o(i.net_heartbeat) refers to user_config.o(.bss) for config
    net_protocol.o(i.net_heartbeat) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i.net_heartbeat) refers to main.o(.bss) for net_info
    net_protocol.o(i.net_protocol_parsing) refers to net_protocol.o(i.net_data_parsing) for net_data_parsing
    net_protocol.o(i.net_protocol_parsing) refers to pc_protocol.o(i.__ARM_common_switch8) for __ARM_common_switch8
    net_protocol.o(i.net_protocol_parsing) refers to net_protocol.o(i._cmd_set_address_) for _cmd_set_address_
    net_protocol.o(i.net_protocol_parsing) refers to net_protocol.o(i._cmd_set_time_) for _cmd_set_time_
    net_protocol.o(i.net_protocol_parsing) refers to net_protocol.o(i._cmd_get_time_) for _cmd_get_time_
    net_protocol.o(i.net_protocol_parsing) refers to net_protocol.o(i._cmd_init_) for _cmd_init_
    net_protocol.o(i.net_protocol_parsing) refers to net_protocol.o(i._cmd_set_ip_sim_) for _cmd_set_ip_sim_
    net_protocol.o(i.net_protocol_parsing) refers to net_protocol.o(i._cmd_get_ip_sim_) for _cmd_get_ip_sim_
    net_protocol.o(i.net_protocol_parsing) refers to net_protocol.o(i._cmd_set_param_) for _cmd_set_param_
    net_protocol.o(i.net_protocol_parsing) refers to net_protocol.o(i._cmd_get_param_) for _cmd_get_param_
    net_protocol.o(i.net_protocol_parsing) refers to net_protocol.o(i._cmd_timing_upload_) for _cmd_timing_upload_
    net_protocol.o(i.net_protocol_parsing) refers to memseta.o(.text) for __aeabi_memclr
    net_protocol.o(i.net_protocol_parsing) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    net_protocol.o(i.net_protocol_parsing) refers to main.o(.bss) for net_info
    net_protocol.o(i.net_protocol_parsing) refers to net_protocol.o(.data) for .data
    net_protocol.o(i.net_protocol_parsing) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i.net_send_packet) refers to memseta.o(.text) for __aeabi_memclr4
    net_protocol.o(i.net_send_packet) refers to net_protocol.o(i.dec_to_hex_to_byte) for dec_to_hex_to_byte
    net_protocol.o(i.net_send_packet) refers to net_protocol.o(i.modbus_crc16) for modbus_crc16
    net_protocol.o(i.net_send_packet) refers to user_config.o(.bss) for config
    net_protocol.o(i.net_send_packet) refers to net_protocol.o(.bss) for .bss
    net_protocol.o(i.net_send_packet) refers to main.o(.data) for calendar
    net_protocol.o(i.net_send_packet) refers to main.o(.bss) for net_info
    net_protocol.o(i.set_net_info) refers to llmul.o(.text) for __aeabi_lmul
    net_protocol.o(i.set_net_info) refers to net_protocol.o(i.dec_to_hex) for dec_to_hex
    net_protocol.o(i.set_net_info) refers to user_config.o(.bss) for config
    bsp_board.o(i.EnterStandbyMode) refers to stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin) for HAL_PWR_EnableWakeUpPin
    bsp_board.o(i.EnterStandbyMode) refers to memseta.o(.text) for __aeabi_memclr4
    bsp_board.o(i.EnterStandbyMode) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    bsp_board.o(i.EnterStandbyMode) refers to stm32l0xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode) for HAL_PWR_EnterSTANDBYMode
    bsp_board.o(i.Error_Handler) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    bsp_board.o(i.Error_Handler) refers to stm32l0xx_hal.o(i.HAL_Delay) for HAL_Delay
    bsp_board.o(i.bsp_board_enter_standy) refers to stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin) for HAL_PWR_EnableWakeUpPin
    bsp_board.o(i.bsp_board_enter_standy) refers to bsp_board.o(i.EnterStandbyMode) for EnterStandbyMode
    bsp_board.o(i.bsp_board_init) refers to memseta.o(.text) for __aeabi_memclr4
    bsp_board.o(i.bsp_board_init) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd12864.o(i.disp_col) refers to lcd12864.o(i.lcd_write_command) for lcd_write_command
    lcd12864.o(i.disp_col) refers to lcd12864.o(i.lcd_write_data) for lcd_write_data
    lcd12864.o(i.disp_row) refers to lcd12864.o(i.lcd_write_command) for lcd_write_command
    lcd12864.o(i.disp_row) refers to lcd12864.o(i.lcd_write_data) for lcd_write_data
    lcd12864.o(i.display_battery_voltage) refers to adc.o(i.Get_Power_Voltage) for Get_Power_Voltage
    lcd12864.o(i.display_battery_voltage) refers to f2d.o(.text) for __aeabi_f2d
    lcd12864.o(i.display_battery_voltage) refers to printfa.o(i.__0sprintf) for __2sprintf
    lcd12864.o(i.display_battery_voltage) refers to lcd12864.o(i.lcd_show_string) for lcd_show_string
    lcd12864.o(i.display_flow_rate) refers to lcd12864.o(i.display_battery_voltage) for display_battery_voltage
    lcd12864.o(i.display_flow_rate) refers to printfa.o(i.__0sprintf) for __2sprintf
    lcd12864.o(i.display_flow_rate) refers to lcd12864.o(i.lcd_show_string) for lcd_show_string
    lcd12864.o(i.display_flow_rate) refers to lcd12864.o(i.lcd_refresh) for lcd_refresh
    lcd12864.o(i.display_flow_rate1) refers to printfa.o(i.__0sprintf) for __2sprintf
    lcd12864.o(i.display_flow_rate1) refers to lcd12864.o(i.lcd_show_string) for lcd_show_string
    lcd12864.o(i.display_flow_rate1) refers to adc.o(i.Get_Power_Voltage) for Get_Power_Voltage
    lcd12864.o(i.display_flow_rate1) refers to f2d.o(.text) for __aeabi_f2d
    lcd12864.o(i.display_flow_rate1) refers to strcpy.o(.text) for strcpy
    lcd12864.o(i.display_flow_rate1) refers to printfa.o(i.__0snprintf) for __2snprintf
    lcd12864.o(i.display_flow_rate1) refers to lcd12864.o(i.lcd_refresh) for lcd_refresh
    lcd12864.o(i.display_flow_rate1) refers to user_config.o(.bss) for config
    lcd12864.o(i.display_fourG_volage) refers to lcd12864.o(i.display_battery_voltage) for display_battery_voltage
    lcd12864.o(i.display_fourG_volage) refers to lcd12864.o(i.display_signal_strength) for display_signal_strength
    lcd12864.o(i.display_power_voltage) refers to adc.o(i.Get_Power_Voltage) for Get_Power_Voltage
    lcd12864.o(i.display_power_voltage) refers to f2d.o(.text) for __aeabi_f2d
    lcd12864.o(i.display_power_voltage) refers to printfa.o(i.__0sprintf) for __2sprintf
    lcd12864.o(i.display_power_voltage) refers to lcd12864.o(i.lcd_show_string) for lcd_show_string
    lcd12864.o(i.display_rotate) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    lcd12864.o(i.display_rotate) refers to lcd12864.o(i.display_flow_rate) for display_flow_rate
    lcd12864.o(i.display_rotate) refers to lcd12864.o(i.display_flow_rate1) for display_flow_rate1
    lcd12864.o(i.display_rotate) refers to lcd12864.o(.data) for .data
    lcd12864.o(i.display_signal_strength) refers to lcd12864.o(i.lcd_show_string) for lcd_show_string
    lcd12864.o(i.display_signal_strength) refers to lcd12864.o(i.lcd_draw_line) for lcd_draw_line
    lcd12864.o(i.dispstr) refers to lcd12864.o(i.lcd_write_command) for lcd_write_command
    lcd12864.o(i.dispstr) refers to lcd12864.o(i.lcd_write_data) for lcd_write_data
    lcd12864.o(i.dispstr) refers to lcd12864.o(.data) for .data
    lcd12864.o(i.lcd_backlight_ctrl) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd12864.o(i.lcd_clear) refers to lcd12864.o(.bss) for .bss
    lcd12864.o(i.lcd_draw_line) refers to lcd12864.o(i.lcd_draw_pixel) for lcd_draw_pixel
    lcd12864.o(i.lcd_draw_pixel) refers to lcd12864.o(.bss) for .bss
    lcd12864.o(i.lcd_gpio_init) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd12864.o(i.lcd_init) refers to lcd12864.o(i.lcd_gpio_init) for lcd_gpio_init
    lcd12864.o(i.lcd_init) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd12864.o(i.lcd_init) refers to memseta.o(.text) for __aeabi_memclr
    lcd12864.o(i.lcd_init) refers to board.o(i.delay_us) for delay_us
    lcd12864.o(i.lcd_init) refers to lcd12864.o(i.lcd_write_command) for lcd_write_command
    lcd12864.o(i.lcd_init) refers to lcd12864.o(i.lcd_clear) for lcd_clear
    lcd12864.o(i.lcd_init) refers to lcd12864.o(i.lcd_refresh) for lcd_refresh
    lcd12864.o(i.lcd_init) refers to lcd12864.o(.bss) for .bss
    lcd12864.o(i.lcd_refresh) refers to lcd12864.o(i.lcd_write_command) for lcd_write_command
    lcd12864.o(i.lcd_refresh) refers to lcd12864.o(i.lcd_write_data) for lcd_write_data
    lcd12864.o(i.lcd_refresh) refers to lcd12864.o(.bss) for .bss
    lcd12864.o(i.lcd_show_char) refers to lcd12864.o(i.lcd_draw_pixel) for lcd_draw_pixel
    lcd12864.o(i.lcd_show_string) refers to lcd_font.o(i.get_font_type) for get_font_type
    lcd12864.o(i.lcd_show_string) refers to lcd_font.o(i.get_font_info) for get_font_info
    lcd12864.o(i.lcd_show_string) refers to lcd12864.o(i.lcd_show_char) for lcd_show_char
    lcd12864.o(i.lcd_write_command) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd12864.o(i.lcd_write_data) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd_font.o(i.get_font_info) refers to lcd_font.o(i.get_font_size) for get_font_size
    lcd_font.o(i.get_font_info) refers to lcd_font.o(i.get_font_type) for get_font_type
    lcd_font.o(i.get_font_info) refers to memcmp.o(.text) for memcmp
    lcd_font.o(i.get_font_info) refers to lcd_font.o(.constdata) for .constdata
    net.o(i.USART1_IRQHandler) refers to memseta.o(.text) for __aeabi_memclr4
    net.o(i.USART1_IRQHandler) refers to net.o(.bss) for .bss
    net.o(i.USART1_IRQHandler) refers to net.o(.data) for .data
    net.o(i.net_clean) refers to memseta.o(.text) for __aeabi_memclr4
    net.o(i.net_clean) refers to net.o(.data) for .data
    net.o(i.net_init) refers to net.o(i.net_module_init) for net_module_init
    net.o(i.net_init) refers to ec600m_common.o(i.ec600m_base_function_register) for ec600m_base_function_register
    net.o(i.net_init) refers to ec600m_tcpip.o(i.ec600m_tcpip_register) for ec600m_tcpip_register
    net.o(i.net_init) refers to net.o(.data) for .data
    net.o(i.net_module_init) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    net.o(i.net_module_init) refers to net.o(i.net_module_reboot) for net_module_reboot
    net.o(i.net_module_reboot) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    net.o(i.net_module_reboot) refers to stm32l0xx_hal.o(i.HAL_Delay) for HAL_Delay
    net.o(i.net_module_reboot) refers to strstr.o(.text) for strstr
    net.o(i.net_module_reboot) refers to net.o(.data) for .data
    net.o(i.net_send_cmd) refers to stm32l0xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    net.o(i.net_send_cmd) refers to net.o(.bss) for .bss
    net.o(i.net_send_data) refers to stm32l0xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    net.o(i.net_send_data) refers to net.o(.bss) for .bss
    net.o(i.net_serial_init) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    net.o(i.net_serial_init) refers to stm32l0xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    net.o(i.net_serial_init) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    net.o(i.net_serial_init) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    net.o(i.net_serial_init) refers to net.o(.bss) for .bss
    net.o(.data) refers to net.o(i.net_serial_init) for net_serial_init
    net.o(.data) refers to net.o(i.net_clean) for net_clean
    net.o(.data) refers to net.o(i.net_send_data) for net_send_data
    net.o(.data) refers to net.o(i.net_send_cmd) for net_send_cmd
    ec600m_common.o(i.ec600m_base_function_register) refers to ec600m_common.o(.data) for .data
    ec600m_common.o(i.get_info) refers to ec600m_common.o(i.send_cmd) for send_cmd
    ec600m_common.o(i.get_info) refers to strstr.o(.text) for strstr
    ec600m_common.o(i.get_info) refers to strncpy.o(.text) for strncpy
    ec600m_common.o(i.get_info) refers to strchr.o(.text) for strchr
    ec600m_common.o(i.get_info) refers to net.o(.data) for serial_port
    ec600m_common.o(i.get_net_sig) refers to ec600m_common.o(i.send_cmd) for send_cmd
    ec600m_common.o(i.get_net_sig) refers to strstr.o(.text) for strstr
    ec600m_common.o(i.get_net_sig) refers to strchr.o(.text) for strchr
    ec600m_common.o(i.get_net_sig) refers to atoi.o(.text) for atoi
    ec600m_common.o(i.get_net_sig) refers to net.o(.data) for serial_port
    ec600m_common.o(i.send_cmd) refers to strlen.o(.text) for strlen
    ec600m_common.o(i.send_cmd) refers to uidiv.o(.text) for __aeabi_uidivmod
    ec600m_common.o(i.send_cmd) refers to stm32l0xx_hal.o(i.HAL_Delay) for HAL_Delay
    ec600m_common.o(i.send_cmd) refers to strstr.o(.text) for strstr
    ec600m_common.o(i.send_cmd) refers to net.o(.data) for serial_port
    ec600m_common.o(i.send_data) refers to net.o(.data) for serial_port
    ec600m_common.o(i.sync_time) refers to ec600m_common.o(i.send_cmd) for send_cmd
    ec600m_common.o(i.sync_time) refers to strchr.o(.text) for strchr
    ec600m_common.o(i.sync_time) refers to atoi.o(.text) for atoi
    ec600m_common.o(i.sync_time) refers to net.o(.data) for serial_port
    ec600m_common.o(i.watting) refers to uidiv.o(.text) for __aeabi_uidivmod
    ec600m_common.o(i.watting) refers to stm32l0xx_hal.o(i.HAL_Delay) for HAL_Delay
    ec600m_common.o(i.watting) refers to strstr.o(.text) for strstr
    ec600m_common.o(i.watting) refers to net.o(.data) for serial_port
    ec600m_common.o(.data) refers to ec600m_common.o(i.send_cmd) for send_cmd
    ec600m_common.o(.data) refers to ec600m_common.o(i.watting) for watting
    ec600m_common.o(.data) refers to ec600m_common.o(i.send_data) for send_data
    ec600m_common.o(.data) refers to ec600m_common.o(i.sync_time) for sync_time
    ec600m_common.o(.data) refers to ec600m_common.o(i.get_info) for get_info
    ec600m_common.o(.data) refers to ec600m_common.o(i.get_net_sig) for get_net_sig
    ec600m_tcpip.o(i.close) refers to stm32l0xx_hal.o(i.HAL_Delay) for HAL_Delay
    ec600m_tcpip.o(i.close) refers to strstr.o(.text) for strstr
    ec600m_tcpip.o(i.close) refers to net.o(.data) for serial_port
    ec600m_tcpip.o(i.close) refers to ec600m_common.o(.data) for ec600x_base_functions
    ec600m_tcpip.o(i.connect) refers to malloc.o(i.malloc) for malloc
    ec600m_tcpip.o(i.connect) refers to memseta.o(.text) for __aeabi_memclr
    ec600m_tcpip.o(i.connect) refers to strchr.o(.text) for strchr
    ec600m_tcpip.o(i.connect) refers to atoi.o(.text) for atoi
    ec600m_tcpip.o(i.connect) refers to printfa.o(i.__0sprintf) for __2sprintf
    ec600m_tcpip.o(i.connect) refers to malloc.o(i.free) for free
    ec600m_tcpip.o(i.connect) refers to ec600m_common.o(.data) for ec600x_base_functions
    ec600m_tcpip.o(i.connect) refers to net.o(.data) for serial_port
    ec600m_tcpip.o(i.ec600m_tcpip_register) refers to ec600m_tcpip.o(.data) for .data
    ec600m_tcpip.o(i.recv) refers to memcpya.o(.text) for __aeabi_memcpy
    ec600m_tcpip.o(i.recv) refers to net.o(.data) for serial_port
    ec600m_tcpip.o(i.send) refers to net.o(.data) for serial_port
    ec600m_tcpip.o(.data) refers to ec600m_tcpip.o(i.connect) for connect
    ec600m_tcpip.o(.data) refers to ec600m_tcpip.o(i.send) for send
    ec600m_tcpip.o(.data) refers to ec600m_tcpip.o(i.recv) for recv
    ec600m_tcpip.o(.data) refers to ec600m_tcpip.o(i.close) for close
    uart.o(i.USART2_IRQHandler) refers to memseta.o(.text) for __aeabi_memclr4
    uart.o(i.USART2_IRQHandler) refers to uart.o(.bss) for .bss
    uart.o(i.serial_485_init) refers to memseta.o(.text) for __aeabi_memclr4
    uart.o(i.serial_485_init) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    uart.o(i.serial_485_init) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    uart.o(i.serial_485_init) refers to pc_protocol.o(i.__ARM_common_switch8) for __ARM_common_switch8
    uart.o(i.serial_485_init) refers to stm32l0xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    uart.o(i.serial_485_init) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    uart.o(i.serial_485_init) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    uart.o(i.serial_485_init) refers to uart.o(.bss) for .bss
    uart.o(i.serial_485_send_dat) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    uart.o(i.serial_485_send_dat) refers to stm32l0xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    uart.o(i.serial_485_send_dat) refers to uart.o(.bss) for .bss
    debug.o(i.debug_init) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    debug.o(i.debug_init) refers to stm32l0xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    debug.o(i.debug_init) refers to debug.o(.bss) for .bss
    debug.o(i.fputc) refers to stm32l0xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    debug.o(i.fputc) refers to debug.o(.bss) for .bss
    stm32_flash.o(i.EEPROM_EraseAll) refers to stm32_flash.o(i.EEPROM_ErasePage) for EEPROM_ErasePage
    stm32_flash.o(i.EEPROM_ErasePage) refers to stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Erase) for HAL_FLASHEx_DATAEEPROM_Erase
    stm32_flash.o(i.EEPROM_Init) refers to stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32_flash.o(i.EEPROM_Init) refers to stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Unlock) for HAL_FLASHEx_DATAEEPROM_Unlock
    stm32_flash.o(i.EEPROM_ReadBuffer) refers to memcpya.o(.text) for __aeabi_memcpy
    stm32_flash.o(i.EEPROM_WriteBuffer) refers to stm32_flash.o(i.EEPROM_WriteByte) for EEPROM_WriteByte
    stm32_flash.o(i.EEPROM_WriteByte) refers to stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Program) for HAL_FLASHEx_DATAEEPROM_Program
    stm32_flash.o(i.stm32_flash_erase) refers to stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) for HAL_FLASHEx_Erase
    stm32_flash.o(i.stm32_flash_read) refers to stm32_flash.o(i.stm32_flash_read_byte) for stm32_flash_read_byte
    stm32_flash.o(i.stm32_flash_write) refers to stm32l0xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    stm32_flash.o(i.stm32_flash_write) refers to memcpya.o(.text) for __aeabi_memcpy
    stm32_flash.o(i.stm32_flash_write) refers to stm32_flash.o(i.stm32_flash_erase) for stm32_flash_erase
    stm32_flash.o(i.stm32_flash_write) refers to stm32_flash.o(i.stm32_flash_read) for stm32_flash_read
    stm32_flash.o(i.stm32_flash_write) refers to stm32_flash.o(i.stm32_flash_write_nbyte) for stm32_flash_write_nbyte
    stm32_flash.o(i.stm32_flash_write) refers to stm32l0xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    stm32_flash.o(i.stm32_flash_write) refers to stm32_flash.o(.bss) for .bss
    stm32_flash.o(i.stm32_flash_write_nbyte) refers to stm32l0xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    adc.o(i.ADC_Init_LowPower) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.ADC_Init_LowPower) refers to printfa.o(i.__0printf) for __2printf
    adc.o(i.ADC_Init_LowPower) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.ADC_Init_LowPower) refers to adc.o(.bss) for .bss
    adc.o(i.ADC_Voltage_Init) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.ADC_Voltage_Init) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.ADC_Voltage_Init) refers to adc.o(.bss) for .bss
    adc.o(i.Get_Power_Voltage) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_Start) for HAL_ADC_Start
    adc.o(i.Get_Power_Voltage) refers to printfa.o(i.__0printf) for __2printf
    adc.o(i.Get_Power_Voltage) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_PollForConversion) for HAL_ADC_PollForConversion
    adc.o(i.Get_Power_Voltage) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_GetValue) for HAL_ADC_GetValue
    adc.o(i.Get_Power_Voltage) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_Stop) for HAL_ADC_Stop
    adc.o(i.Get_Power_Voltage) refers to ffltui.o(.text) for __aeabi_ui2f
    adc.o(i.Get_Power_Voltage) refers to fmul.o(.text) for __aeabi_fmul
    adc.o(i.Get_Power_Voltage) refers to fdiv.o(.text) for __aeabi_fdiv
    adc.o(i.Get_Power_Voltage) refers to adc.o(.bss) for .bss
    adc.o(i.Read_VREFINT_Raw) refers to printfa.o(i.__0printf) for __2printf
    adc.o(i.Read_VREFINT_Raw) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_Start) for HAL_ADC_Start
    adc.o(i.Read_VREFINT_Raw) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_PollForConversion) for HAL_ADC_PollForConversion
    adc.o(i.Read_VREFINT_Raw) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_Stop) for HAL_ADC_Stop
    adc.o(i.Read_VREFINT_Raw) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_GetValue) for HAL_ADC_GetValue
    adc.o(i.Read_VREFINT_Raw) refers to uidiv.o(.text) for __aeabi_uidivmod
    adc.o(i.Read_VREFINT_Raw) refers to adc.o(.bss) for .bss
    adc.o(i.vdda_get_voltage) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_Start) for HAL_ADC_Start
    adc.o(i.vdda_get_voltage) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_DeInit) for HAL_ADC_DeInit
    adc.o(i.vdda_get_voltage) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.vdda_get_voltage) refers to printfa.o(i.__0printf) for __2printf
    adc.o(i.vdda_get_voltage) refers to adc.o(i.ADC_Init_LowPower) for ADC_Init_LowPower
    adc.o(i.vdda_get_voltage) refers to stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_EnableVREFINT) for HAL_ADCEx_EnableVREFINT
    adc.o(i.vdda_get_voltage) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_GetError) for HAL_ADC_GetError
    adc.o(i.vdda_get_voltage) refers to adc.o(i.Read_VREFINT_Raw) for Read_VREFINT_Raw
    adc.o(i.vdda_get_voltage) refers to stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVREFINT) for HAL_ADCEx_DisableVREFINT
    adc.o(i.vdda_get_voltage) refers to ffltui.o(.text) for __aeabi_ui2f
    adc.o(i.vdda_get_voltage) refers to fmul.o(.text) for __aeabi_fmul
    adc.o(i.vdda_get_voltage) refers to fdiv.o(.text) for __aeabi_fdiv
    adc.o(i.vdda_get_voltage) refers to ffixui.o(.text) for __aeabi_f2uiz
    adc.o(i.vdda_get_voltage) refers to adc.o(.bss) for .bss
    rtc.o(i.rtc_get_calendar) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_GetDate) for HAL_RTC_GetDate
    rtc.o(i.rtc_get_calendar) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_GetTime) for HAL_RTC_GetTime
    rtc.o(i.rtc_get_calendar) refers to rtc.o(.bss) for .bss
    rtc.o(i.rtc_init) refers to stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    rtc.o(i.rtc_init) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    rtc.o(i.rtc_init) refers to stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    rtc.o(i.rtc_init) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_Init) for HAL_RTC_Init
    rtc.o(i.rtc_init) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPRead) for HAL_RTCEx_BKUPRead
    rtc.o(i.rtc_init) refers to rtc.o(i.rtc_set_time) for rtc_set_time
    rtc.o(i.rtc_init) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPWrite) for HAL_RTCEx_BKUPWrite
    rtc.o(i.rtc_init) refers to rtc.o(.bss) for .bss
    rtc.o(i.rtc_set_alarm) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT) for HAL_RTC_SetAlarm_IT
    rtc.o(i.rtc_set_alarm) refers to rtc.o(.bss) for .bss
    rtc.o(i.rtc_set_time) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_SetDate) for HAL_RTC_SetDate
    rtc.o(i.rtc_set_time) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_SetTime) for HAL_RTC_SetTime
    rtc.o(i.rtc_set_time) refers to rtc.o(.bss) for .bss
    system_stm32l0xx.o(i.SystemCoreClockUpdate) refers to uidiv.o(.text) for __aeabi_uidivmod
    system_stm32l0xx.o(i.SystemCoreClockUpdate) refers to system_stm32l0xx.o(.data) for .data
    system_stm32l0xx.o(i.SystemCoreClockUpdate) refers to system_stm32l0xx.o(.constdata) for .constdata
    startup_stm32l072xx.o(RESET) refers to startup_stm32l072xx.o(STACK) for __initial_sp
    startup_stm32l072xx.o(RESET) refers to startup_stm32l072xx.o(.text) for Reset_Handler
    startup_stm32l072xx.o(RESET) refers to context_rvds.o(.text) for HardFault_Handler
    startup_stm32l072xx.o(RESET) refers to board.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32l072xx.o(RESET) refers to net.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32l072xx.o(RESET) refers to uart.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32l072xx.o(.text) refers to system_stm32l0xx.o(i.SystemInit) for SystemInit
    startup_stm32l072xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system.o(i.system_clock_init) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    system.o(i.system_clock_init) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    stm32l0xx_hal.o(i.HAL_DeInit) refers to stm32l0xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32l0xx_hal.o(i.HAL_Delay) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal.o(i.HAL_Delay) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal.o(i.HAL_GetTick) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal.o(i.HAL_GetTickFreq) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal.o(i.HAL_GetTickPrio) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal.o(i.HAL_IncTick) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal.o(i.HAL_Init) refers to stm32l0xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l0xx_hal.o(i.HAL_Init) refers to stm32l0xx_hal.o(i.HAL_MspInit) for HAL_MspInit
    stm32l0xx_hal.o(i.HAL_InitTick) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal.o(i.HAL_InitTick) refers to stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32l0xx_hal.o(i.HAL_InitTick) refers to stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32l0xx_hal.o(i.HAL_InitTick) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal.o(i.HAL_InitTick) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal.o(i.HAL_SetTickFreq) refers to stm32l0xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l0xx_hal.o(i.HAL_SetTickFreq) refers to stm32l0xx_hal.o(.data) for .data
    stm32l0xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32l0xx_hal_adc.o(i.ADC_DMAError) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32l0xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32l0xx_hal_adc.o(i.ADC_DelayMicroSecond) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_adc.o(i.ADC_DelayMicroSecond) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_adc.o(i.ADC_Disable) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc.o(i.ADC_Enable) refers to stm32l0xx_hal_adc.o(i.ADC_DelayMicroSecond) for ADC_DelayMicroSecond
    stm32l0xx_hal_adc.o(i.ADC_Enable) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to stm32l0xx_hal_adc.o(i.ADC_DelayMicroSecond) for ADC_DelayMicroSecond
    stm32l0xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32l0xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l0xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32l0xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l0xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32l0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32l0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32l0xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32l0xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32l0xx_hal_adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32l0xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32l0xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l0xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l0xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l0xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32l0xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32l0xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32l0xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32l0xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32l0xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l0xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32l0xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32l0xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32l0xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_EnableVREFINT) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_EnableVREFINTTempSensor) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32l0xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32l0xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32l0xx_hal_dma.o(i.HAL_DMA_DeInit) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_dma.o(i.HAL_DMA_Init) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32l0xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32l0xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32l0xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback) refers to pc_protocol.o(i.__ARM_common_switch8) for __ARM_common_switch8
    stm32l0xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32l0xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32l0xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l0xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l0xx.o(.constdata) for AHBPrescTable
    stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32l0xx_hal.o(.data) for uwTickPrio
    stm32l0xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l0xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l0xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32l0xx_hal.o(.data) for uwTickPrio
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32l0xx.o(.constdata) for APBPrescTable
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32l0xx.o(.constdata) for APBPrescTable
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32l0xx_hal_rcc.o(i.__ARM_common_ll_muluu) for __ARM_common_ll_muluu
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to system_stm32l0xx.o(.constdata) for PLLMulTable
    stm32l0xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to memseta.o(.text) for __aeabi_memclr4
    stm32l0xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32l0xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l0xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l0xx.o(.constdata) for AHBPrescTable
    stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32l0xx.o(.data) for SystemCoreClock
    stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32l0xx_hal.o(.data) for uwTickPrio
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to system_stm32l0xx.o(.constdata) for PLLMulTable
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_AlarmAEventCallback) for HAL_RTC_AlarmAEventCallback
    stm32l0xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_AlarmBEventCallback) for HAL_RTCEx_AlarmBEventCallback
    stm32l0xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32l0xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32l0xx_hal_rtc.o(i.HAL_RTC_DeInit) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_MspDeInit) for HAL_RTC_MspDeInit
    stm32l0xx_hal_rtc.o(i.HAL_RTC_DeactivateAlarm) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc.o(i.HAL_RTC_GetAlarm) refers to stm32l0xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32l0xx_hal_rtc.o(i.HAL_RTC_GetDate) refers to stm32l0xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32l0xx_hal_rtc.o(i.HAL_RTC_GetTime) refers to stm32l0xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32l0xx_hal_rtc.o(i.HAL_RTC_Init) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_MspInit) for HAL_RTC_MspInit
    stm32l0xx_hal_rtc.o(i.HAL_RTC_Init) refers to stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32l0xx_hal_rtc.o(i.HAL_RTC_Init) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32l0xx_hal_rtc.o(i.HAL_RTC_PollForAlarmAEvent) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm) refers to stm32l0xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT) refers to stm32l0xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32l0xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetDate) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32l0xx_hal_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32l0xx_hal_rtc.o(i.HAL_RTC_SetTime) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateRefClock) refers to stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateWakeUpTimer) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_GetTimeStamp) refers to stm32l0xx_hal_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForAlarmBEvent) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper1Event) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper2Event) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper3Event) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTimeStampEvent) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForWakeUpTimerEvent) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetRefClock) refers to stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSmoothCalib) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift) refers to stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer_IT) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TimeStampEventCallback) for HAL_RTCEx_TimeStampEventCallback
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper1EventCallback) for HAL_RTCEx_Tamper1EventCallback
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper2EventCallback) for HAL_RTCEx_Tamper2EventCallback
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper3EventCallback) for HAL_RTCEx_Tamper3EventCallback
    stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerIRQHandler) refers to stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerEventCallback) for HAL_RTCEx_WakeUpTimerEventCallback
    stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l0xx_hal_uart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l0xx_hal_uart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l0xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l0xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l0xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l0xx_hal_uart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32l0xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32l0xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l0xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32l0xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l0xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l0xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32l0xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32l0xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l0xx_hal_uart.o(i.HAL_UART_DeInit) refers to stm32l0xx_hal_uart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32l0xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32l0xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l0xx_hal_uart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l0xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l0xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l0xx_hal_uart.o(i.HAL_UART_Init) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l0xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32l0xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32l0xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l0xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l0xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32l0xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32l0xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32l0xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32l0xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l0xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32l0xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l0xx_hal_uart.o(i.UART_DMAError) refers to stm32l0xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32l0xx_hal_uart.o(i.UART_DMAError) refers to stm32l0xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32l0xx_hal_uart.o(i.UART_DMAError) refers to stm32l0xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32l0xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32l0xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32l0xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l0xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32l0xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32l0xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l0xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32l0xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32l0xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32l0xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32l0xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32l0xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32l0xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32l0xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l0xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32l0xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l0xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32l0xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32l0xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32l0xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32l0xx_hal_uart.o(i.UART_SetConfig) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l0xx_hal_uart.o(i.UART_SetConfig) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l0xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32l0xx_hal_uart.o(i.UART_SetConfig) refers to pc_protocol.o(i.__ARM_common_switch8) for __ARM_common_switch8
    stm32l0xx_hal_uart.o(i.UART_SetConfig) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l0xx_hal_uart.o(i.UART_SetConfig) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l0xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l0xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32l0xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32l0xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32l0xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32l0xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32l0xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l0xx_hal_uart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32l0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l0xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32l0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32l0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32l0xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32l0xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l0xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig) for UARTEx_Wakeup_AddressConfig
    stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32l0xx_hal_usart.o(i.HAL_USART_Abort) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_usart.o(i.HAL_USART_Abort) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l0xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32l0xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32l0xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32l0xx_hal_usart.o(i.USART_DMATxAbortCallback) for USART_DMATxAbortCallback
    stm32l0xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32l0xx_hal_usart.o(i.USART_DMARxAbortCallback) for USART_DMARxAbortCallback
    stm32l0xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32l0xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32l0xx_hal_usart.o(i.USART_EndTransfer) for USART_EndTransfer
    stm32l0xx_hal_usart.o(i.HAL_USART_DeInit) refers to stm32l0xx_hal_usart.o(i.HAL_USART_MspDeInit) for HAL_USART_MspDeInit
    stm32l0xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32l0xx_hal_usart.o(i.USART_EndTransfer) for USART_EndTransfer
    stm32l0xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32l0xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32l0xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32l0xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32l0xx_hal_usart.o(i.USART_EndTransmit_IT) for USART_EndTransmit_IT
    stm32l0xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32l0xx_hal_usart.o(i.USART_DMAAbortOnError) for USART_DMAAbortOnError
    stm32l0xx_hal_usart.o(i.HAL_USART_Init) refers to stm32l0xx_hal_usart.o(i.HAL_USART_MspInit) for HAL_USART_MspInit
    stm32l0xx_hal_usart.o(i.HAL_USART_Init) refers to stm32l0xx_hal_usart.o(i.USART_SetConfig) for USART_SetConfig
    stm32l0xx_hal_usart.o(i.HAL_USART_Init) refers to stm32l0xx_hal_usart.o(i.USART_CheckIdleState) for USART_CheckIdleState
    stm32l0xx_hal_usart.o(i.HAL_USART_Receive) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_usart.o(i.HAL_USART_Receive) refers to stm32l0xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32l0xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32l0xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32l0xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32l0xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32l0xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32l0xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32l0xx_hal_usart.o(i.HAL_USART_Receive_IT) refers to stm32l0xx_hal_usart.o(i.USART_RxISR_8BIT) for USART_RxISR_8BIT
    stm32l0xx_hal_usart.o(i.HAL_USART_Receive_IT) refers to stm32l0xx_hal_usart.o(i.USART_RxISR_16BIT) for USART_RxISR_16BIT
    stm32l0xx_hal_usart.o(i.HAL_USART_Transmit) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_usart.o(i.HAL_USART_Transmit) refers to stm32l0xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive) refers to stm32l0xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32l0xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32l0xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32l0xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32l0xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32l0xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive_IT) refers to stm32l0xx_hal_usart.o(i.USART_TxISR_8BIT) for USART_TxISR_8BIT
    stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive_IT) refers to stm32l0xx_hal_usart.o(i.USART_RxISR_8BIT) for USART_RxISR_8BIT
    stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive_IT) refers to stm32l0xx_hal_usart.o(i.USART_TxISR_16BIT) for USART_TxISR_16BIT
    stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive_IT) refers to stm32l0xx_hal_usart.o(i.USART_RxISR_16BIT) for USART_RxISR_16BIT
    stm32l0xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32l0xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32l0xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32l0xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32l0xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32l0xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32l0xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32l0xx_hal_usart.o(i.HAL_USART_Transmit_IT) refers to stm32l0xx_hal_usart.o(i.USART_TxISR_8BIT) for USART_TxISR_8BIT
    stm32l0xx_hal_usart.o(i.HAL_USART_Transmit_IT) refers to stm32l0xx_hal_usart.o(i.USART_TxISR_16BIT) for USART_TxISR_16BIT
    stm32l0xx_hal_usart.o(i.USART_CheckIdleState) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_usart.o(i.USART_CheckIdleState) refers to stm32l0xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32l0xx_hal_usart.o(i.USART_DMAAbortOnError) refers to stm32l0xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32l0xx_hal_usart.o(i.USART_DMAError) refers to stm32l0xx_hal_usart.o(i.USART_EndTransfer) for USART_EndTransfer
    stm32l0xx_hal_usart.o(i.USART_DMAError) refers to stm32l0xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32l0xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32l0xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32l0xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32l0xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32l0xx_hal_usart.o(i.USART_DMARxAbortCallback) refers to stm32l0xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32l0xx_hal_usart.o(i.USART_DMARxHalfCplt) refers to stm32l0xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback) for HAL_USART_RxHalfCpltCallback
    stm32l0xx_hal_usart.o(i.USART_DMATransmitCplt) refers to stm32l0xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32l0xx_hal_usart.o(i.USART_DMATxAbortCallback) refers to stm32l0xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32l0xx_hal_usart.o(i.USART_DMATxHalfCplt) refers to stm32l0xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback) for HAL_USART_TxHalfCpltCallback
    stm32l0xx_hal_usart.o(i.USART_EndTransmit_IT) refers to stm32l0xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32l0xx_hal_usart.o(i.USART_EndTransmit_IT) refers to stm32l0xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32l0xx_hal_usart.o(i.USART_RxISR_16BIT) refers to stm32l0xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32l0xx_hal_usart.o(i.USART_RxISR_16BIT) refers to stm32l0xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32l0xx_hal_usart.o(i.USART_RxISR_8BIT) refers to stm32l0xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32l0xx_hal_usart.o(i.USART_RxISR_8BIT) refers to stm32l0xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32l0xx_hal_usart.o(i.USART_SetConfig) refers to pc_protocol.o(i.__ARM_common_switch8) for __ARM_common_switch8
    stm32l0xx_hal_usart.o(i.USART_SetConfig) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32l0xx_hal_usart.o(i.USART_SetConfig) refers to uidiv.o(.text) for __aeabi_uidivmod
    stm32l0xx_hal_usart.o(i.USART_SetConfig) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32l0xx_hal_usart.o(i.USART_SetConfig) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32l0xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32l0xx_hal_flash.o(.bss) for .bss
    stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l0xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32l0xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32l0xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32l0xx_hal_flash.o(.bss) for .bss
    stm32l0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l0xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32l0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l0xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32l0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l0xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32l0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l0xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32l0xx_hal_flash.o(.bss) for .bss
    stm32l0xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32l0xx_hal_flash.o(.bss) for .bss
    stm32l0xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32l0xx_hal_flash.o(.bss) for .bss
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_BOOTBit1Config) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_BOOTBit1Config) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_BORConfig) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_BORConfig) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_ProtectedSectorsConfig) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_ProtectedSectorsConfig) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_ProtectedSectorsConfig) for FLASH_OB_ProtectedSectorsConfig
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) for FLASH_OB_BootConfig
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Erase) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Erase) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Program) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Program) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_GetWRP2) for FLASH_OB_GetWRP2
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_GetBOOTBit1) for FLASH_OB_GetBOOTBit1
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_ProtectedSectorsConfig) for FLASH_OB_ProtectedSectorsConfig
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig) for FLASH_OB_RDPConfig
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_BORConfig) for FLASH_OB_BORConfig
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l0xx_hal_flash_ex.o(i.FLASH_OB_BOOTBit1Config) for FLASH_OB_BOOTBit1Config
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_DeSelectPCROP) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_DeSelectPCROP) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_SelectPCROP) refers to stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_SelectPCROP) refers to stm32l0xx_hal_flash.o(.bss) for pFlash
    board.o(i.SysTick_Handler) refers to irq.o(i.rt_interrupt_enter) for rt_interrupt_enter
    board.o(i.SysTick_Handler) refers to clock.o(i.rt_tick_increase) for rt_tick_increase
    board.o(i.SysTick_Handler) refers to stm32l0xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    board.o(i.SysTick_Handler) refers to board.o(i.dev_delay) for dev_delay
    board.o(i.SysTick_Handler) refers to irq.o(i.rt_interrupt_leave) for rt_interrupt_leave
    board.o(i.delay_us) refers to uidiv.o(.text) for __aeabi_uidivmod
    board.o(i.dev_delay) refers to uart.o(.bss) for serial_485
    board.o(i.dev_delay) refers to main.o(.bss) for net_info
    board.o(i.rt_heap_begin_get) refers to board.o(.bss) for .bss
    board.o(i.rt_heap_end_get) refers to board.o(.bss) for .bss
    board.o(i.rt_hw_board_init) refers to stm32l0xx_hal.o(i.HAL_Init) for HAL_Init
    board.o(i.rt_hw_board_init) refers to system.o(i.system_clock_init) for system_clock_init
    board.o(i.rt_hw_board_init) refers to system_stm32l0xx.o(i.SystemCoreClockUpdate) for SystemCoreClockUpdate
    board.o(i.rt_hw_board_init) refers to stm32l0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    board.o(i.rt_hw_board_init) refers to uidiv.o(.text) for __aeabi_uidivmod
    board.o(i.rt_hw_board_init) refers to stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    board.o(i.rt_hw_board_init) refers to board.o(i.rt_heap_end_get) for rt_heap_end_get
    board.o(i.rt_hw_board_init) refers to board.o(i.rt_heap_begin_get) for rt_heap_begin_get
    board.o(i.rt_hw_board_init) refers to mem.o(i.rt_system_heap_init) for rt_system_heap_init
    clock.o(i.rt_tick_from_millisecond) refers to idiv.o(.text) for __aeabi_idivmod
    clock.o(i.rt_tick_get) refers to clock.o(.data) for .data
    clock.o(i.rt_tick_increase) refers to thread.o(i.rt_thread_self) for rt_thread_self
    clock.o(i.rt_tick_increase) refers to thread.o(i.rt_thread_yield) for rt_thread_yield
    clock.o(i.rt_tick_increase) refers to timer.o(i.rt_timer_check) for rt_timer_check
    clock.o(i.rt_tick_increase) refers to clock.o(.data) for .data
    clock.o(i.rt_tick_set) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    clock.o(i.rt_tick_set) refers to clock.o(.data) for .data
    components.o(i.$Sub$$main) refers to components.o(i.rtthread_startup) for rtthread_startup
    components.o(i.main_thread_entry) refers to main.o(i.main) for $Super$$main
    components.o(i.rt_application_init) refers to thread.o(i.rt_thread_create) for rt_thread_create
    components.o(i.rt_application_init) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    components.o(i.rt_application_init) refers to thread.o(i.rt_thread_startup) for rt_thread_startup
    components.o(i.rt_application_init) refers to components.o(i.main_thread_entry) for main_thread_entry
    components.o(i.rt_application_init) refers to components.o(.constdata) for .constdata
    components.o(i.rtthread_startup) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    components.o(i.rtthread_startup) refers to board.o(i.rt_hw_board_init) for rt_hw_board_init
    components.o(i.rtthread_startup) refers to kservice.o(i.rt_show_version) for rt_show_version
    components.o(i.rtthread_startup) refers to timer.o(i.rt_system_timer_init) for rt_system_timer_init
    components.o(i.rtthread_startup) refers to scheduler.o(i.rt_system_scheduler_init) for rt_system_scheduler_init
    components.o(i.rtthread_startup) refers to components.o(i.rt_application_init) for rt_application_init
    components.o(i.rtthread_startup) refers to timer.o(i.rt_system_timer_thread_init) for rt_system_timer_thread_init
    components.o(i.rtthread_startup) refers to idle.o(i.rt_thread_idle_init) for rt_thread_idle_init
    components.o(i.rtthread_startup) refers to scheduler.o(i.rt_system_scheduler_start) for rt_system_scheduler_start
    idle.o(i._has_defunct_thread) refers to scheduler.o(.data) for rt_thread_defunct
    idle.o(i.rt_thread_idle_delhook) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    idle.o(i.rt_thread_idle_delhook) refers to idle.o(.bss) for .bss
    idle.o(i.rt_thread_idle_entry) refers to idle.o(i.rt_thread_idle_excute) for rt_thread_idle_excute
    idle.o(i.rt_thread_idle_entry) refers to idle.o(.bss) for .bss
    idle.o(i.rt_thread_idle_excute) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    idle.o(i.rt_thread_idle_excute) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    idle.o(i.rt_thread_idle_excute) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    idle.o(i.rt_thread_idle_excute) refers to idle.o(i._has_defunct_thread) for _has_defunct_thread
    idle.o(i.rt_thread_idle_excute) refers to scheduler.o(i.rt_enter_critical) for rt_enter_critical
    idle.o(i.rt_thread_idle_excute) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    idle.o(i.rt_thread_idle_excute) refers to scheduler.o(i.rt_exit_critical) for rt_exit_critical
    idle.o(i.rt_thread_idle_excute) refers to mem.o(i.rt_free) for rt_free
    idle.o(i.rt_thread_idle_excute) refers to object.o(i.rt_object_delete) for rt_object_delete
    idle.o(i.rt_thread_idle_excute) refers to object.o(i.rt_object_detach) for rt_object_detach
    idle.o(i.rt_thread_idle_excute) refers to idle.o(.constdata) for .constdata
    idle.o(i.rt_thread_idle_excute) refers to scheduler.o(.data) for rt_thread_defunct
    idle.o(i.rt_thread_idle_gethandler) refers to idle.o(.bss) for .bss
    idle.o(i.rt_thread_idle_init) refers to kservice.o(i.rt_sprintf) for rt_sprintf
    idle.o(i.rt_thread_idle_init) refers to thread.o(i.rt_thread_init) for rt_thread_init
    idle.o(i.rt_thread_idle_init) refers to thread.o(i.rt_thread_startup) for rt_thread_startup
    idle.o(i.rt_thread_idle_init) refers to idle.o(.bss) for .bss
    idle.o(i.rt_thread_idle_init) refers to idle.o(i.rt_thread_idle_entry) for rt_thread_idle_entry
    idle.o(i.rt_thread_idle_sethook) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    idle.o(i.rt_thread_idle_sethook) refers to idle.o(.bss) for .bss
    ipc.o(i.rt_event_control) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_event_control) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_event_control) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_event_control) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_event_control) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_event_control) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_event_control) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_event_create) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_event_create) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_event_create) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_event_create) refers to object.o(i.rt_object_allocate) for rt_object_allocate
    ipc.o(i.rt_event_create) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_event_delete) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_event_delete) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_event_delete) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    ipc.o(i.rt_event_delete) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_event_delete) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_event_delete) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_event_delete) refers to object.o(i.rt_object_delete) for rt_object_delete
    ipc.o(i.rt_event_delete) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_event_delete) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_event_detach) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_event_detach) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_event_detach) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    ipc.o(i.rt_event_detach) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_event_detach) refers to object.o(i.rt_object_detach) for rt_object_detach
    ipc.o(i.rt_event_detach) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_event_detach) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_event_init) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_event_init) refers to object.o(i.rt_object_init) for rt_object_init
    ipc.o(i.rt_event_init) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_event_recv) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_event_recv) refers to thread.o(i.rt_thread_self) for rt_thread_self
    ipc.o(i.rt_event_recv) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_event_recv) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_event_recv) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_event_recv) refers to ipc.o(i.rt_ipc_list_suspend) for rt_ipc_list_suspend
    ipc.o(i.rt_event_recv) refers to timer.o(i.rt_timer_control) for rt_timer_control
    ipc.o(i.rt_event_recv) refers to timer.o(i.rt_timer_start) for rt_timer_start
    ipc.o(i.rt_event_recv) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_event_recv) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_event_recv) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_event_recv) refers to object.o(.data) for rt_object_trytake_hook
    ipc.o(i.rt_event_send) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_event_send) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_event_send) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_event_send) refers to ipc.o(i.rt_list_isempty) for rt_list_isempty
    ipc.o(i.rt_event_send) refers to thread.o(i.rt_thread_resume) for rt_thread_resume
    ipc.o(i.rt_event_send) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_event_send) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_event_send) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_event_send) refers to object.o(.data) for rt_object_put_hook
    ipc.o(i.rt_ipc_list_resume) refers to thread.o(i.rt_thread_resume) for rt_thread_resume
    ipc.o(i.rt_ipc_list_resume_all) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_ipc_list_resume_all) refers to thread.o(i.rt_thread_resume) for rt_thread_resume
    ipc.o(i.rt_ipc_list_resume_all) refers to ipc.o(i.rt_list_isempty) for rt_list_isempty
    ipc.o(i.rt_ipc_list_suspend) refers to thread.o(i.rt_thread_suspend) for rt_thread_suspend
    ipc.o(i.rt_ipc_list_suspend) refers to ipc.o(i.rt_list_insert_before) for rt_list_insert_before
    ipc.o(i.rt_mb_control) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mb_control) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mb_control) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mb_control) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_mb_control) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_mb_control) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mb_control) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mb_create) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mb_create) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_mb_create) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mb_create) refers to object.o(i.rt_object_allocate) for rt_object_allocate
    ipc.o(i.rt_mb_create) refers to mem.o(i.rt_malloc) for rt_malloc
    ipc.o(i.rt_mb_create) refers to object.o(i.rt_object_delete) for rt_object_delete
    ipc.o(i.rt_mb_create) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mb_delete) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mb_delete) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_mb_delete) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mb_delete) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mb_delete) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    ipc.o(i.rt_mb_delete) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_mb_delete) refers to mem.o(i.rt_free) for rt_free
    ipc.o(i.rt_mb_delete) refers to object.o(i.rt_object_delete) for rt_object_delete
    ipc.o(i.rt_mb_delete) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mb_delete) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mb_detach) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mb_detach) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mb_detach) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    ipc.o(i.rt_mb_detach) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_mb_detach) refers to object.o(i.rt_object_detach) for rt_object_detach
    ipc.o(i.rt_mb_detach) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mb_detach) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mb_init) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mb_init) refers to object.o(i.rt_object_init) for rt_object_init
    ipc.o(i.rt_mb_init) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mb_recv) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mb_recv) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mb_recv) refers to thread.o(i.rt_thread_self) for rt_thread_self
    ipc.o(i.rt_mb_recv) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mb_recv) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_mb_recv) refers to ipc.o(i.rt_ipc_list_suspend) for rt_ipc_list_suspend
    ipc.o(i.rt_mb_recv) refers to clock.o(i.rt_tick_get) for rt_tick_get
    ipc.o(i.rt_mb_recv) refers to timer.o(i.rt_timer_control) for rt_timer_control
    ipc.o(i.rt_mb_recv) refers to timer.o(i.rt_timer_start) for rt_timer_start
    ipc.o(i.rt_mb_recv) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_mb_recv) refers to ipc.o(i.rt_list_isempty) for rt_list_isempty
    ipc.o(i.rt_mb_recv) refers to ipc.o(i.rt_ipc_list_resume) for rt_ipc_list_resume
    ipc.o(i.rt_mb_recv) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mb_recv) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mb_recv) refers to object.o(.data) for rt_object_trytake_hook
    ipc.o(i.rt_mb_send) refers to ipc.o(i.rt_mb_send_wait) for rt_mb_send_wait
    ipc.o(i.rt_mb_send_wait) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mb_send_wait) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mb_send_wait) refers to thread.o(i.rt_thread_self) for rt_thread_self
    ipc.o(i.rt_mb_send_wait) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mb_send_wait) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_mb_send_wait) refers to ipc.o(i.rt_ipc_list_suspend) for rt_ipc_list_suspend
    ipc.o(i.rt_mb_send_wait) refers to clock.o(i.rt_tick_get) for rt_tick_get
    ipc.o(i.rt_mb_send_wait) refers to timer.o(i.rt_timer_control) for rt_timer_control
    ipc.o(i.rt_mb_send_wait) refers to timer.o(i.rt_timer_start) for rt_timer_start
    ipc.o(i.rt_mb_send_wait) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_mb_send_wait) refers to ipc.o(i.rt_list_isempty) for rt_list_isempty
    ipc.o(i.rt_mb_send_wait) refers to ipc.o(i.rt_ipc_list_resume) for rt_ipc_list_resume
    ipc.o(i.rt_mb_send_wait) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mb_send_wait) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mb_send_wait) refers to object.o(.data) for rt_object_put_hook
    ipc.o(i.rt_mq_control) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mq_control) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mq_control) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mq_control) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_mq_control) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_mq_control) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mq_control) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mq_create) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mq_create) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_mq_create) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mq_create) refers to object.o(i.rt_object_allocate) for rt_object_allocate
    ipc.o(i.rt_mq_create) refers to mem.o(i.rt_malloc) for rt_malloc
    ipc.o(i.rt_mq_create) refers to object.o(i.rt_object_delete) for rt_object_delete
    ipc.o(i.rt_mq_create) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mq_delete) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mq_delete) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_mq_delete) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mq_delete) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mq_delete) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    ipc.o(i.rt_mq_delete) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_mq_delete) refers to mem.o(i.rt_free) for rt_free
    ipc.o(i.rt_mq_delete) refers to object.o(i.rt_object_delete) for rt_object_delete
    ipc.o(i.rt_mq_delete) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mq_delete) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mq_detach) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mq_detach) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mq_detach) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    ipc.o(i.rt_mq_detach) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_mq_detach) refers to object.o(i.rt_object_detach) for rt_object_detach
    ipc.o(i.rt_mq_detach) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mq_detach) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mq_init) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mq_init) refers to object.o(i.rt_object_init) for rt_object_init
    ipc.o(i.rt_mq_init) refers to uidiv.o(.text) for __aeabi_uidivmod
    ipc.o(i.rt_mq_init) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mq_recv) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mq_recv) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mq_recv) refers to thread.o(i.rt_thread_self) for rt_thread_self
    ipc.o(i.rt_mq_recv) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mq_recv) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_mq_recv) refers to ipc.o(i.rt_ipc_list_suspend) for rt_ipc_list_suspend
    ipc.o(i.rt_mq_recv) refers to clock.o(i.rt_tick_get) for rt_tick_get
    ipc.o(i.rt_mq_recv) refers to timer.o(i.rt_timer_control) for rt_timer_control
    ipc.o(i.rt_mq_recv) refers to timer.o(i.rt_timer_start) for rt_timer_start
    ipc.o(i.rt_mq_recv) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_mq_recv) refers to kservice.o(i.rt_memcpy) for rt_memcpy
    ipc.o(i.rt_mq_recv) refers to ipc.o(i.rt_list_isempty) for rt_list_isempty
    ipc.o(i.rt_mq_recv) refers to ipc.o(i.rt_ipc_list_resume) for rt_ipc_list_resume
    ipc.o(i.rt_mq_recv) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mq_recv) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mq_recv) refers to object.o(.data) for rt_object_trytake_hook
    ipc.o(i.rt_mq_send) refers to ipc.o(i.rt_mq_send_wait) for rt_mq_send_wait
    ipc.o(i.rt_mq_send_wait) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mq_send_wait) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mq_send_wait) refers to thread.o(i.rt_thread_self) for rt_thread_self
    ipc.o(i.rt_mq_send_wait) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mq_send_wait) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_mq_send_wait) refers to ipc.o(i.rt_ipc_list_suspend) for rt_ipc_list_suspend
    ipc.o(i.rt_mq_send_wait) refers to clock.o(i.rt_tick_get) for rt_tick_get
    ipc.o(i.rt_mq_send_wait) refers to timer.o(i.rt_timer_control) for rt_timer_control
    ipc.o(i.rt_mq_send_wait) refers to timer.o(i.rt_timer_start) for rt_timer_start
    ipc.o(i.rt_mq_send_wait) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_mq_send_wait) refers to kservice.o(i.rt_memcpy) for rt_memcpy
    ipc.o(i.rt_mq_send_wait) refers to ipc.o(i.rt_list_isempty) for rt_list_isempty
    ipc.o(i.rt_mq_send_wait) refers to ipc.o(i.rt_ipc_list_resume) for rt_ipc_list_resume
    ipc.o(i.rt_mq_send_wait) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mq_send_wait) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mq_send_wait) refers to object.o(.data) for rt_object_put_hook
    ipc.o(i.rt_mq_urgent) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mq_urgent) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mq_urgent) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mq_urgent) refers to kservice.o(i.rt_memcpy) for rt_memcpy
    ipc.o(i.rt_mq_urgent) refers to ipc.o(i.rt_list_isempty) for rt_list_isempty
    ipc.o(i.rt_mq_urgent) refers to ipc.o(i.rt_ipc_list_resume) for rt_ipc_list_resume
    ipc.o(i.rt_mq_urgent) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_mq_urgent) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mq_urgent) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mq_urgent) refers to object.o(.data) for rt_object_put_hook
    ipc.o(i.rt_mutex_control) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mutex_control) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mutex_control) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mutex_control) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mutex_create) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mutex_create) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_mutex_create) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mutex_create) refers to object.o(i.rt_object_allocate) for rt_object_allocate
    ipc.o(i.rt_mutex_create) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mutex_delete) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mutex_delete) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_mutex_delete) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mutex_delete) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mutex_delete) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    ipc.o(i.rt_mutex_delete) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_mutex_delete) refers to object.o(i.rt_object_delete) for rt_object_delete
    ipc.o(i.rt_mutex_delete) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mutex_delete) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mutex_detach) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mutex_detach) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mutex_detach) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    ipc.o(i.rt_mutex_detach) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_mutex_detach) refers to object.o(i.rt_object_detach) for rt_object_detach
    ipc.o(i.rt_mutex_detach) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mutex_detach) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mutex_init) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mutex_init) refers to object.o(i.rt_object_init) for rt_object_init
    ipc.o(i.rt_mutex_init) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mutex_release) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mutex_release) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mutex_release) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mutex_release) refers to thread.o(i.rt_thread_self) for rt_thread_self
    ipc.o(i.rt_mutex_release) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_mutex_release) refers to thread.o(i.rt_thread_control) for rt_thread_control
    ipc.o(i.rt_mutex_release) refers to ipc.o(i.rt_list_isempty) for rt_list_isempty
    ipc.o(i.rt_mutex_release) refers to ipc.o(i.rt_ipc_list_resume) for rt_ipc_list_resume
    ipc.o(i.rt_mutex_release) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_mutex_release) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mutex_release) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mutex_release) refers to object.o(.data) for rt_object_put_hook
    ipc.o(i.rt_mutex_take) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_mutex_take) refers to thread.o(i.rt_thread_self) for rt_thread_self
    ipc.o(i.rt_mutex_take) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_mutex_take) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_mutex_take) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_mutex_take) refers to thread.o(i.rt_thread_control) for rt_thread_control
    ipc.o(i.rt_mutex_take) refers to ipc.o(i.rt_ipc_list_suspend) for rt_ipc_list_suspend
    ipc.o(i.rt_mutex_take) refers to timer.o(i.rt_timer_control) for rt_timer_control
    ipc.o(i.rt_mutex_take) refers to timer.o(i.rt_timer_start) for rt_timer_start
    ipc.o(i.rt_mutex_take) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_mutex_take) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_mutex_take) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_mutex_take) refers to object.o(.data) for rt_object_trytake_hook
    ipc.o(i.rt_sem_control) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_sem_control) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_sem_control) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_sem_control) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_sem_control) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_sem_control) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_sem_control) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_sem_create) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_sem_create) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_sem_create) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_sem_create) refers to object.o(i.rt_object_allocate) for rt_object_allocate
    ipc.o(i.rt_sem_create) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_sem_delete) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_sem_delete) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_sem_delete) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_sem_delete) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_sem_delete) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    ipc.o(i.rt_sem_delete) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_sem_delete) refers to object.o(i.rt_object_delete) for rt_object_delete
    ipc.o(i.rt_sem_delete) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_sem_delete) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_sem_detach) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_sem_detach) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_sem_detach) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    ipc.o(i.rt_sem_detach) refers to ipc.o(i.rt_ipc_list_resume_all) for rt_ipc_list_resume_all
    ipc.o(i.rt_sem_detach) refers to object.o(i.rt_object_detach) for rt_object_detach
    ipc.o(i.rt_sem_detach) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_sem_detach) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_sem_init) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_sem_init) refers to object.o(i.rt_object_init) for rt_object_init
    ipc.o(i.rt_sem_init) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_sem_release) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_sem_release) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_sem_release) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_sem_release) refers to ipc.o(i.rt_list_isempty) for rt_list_isempty
    ipc.o(i.rt_sem_release) refers to ipc.o(i.rt_ipc_list_resume) for rt_ipc_list_resume
    ipc.o(i.rt_sem_release) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_sem_release) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_sem_release) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_sem_release) refers to object.o(.data) for rt_object_put_hook
    ipc.o(i.rt_sem_take) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    ipc.o(i.rt_sem_take) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    ipc.o(i.rt_sem_take) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    ipc.o(i.rt_sem_take) refers to thread.o(i.rt_thread_self) for rt_thread_self
    ipc.o(i.rt_sem_take) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    ipc.o(i.rt_sem_take) refers to ipc.o(i.rt_ipc_list_suspend) for rt_ipc_list_suspend
    ipc.o(i.rt_sem_take) refers to timer.o(i.rt_timer_control) for rt_timer_control
    ipc.o(i.rt_sem_take) refers to timer.o(i.rt_timer_start) for rt_timer_start
    ipc.o(i.rt_sem_take) refers to scheduler.o(i.rt_schedule) for rt_schedule
    ipc.o(i.rt_sem_take) refers to ipc.o(.constdata) for .constdata
    ipc.o(i.rt_sem_take) refers to ipc.o(.conststring) for .conststring
    ipc.o(i.rt_sem_take) refers to object.o(.data) for rt_object_trytake_hook
    ipc.o(i.rt_sem_trytake) refers to ipc.o(i.rt_sem_take) for rt_sem_take
    irq.o(i.rt_interrupt_enter) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    irq.o(i.rt_interrupt_enter) refers to irq.o(.data) for .data
    irq.o(i.rt_interrupt_enter_sethook) refers to irq.o(.data) for .data
    irq.o(i.rt_interrupt_get_nest) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    irq.o(i.rt_interrupt_get_nest) refers to irq.o(.data) for .data
    irq.o(i.rt_interrupt_leave) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    irq.o(i.rt_interrupt_leave) refers to irq.o(.data) for .data
    irq.o(i.rt_interrupt_leave_sethook) refers to irq.o(.data) for .data
    kservice.o(i.__rt_ffs) refers to kservice.o(.constdata) for .constdata
    kservice.o(i._rt_errno) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    kservice.o(i._rt_errno) refers to thread.o(i.rt_thread_self) for rt_thread_self
    kservice.o(i._rt_errno) refers to kservice.o(.data) for .data
    kservice.o(i.print_number) refers to uidiv.o(.text) for __aeabi_uidivmod
    kservice.o(i.print_number) refers to kservice.o(.constdata) for .constdata
    kservice.o(i.rt_assert_handler) refers to kservice.o(.data) for .data
    kservice.o(i.rt_assert_set_hook) refers to kservice.o(.data) for .data
    kservice.o(i.rt_free_align) refers to mem.o(i.rt_free) for rt_free
    kservice.o(i.rt_get_errno) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    kservice.o(i.rt_get_errno) refers to thread.o(i.rt_thread_self) for rt_thread_self
    kservice.o(i.rt_get_errno) refers to kservice.o(.data) for .data
    kservice.o(i.rt_malloc_align) refers to mem.o(i.rt_malloc) for rt_malloc
    kservice.o(i.rt_set_errno) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    kservice.o(i.rt_set_errno) refers to thread.o(i.rt_thread_self) for rt_thread_self
    kservice.o(i.rt_set_errno) refers to kservice.o(.data) for .data
    kservice.o(i.rt_snprintf) refers to kservice.o(i.rt_vsnprintf) for rt_vsnprintf
    kservice.o(i.rt_sprintf) refers to kservice.o(i.rt_vsprintf) for rt_vsprintf
    kservice.o(i.rt_strdup) refers to kservice.o(i.rt_strlen) for rt_strlen
    kservice.o(i.rt_strdup) refers to mem.o(i.rt_malloc) for rt_malloc
    kservice.o(i.rt_strdup) refers to kservice.o(i.rt_memcpy) for rt_memcpy
    kservice.o(i.rt_strstr) refers to kservice.o(i.rt_strlen) for rt_strlen
    kservice.o(i.rt_strstr) refers to kservice.o(i.rt_memcmp) for rt_memcmp
    kservice.o(i.rt_vsnprintf) refers to kservice.o(i.skip_atoi) for skip_atoi
    kservice.o(i.rt_vsnprintf) refers to kservice.o(i.rt_strlen) for rt_strlen
    kservice.o(i.rt_vsnprintf) refers to kservice.o(i.print_number) for print_number
    kservice.o(i.rt_vsprintf) refers to kservice.o(i.rt_vsnprintf) for rt_vsnprintf
    mem.o(i.plug_holes) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    mem.o(i.plug_holes) refers to mem.o(.data) for .data
    mem.o(i.plug_holes) refers to mem.o(.constdata) for .constdata
    mem.o(i.rt_calloc) refers to mem.o(i.rt_malloc) for rt_malloc
    mem.o(i.rt_calloc) refers to kservice.o(i.rt_memset) for rt_memset
    mem.o(i.rt_free) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    mem.o(i.rt_free) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    mem.o(i.rt_free) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    mem.o(i.rt_free) refers to ipc.o(i.rt_sem_take) for rt_sem_take
    mem.o(i.rt_free) refers to mem.o(i.plug_holes) for plug_holes
    mem.o(i.rt_free) refers to ipc.o(i.rt_sem_release) for rt_sem_release
    mem.o(i.rt_free) refers to mem.o(.constdata) for .constdata
    mem.o(i.rt_free) refers to mem.o(.data) for .data
    mem.o(i.rt_free) refers to mem.o(.conststring) for .conststring
    mem.o(i.rt_free) refers to mem.o(.bss) for .bss
    mem.o(i.rt_free_sethook) refers to mem.o(.data) for .data
    mem.o(i.rt_malloc) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    mem.o(i.rt_malloc) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    mem.o(i.rt_malloc) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    mem.o(i.rt_malloc) refers to ipc.o(i.rt_sem_take) for rt_sem_take
    mem.o(i.rt_malloc) refers to ipc.o(i.rt_sem_release) for rt_sem_release
    mem.o(i.rt_malloc) refers to mem.o(.constdata) for .constdata
    mem.o(i.rt_malloc) refers to mem.o(.data) for .data
    mem.o(i.rt_malloc) refers to mem.o(.bss) for .bss
    mem.o(i.rt_malloc) refers to mem.o(.conststring) for .conststring
    mem.o(i.rt_malloc_sethook) refers to mem.o(.data) for .data
    mem.o(i.rt_memory_info) refers to mem.o(.data) for .data
    mem.o(i.rt_realloc) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    mem.o(i.rt_realloc) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    mem.o(i.rt_realloc) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    mem.o(i.rt_realloc) refers to ipc.o(i.rt_sem_take) for rt_sem_take
    mem.o(i.rt_realloc) refers to ipc.o(i.rt_sem_release) for rt_sem_release
    mem.o(i.rt_realloc) refers to mem.o(i.rt_free) for rt_free
    mem.o(i.rt_realloc) refers to mem.o(i.rt_malloc) for rt_malloc
    mem.o(i.rt_realloc) refers to mem.o(i.plug_holes) for plug_holes
    mem.o(i.rt_realloc) refers to kservice.o(i.rt_memcpy) for rt_memcpy
    mem.o(i.rt_realloc) refers to mem.o(.constdata) for .constdata
    mem.o(i.rt_realloc) refers to mem.o(.data) for .data
    mem.o(i.rt_realloc) refers to mem.o(.bss) for .bss
    mem.o(i.rt_system_heap_init) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    mem.o(i.rt_system_heap_init) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    mem.o(i.rt_system_heap_init) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    mem.o(i.rt_system_heap_init) refers to ipc.o(i.rt_sem_init) for rt_sem_init
    mem.o(i.rt_system_heap_init) refers to mem.o(.constdata) for .constdata
    mem.o(i.rt_system_heap_init) refers to mem.o(.data) for .data
    mem.o(i.rt_system_heap_init) refers to mem.o(.bss) for .bss
    mempool.o(i.rt_mp_alloc) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    mempool.o(i.rt_mp_alloc) refers to thread.o(i.rt_thread_self) for rt_thread_self
    mempool.o(i.rt_mp_alloc) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    mempool.o(i.rt_mp_alloc) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    mempool.o(i.rt_mp_alloc) refers to thread.o(i.rt_thread_suspend) for rt_thread_suspend
    mempool.o(i.rt_mp_alloc) refers to clock.o(i.rt_tick_get) for rt_tick_get
    mempool.o(i.rt_mp_alloc) refers to timer.o(i.rt_timer_control) for rt_timer_control
    mempool.o(i.rt_mp_alloc) refers to timer.o(i.rt_timer_start) for rt_timer_start
    mempool.o(i.rt_mp_alloc) refers to scheduler.o(i.rt_schedule) for rt_schedule
    mempool.o(i.rt_mp_alloc) refers to kservice.o(i.rt_set_errno) for rt_set_errno
    mempool.o(i.rt_mp_alloc) refers to mempool.o(.constdata) for .constdata
    mempool.o(i.rt_mp_alloc) refers to mempool.o(.data) for .data
    mempool.o(i.rt_mp_alloc_sethook) refers to mempool.o(.data) for .data
    mempool.o(i.rt_mp_create) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    mempool.o(i.rt_mp_create) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    mempool.o(i.rt_mp_create) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    mempool.o(i.rt_mp_create) refers to object.o(i.rt_object_allocate) for rt_object_allocate
    mempool.o(i.rt_mp_create) refers to mem.o(i.rt_malloc) for rt_malloc
    mempool.o(i.rt_mp_create) refers to object.o(i.rt_object_delete) for rt_object_delete
    mempool.o(i.rt_mp_create) refers to mempool.o(.constdata) for .constdata
    mempool.o(i.rt_mp_delete) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    mempool.o(i.rt_mp_delete) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    mempool.o(i.rt_mp_delete) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    mempool.o(i.rt_mp_delete) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    mempool.o(i.rt_mp_delete) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    mempool.o(i.rt_mp_delete) refers to thread.o(i.rt_thread_resume) for rt_thread_resume
    mempool.o(i.rt_mp_delete) refers to mempool.o(i.rt_list_isempty) for rt_list_isempty
    mempool.o(i.rt_mp_delete) refers to mem.o(i.rt_free) for rt_free
    mempool.o(i.rt_mp_delete) refers to object.o(i.rt_object_delete) for rt_object_delete
    mempool.o(i.rt_mp_delete) refers to mempool.o(.constdata) for .constdata
    mempool.o(i.rt_mp_detach) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    mempool.o(i.rt_mp_detach) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    mempool.o(i.rt_mp_detach) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    mempool.o(i.rt_mp_detach) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    mempool.o(i.rt_mp_detach) refers to thread.o(i.rt_thread_resume) for rt_thread_resume
    mempool.o(i.rt_mp_detach) refers to mempool.o(i.rt_list_isempty) for rt_list_isempty
    mempool.o(i.rt_mp_detach) refers to object.o(i.rt_object_detach) for rt_object_detach
    mempool.o(i.rt_mp_detach) refers to mempool.o(.constdata) for .constdata
    mempool.o(i.rt_mp_free) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    mempool.o(i.rt_mp_free) refers to mempool.o(i.rt_list_isempty) for rt_list_isempty
    mempool.o(i.rt_mp_free) refers to thread.o(i.rt_thread_resume) for rt_thread_resume
    mempool.o(i.rt_mp_free) refers to scheduler.o(i.rt_schedule) for rt_schedule
    mempool.o(i.rt_mp_free) refers to mempool.o(.data) for .data
    mempool.o(i.rt_mp_free_sethook) refers to mempool.o(.data) for .data
    mempool.o(i.rt_mp_init) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    mempool.o(i.rt_mp_init) refers to object.o(i.rt_object_init) for rt_object_init
    mempool.o(i.rt_mp_init) refers to uidiv.o(.text) for __aeabi_uidivmod
    mempool.o(i.rt_mp_init) refers to mempool.o(.constdata) for .constdata
    object.o(i.rt_object_allocate) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    object.o(i.rt_object_allocate) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    object.o(i.rt_object_allocate) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    object.o(i.rt_object_allocate) refers to object.o(i.rt_object_get_information) for rt_object_get_information
    object.o(i.rt_object_allocate) refers to mem.o(i.rt_malloc) for rt_malloc
    object.o(i.rt_object_allocate) refers to kservice.o(i.rt_memset) for rt_memset
    object.o(i.rt_object_allocate) refers to kservice.o(i.rt_strncpy) for rt_strncpy
    object.o(i.rt_object_allocate) refers to object.o(i.rt_list_insert_after) for rt_list_insert_after
    object.o(i.rt_object_allocate) refers to object.o(.constdata) for .constdata
    object.o(i.rt_object_allocate) refers to object.o(.data) for .data
    object.o(i.rt_object_attach_sethook) refers to object.o(.data) for .data
    object.o(i.rt_object_delete) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    object.o(i.rt_object_delete) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    object.o(i.rt_object_delete) refers to object.o(i.rt_list_remove) for rt_list_remove
    object.o(i.rt_object_delete) refers to mem.o(i.rt_free) for rt_free
    object.o(i.rt_object_delete) refers to object.o(.constdata) for .constdata
    object.o(i.rt_object_delete) refers to object.o(.data) for .data
    object.o(i.rt_object_detach) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    object.o(i.rt_object_detach) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    object.o(i.rt_object_detach) refers to object.o(i.rt_list_remove) for rt_list_remove
    object.o(i.rt_object_detach) refers to object.o(.constdata) for .constdata
    object.o(i.rt_object_detach) refers to object.o(.data) for .data
    object.o(i.rt_object_detach_sethook) refers to object.o(.data) for .data
    object.o(i.rt_object_find) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    object.o(i.rt_object_find) refers to irq.o(i.rt_interrupt_get_nest) for rt_interrupt_get_nest
    object.o(i.rt_object_find) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    object.o(i.rt_object_find) refers to scheduler.o(i.rt_enter_critical) for rt_enter_critical
    object.o(i.rt_object_find) refers to object.o(i.rt_object_get_information) for rt_object_get_information
    object.o(i.rt_object_find) refers to kservice.o(i.rt_strncmp) for rt_strncmp
    object.o(i.rt_object_find) refers to scheduler.o(i.rt_exit_critical) for rt_exit_critical
    object.o(i.rt_object_find) refers to object.o(.constdata) for .constdata
    object.o(i.rt_object_get_information) refers to object.o(.data) for .data
    object.o(i.rt_object_get_type) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    object.o(i.rt_object_get_type) refers to object.o(.constdata) for .constdata
    object.o(i.rt_object_init) refers to object.o(i.rt_object_get_information) for rt_object_get_information
    object.o(i.rt_object_init) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    object.o(i.rt_object_init) refers to scheduler.o(i.rt_enter_critical) for rt_enter_critical
    object.o(i.rt_object_init) refers to scheduler.o(i.rt_exit_critical) for rt_exit_critical
    object.o(i.rt_object_init) refers to kservice.o(i.rt_strncpy) for rt_strncpy
    object.o(i.rt_object_init) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    object.o(i.rt_object_init) refers to object.o(i.rt_list_insert_after) for rt_list_insert_after
    object.o(i.rt_object_init) refers to object.o(.constdata) for .constdata
    object.o(i.rt_object_init) refers to object.o(.data) for .data
    object.o(i.rt_object_is_systemobject) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    object.o(i.rt_object_is_systemobject) refers to object.o(.constdata) for .constdata
    object.o(i.rt_object_put_sethook) refers to object.o(.data) for .data
    object.o(i.rt_object_take_sethook) refers to object.o(.data) for .data
    object.o(i.rt_object_trytake_sethook) refers to object.o(.data) for .data
    scheduler.o(i._get_highest_priority_thread) refers to kservice.o(i.__rt_ffs) for __rt_ffs
    scheduler.o(i._get_highest_priority_thread) refers to scheduler.o(.data) for .data
    scheduler.o(i._get_highest_priority_thread) refers to scheduler.o(.bss) for .bss
    scheduler.o(i._rt_scheduler_stack_check) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    scheduler.o(i._rt_scheduler_stack_check) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    scheduler.o(i._rt_scheduler_stack_check) refers to scheduler.o(.constdata) for .constdata
    scheduler.o(i.rt_critical_level) refers to scheduler.o(.data) for .data
    scheduler.o(i.rt_enter_critical) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    scheduler.o(i.rt_enter_critical) refers to scheduler.o(.data) for .data
    scheduler.o(i.rt_exit_critical) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    scheduler.o(i.rt_exit_critical) refers to scheduler.o(i.rt_schedule) for rt_schedule
    scheduler.o(i.rt_exit_critical) refers to scheduler.o(.data) for .data
    scheduler.o(i.rt_schedule) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    scheduler.o(i.rt_schedule) refers to scheduler.o(i._get_highest_priority_thread) for _get_highest_priority_thread
    scheduler.o(i.rt_schedule) refers to scheduler.o(i.rt_schedule_insert_thread) for rt_schedule_insert_thread
    scheduler.o(i.rt_schedule) refers to scheduler.o(i.rt_schedule_remove_thread) for rt_schedule_remove_thread
    scheduler.o(i.rt_schedule) refers to scheduler.o(i._rt_scheduler_stack_check) for _rt_scheduler_stack_check
    scheduler.o(i.rt_schedule) refers to scheduler.o(.data) for .data
    scheduler.o(i.rt_schedule) refers to irq.o(.data) for rt_interrupt_nest
    scheduler.o(i.rt_schedule_insert_thread) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    scheduler.o(i.rt_schedule_insert_thread) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    scheduler.o(i.rt_schedule_insert_thread) refers to scheduler.o(.constdata) for .constdata
    scheduler.o(i.rt_schedule_insert_thread) refers to scheduler.o(.data) for .data
    scheduler.o(i.rt_schedule_insert_thread) refers to scheduler.o(.bss) for .bss
    scheduler.o(i.rt_schedule_remove_thread) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    scheduler.o(i.rt_schedule_remove_thread) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    scheduler.o(i.rt_schedule_remove_thread) refers to scheduler.o(.constdata) for .constdata
    scheduler.o(i.rt_schedule_remove_thread) refers to scheduler.o(.bss) for .bss
    scheduler.o(i.rt_schedule_remove_thread) refers to scheduler.o(.data) for .data
    scheduler.o(i.rt_scheduler_sethook) refers to scheduler.o(.data) for .data
    scheduler.o(i.rt_system_scheduler_init) refers to scheduler.o(.data) for .data
    scheduler.o(i.rt_system_scheduler_init) refers to scheduler.o(.bss) for .bss
    scheduler.o(i.rt_system_scheduler_start) refers to scheduler.o(i._get_highest_priority_thread) for _get_highest_priority_thread
    scheduler.o(i.rt_system_scheduler_start) refers to scheduler.o(i.rt_schedule_remove_thread) for rt_schedule_remove_thread
    scheduler.o(i.rt_system_scheduler_start) refers to context_rvds.o(.text) for rt_hw_context_switch_to
    scheduler.o(i.rt_system_scheduler_start) refers to scheduler.o(.data) for .data
    thread.o(i._rt_thread_init) refers to kservice.o(i.rt_memset) for rt_memset
    thread.o(i._rt_thread_init) refers to cpuport.o(i.rt_hw_stack_init) for rt_hw_stack_init
    thread.o(i._rt_thread_init) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    thread.o(i._rt_thread_init) refers to timer.o(i.rt_timer_init) for rt_timer_init
    thread.o(i._rt_thread_init) refers to thread.o(i.rt_thread_exit) for rt_thread_exit
    thread.o(i._rt_thread_init) refers to thread.o(.constdata) for .constdata
    thread.o(i._rt_thread_init) refers to thread.o(i.rt_thread_timeout) for rt_thread_timeout
    thread.o(i._rt_thread_init) refers to thread.o(.data) for .data
    thread.o(i.rt_thread_control) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    thread.o(i.rt_thread_control) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    thread.o(i.rt_thread_control) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    thread.o(i.rt_thread_control) refers to scheduler.o(i.rt_schedule_remove_thread) for rt_schedule_remove_thread
    thread.o(i.rt_thread_control) refers to scheduler.o(i.rt_schedule_insert_thread) for rt_schedule_insert_thread
    thread.o(i.rt_thread_control) refers to thread.o(i.rt_thread_startup) for rt_thread_startup
    thread.o(i.rt_thread_control) refers to thread.o(i.rt_thread_delete) for rt_thread_delete
    thread.o(i.rt_thread_control) refers to thread.o(.constdata) for .constdata
    thread.o(i.rt_thread_control) refers to thread.o(.conststring) for .conststring
    thread.o(i.rt_thread_create) refers to object.o(i.rt_object_allocate) for rt_object_allocate
    thread.o(i.rt_thread_create) refers to mem.o(i.rt_malloc) for rt_malloc
    thread.o(i.rt_thread_create) refers to thread.o(i._rt_thread_init) for _rt_thread_init
    thread.o(i.rt_thread_create) refers to object.o(i.rt_object_delete) for rt_object_delete
    thread.o(i.rt_thread_delay) refers to thread.o(i.rt_thread_sleep) for rt_thread_sleep
    thread.o(i.rt_thread_delay_until) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    thread.o(i.rt_thread_delay_until) refers to thread.o(i.rt_thread_self) for rt_thread_self
    thread.o(i.rt_thread_delay_until) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    thread.o(i.rt_thread_delay_until) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    thread.o(i.rt_thread_delay_until) refers to clock.o(i.rt_tick_get) for rt_tick_get
    thread.o(i.rt_thread_delay_until) refers to thread.o(i.rt_thread_suspend) for rt_thread_suspend
    thread.o(i.rt_thread_delay_until) refers to timer.o(i.rt_timer_control) for rt_timer_control
    thread.o(i.rt_thread_delay_until) refers to timer.o(i.rt_timer_start) for rt_timer_start
    thread.o(i.rt_thread_delay_until) refers to scheduler.o(i.rt_schedule) for rt_schedule
    thread.o(i.rt_thread_delay_until) refers to thread.o(.constdata) for .constdata
    thread.o(i.rt_thread_delay_until) refers to thread.o(.conststring) for .conststring
    thread.o(i.rt_thread_delete) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    thread.o(i.rt_thread_delete) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    thread.o(i.rt_thread_delete) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    thread.o(i.rt_thread_delete) refers to scheduler.o(i.rt_schedule_remove_thread) for rt_schedule_remove_thread
    thread.o(i.rt_thread_delete) refers to timer.o(i.rt_timer_detach) for rt_timer_detach
    thread.o(i.rt_thread_delete) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    thread.o(i.rt_thread_delete) refers to thread.o(i.rt_list_insert_after) for rt_list_insert_after
    thread.o(i.rt_thread_delete) refers to thread.o(.constdata) for .constdata
    thread.o(i.rt_thread_delete) refers to thread.o(.conststring) for .conststring
    thread.o(i.rt_thread_delete) refers to scheduler.o(.data) for rt_thread_defunct
    thread.o(i.rt_thread_detach) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    thread.o(i.rt_thread_detach) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    thread.o(i.rt_thread_detach) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    thread.o(i.rt_thread_detach) refers to scheduler.o(i.rt_schedule_remove_thread) for rt_schedule_remove_thread
    thread.o(i.rt_thread_detach) refers to timer.o(i.rt_timer_detach) for rt_timer_detach
    thread.o(i.rt_thread_detach) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    thread.o(i.rt_thread_detach) refers to thread.o(i.rt_list_insert_after) for rt_list_insert_after
    thread.o(i.rt_thread_detach) refers to object.o(i.rt_object_detach) for rt_object_detach
    thread.o(i.rt_thread_detach) refers to thread.o(.constdata) for .constdata
    thread.o(i.rt_thread_detach) refers to thread.o(.conststring) for .conststring
    thread.o(i.rt_thread_detach) refers to scheduler.o(.data) for rt_thread_defunct
    thread.o(i.rt_thread_exit) refers to thread.o(i.rt_thread_self) for rt_thread_self
    thread.o(i.rt_thread_exit) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    thread.o(i.rt_thread_exit) refers to scheduler.o(i.rt_schedule_remove_thread) for rt_schedule_remove_thread
    thread.o(i.rt_thread_exit) refers to timer.o(i.rt_timer_detach) for rt_timer_detach
    thread.o(i.rt_thread_exit) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    thread.o(i.rt_thread_exit) refers to thread.o(i.rt_list_insert_after) for rt_list_insert_after
    thread.o(i.rt_thread_exit) refers to scheduler.o(i.rt_schedule) for rt_schedule
    thread.o(i.rt_thread_exit) refers to object.o(i.rt_object_detach) for rt_object_detach
    thread.o(i.rt_thread_exit) refers to scheduler.o(.data) for rt_thread_defunct
    thread.o(i.rt_thread_find) refers to thread.o(i.rt_thread_self) for rt_thread_self
    thread.o(i.rt_thread_find) refers to scheduler.o(i.rt_enter_critical) for rt_enter_critical
    thread.o(i.rt_thread_find) refers to object.o(i.rt_object_get_information) for rt_object_get_information
    thread.o(i.rt_thread_find) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    thread.o(i.rt_thread_find) refers to kservice.o(i.rt_strncmp) for rt_strncmp
    thread.o(i.rt_thread_find) refers to scheduler.o(i.rt_exit_critical) for rt_exit_critical
    thread.o(i.rt_thread_find) refers to thread.o(.constdata) for .constdata
    thread.o(i.rt_thread_init) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    thread.o(i.rt_thread_init) refers to object.o(i.rt_object_init) for rt_object_init
    thread.o(i.rt_thread_init) refers to thread.o(i._rt_thread_init) for _rt_thread_init
    thread.o(i.rt_thread_init) refers to thread.o(.constdata) for .constdata
    thread.o(i.rt_thread_inited_sethook) refers to thread.o(.data) for .data
    thread.o(i.rt_thread_mdelay) refers to clock.o(i.rt_tick_from_millisecond) for rt_tick_from_millisecond
    thread.o(i.rt_thread_mdelay) refers to thread.o(i.rt_thread_sleep) for rt_thread_sleep
    thread.o(i.rt_thread_resume) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    thread.o(i.rt_thread_resume) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    thread.o(i.rt_thread_resume) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    thread.o(i.rt_thread_resume) refers to thread.o(i.rt_list_remove) for rt_list_remove
    thread.o(i.rt_thread_resume) refers to timer.o(i.rt_timer_stop) for rt_timer_stop
    thread.o(i.rt_thread_resume) refers to scheduler.o(i.rt_schedule_insert_thread) for rt_schedule_insert_thread
    thread.o(i.rt_thread_resume) refers to thread.o(.constdata) for .constdata
    thread.o(i.rt_thread_resume) refers to thread.o(.conststring) for .conststring
    thread.o(i.rt_thread_resume) refers to thread.o(.data) for .data
    thread.o(i.rt_thread_resume_sethook) refers to thread.o(.data) for .data
    thread.o(i.rt_thread_self) refers to scheduler.o(.data) for rt_current_thread
    thread.o(i.rt_thread_sleep) refers to thread.o(i.rt_thread_self) for rt_thread_self
    thread.o(i.rt_thread_sleep) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    thread.o(i.rt_thread_sleep) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    thread.o(i.rt_thread_sleep) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    thread.o(i.rt_thread_sleep) refers to thread.o(i.rt_thread_suspend) for rt_thread_suspend
    thread.o(i.rt_thread_sleep) refers to timer.o(i.rt_timer_control) for rt_timer_control
    thread.o(i.rt_thread_sleep) refers to timer.o(i.rt_timer_start) for rt_timer_start
    thread.o(i.rt_thread_sleep) refers to scheduler.o(i.rt_schedule) for rt_schedule
    thread.o(i.rt_thread_sleep) refers to thread.o(.constdata) for .constdata
    thread.o(i.rt_thread_sleep) refers to thread.o(.conststring) for .conststring
    thread.o(i.rt_thread_startup) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    thread.o(i.rt_thread_startup) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    thread.o(i.rt_thread_startup) refers to thread.o(i.rt_thread_resume) for rt_thread_resume
    thread.o(i.rt_thread_startup) refers to thread.o(i.rt_thread_self) for rt_thread_self
    thread.o(i.rt_thread_startup) refers to scheduler.o(i.rt_schedule) for rt_schedule
    thread.o(i.rt_thread_startup) refers to thread.o(.constdata) for .constdata
    thread.o(i.rt_thread_startup) refers to thread.o(.conststring) for .conststring
    thread.o(i.rt_thread_suspend) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    thread.o(i.rt_thread_suspend) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    thread.o(i.rt_thread_suspend) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    thread.o(i.rt_thread_suspend) refers to thread.o(i.rt_thread_self) for rt_thread_self
    thread.o(i.rt_thread_suspend) refers to scheduler.o(i.rt_schedule_remove_thread) for rt_schedule_remove_thread
    thread.o(i.rt_thread_suspend) refers to timer.o(i.rt_timer_stop) for rt_timer_stop
    thread.o(i.rt_thread_suspend) refers to thread.o(.constdata) for .constdata
    thread.o(i.rt_thread_suspend) refers to thread.o(.conststring) for .conststring
    thread.o(i.rt_thread_suspend) refers to thread.o(.data) for .data
    thread.o(i.rt_thread_suspend_sethook) refers to thread.o(.data) for .data
    thread.o(i.rt_thread_timeout) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    thread.o(i.rt_thread_timeout) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    thread.o(i.rt_thread_timeout) refers to thread.o(i.rt_list_remove) for rt_list_remove
    thread.o(i.rt_thread_timeout) refers to scheduler.o(i.rt_schedule_insert_thread) for rt_schedule_insert_thread
    thread.o(i.rt_thread_timeout) refers to scheduler.o(i.rt_schedule) for rt_schedule
    thread.o(i.rt_thread_timeout) refers to thread.o(.constdata) for .constdata
    thread.o(i.rt_thread_timeout) refers to thread.o(.conststring) for .conststring
    thread.o(i.rt_thread_yield) refers to scheduler.o(i.rt_schedule) for rt_schedule
    timer.o(i.rt_system_timer_init) refers to timer.o(.data) for .data
    timer.o(i.rt_timer_check) refers to clock.o(i.rt_tick_get) for rt_tick_get
    timer.o(i.rt_timer_check) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    timer.o(i.rt_timer_check) refers to timer.o(i._rt_timer_remove) for _rt_timer_remove
    timer.o(i.rt_timer_check) refers to timer.o(i.rt_timer_start) for rt_timer_start
    timer.o(i.rt_timer_check) refers to timer.o(i.rt_list_isempty) for rt_list_isempty
    timer.o(i.rt_timer_check) refers to timer.o(.data) for .data
    timer.o(i.rt_timer_control) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    timer.o(i.rt_timer_control) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    timer.o(i.rt_timer_control) refers to timer.o(.constdata) for .constdata
    timer.o(i.rt_timer_create) refers to object.o(i.rt_object_allocate) for rt_object_allocate
    timer.o(i.rt_timer_create) refers to timer.o(i._rt_timer_init) for _rt_timer_init
    timer.o(i.rt_timer_delete) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    timer.o(i.rt_timer_delete) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    timer.o(i.rt_timer_delete) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    timer.o(i.rt_timer_delete) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    timer.o(i.rt_timer_delete) refers to timer.o(i._rt_timer_remove) for _rt_timer_remove
    timer.o(i.rt_timer_delete) refers to object.o(i.rt_object_delete) for rt_object_delete
    timer.o(i.rt_timer_delete) refers to timer.o(.constdata) for .constdata
    timer.o(i.rt_timer_detach) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    timer.o(i.rt_timer_detach) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    timer.o(i.rt_timer_detach) refers to object.o(i.rt_object_is_systemobject) for rt_object_is_systemobject
    timer.o(i.rt_timer_detach) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    timer.o(i.rt_timer_detach) refers to timer.o(i._rt_timer_remove) for _rt_timer_remove
    timer.o(i.rt_timer_detach) refers to object.o(i.rt_object_detach) for rt_object_detach
    timer.o(i.rt_timer_detach) refers to timer.o(.constdata) for .constdata
    timer.o(i.rt_timer_enter_sethook) refers to timer.o(.data) for .data
    timer.o(i.rt_timer_exit_sethook) refers to timer.o(.data) for .data
    timer.o(i.rt_timer_init) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    timer.o(i.rt_timer_init) refers to object.o(i.rt_object_init) for rt_object_init
    timer.o(i.rt_timer_init) refers to timer.o(i._rt_timer_init) for _rt_timer_init
    timer.o(i.rt_timer_init) refers to timer.o(.constdata) for .constdata
    timer.o(i.rt_timer_list_next_timeout) refers to timer.o(i.rt_list_isempty) for rt_list_isempty
    timer.o(i.rt_timer_next_timeout_tick) refers to timer.o(i.rt_timer_list_next_timeout) for rt_timer_list_next_timeout
    timer.o(i.rt_timer_next_timeout_tick) refers to timer.o(.data) for .data
    timer.o(i.rt_timer_start) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    timer.o(i.rt_timer_start) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    timer.o(i.rt_timer_start) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    timer.o(i.rt_timer_start) refers to timer.o(i._rt_timer_remove) for _rt_timer_remove
    timer.o(i.rt_timer_start) refers to clock.o(i.rt_tick_get) for rt_tick_get
    timer.o(i.rt_timer_start) refers to timer.o(i.rt_list_insert_after) for rt_list_insert_after
    timer.o(i.rt_timer_start) refers to timer.o(.constdata) for .constdata
    timer.o(i.rt_timer_start) refers to object.o(.data) for rt_object_take_hook
    timer.o(i.rt_timer_start) refers to timer.o(.data) for .data
    timer.o(i.rt_timer_stop) refers to kservice.o(i.rt_assert_handler) for rt_assert_handler
    timer.o(i.rt_timer_stop) refers to object.o(i.rt_object_get_type) for rt_object_get_type
    timer.o(i.rt_timer_stop) refers to context_rvds.o(.text) for rt_hw_interrupt_disable
    timer.o(i.rt_timer_stop) refers to timer.o(i._rt_timer_remove) for _rt_timer_remove
    timer.o(i.rt_timer_stop) refers to timer.o(.constdata) for .constdata
    timer.o(i.rt_timer_stop) refers to object.o(.data) for rt_object_put_hook
    context_rvds.o(.text) refers to cpuport.o(i.rt_hw_hard_fault_exception) for rt_hw_hard_fault_exception
    context_rvds.o(.text) refers to cpuport.o(.data) for rt_thread_switch_interrupt_flag
    context_rvds.o(.text) refers to cpuport.o(.data) for rt_interrupt_from_thread
    context_rvds.o(.text) refers to cpuport.o(.data) for rt_interrupt_to_thread
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.pow) for pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.__pow$lsc) for __pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.__pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.__pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.__pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.__pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.__pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.__pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.__pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.__pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.__pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.__pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.__pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    idiv.o(.text) refers to uidiv.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    llmul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to debug.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to debug.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to debug.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to debug.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to debug.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to debug.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to debug.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to debug.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to debug.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to debug.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to debug.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to debug.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to debug.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to debug.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to debug.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to debug.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to debug.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to debug.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to debug.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to debug.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to debug.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to debug.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to debug.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to debug.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to debug.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to debug.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to debug.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to debug.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to debug.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to debug.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to debug.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to debug.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to debug.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to debug.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to debug.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to debug.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to debug.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to debug.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to debug.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to debug.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to debug.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to debug.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to debug.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to debug.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32l072xx.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32l072xx.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to startup_stm32l072xx.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32l072xx.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32l072xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32l072xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to components.o(i.$Sub$$main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to components.o(i.$Sub$$main) for main
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.data), (1 bytes).
    Removing auto_ctrl_task.o(.rev16_text), (4 bytes).
    Removing auto_ctrl_task.o(.revsh_text), (4 bytes).
    Removing user_config.o(.rev16_text), (4 bytes).
    Removing user_config.o(.revsh_text), (4 bytes).
    Removing protocol_common.o(.rev16_text), (4 bytes).
    Removing protocol_common.o(.revsh_text), (4 bytes).
    Removing daosheng_protocol.o(.rev16_text), (4 bytes).
    Removing daosheng_protocol.o(.revsh_text), (4 bytes).
    Removing daosheng_protocol.o(i.adjust_long_value), (88 bytes).
    Removing daosheng_protocol.o(i.daoSheng_protocol_parsing), (220 bytes).
    Removing daosheng_protocol.o(i.parseWaterMeterData), (160 bytes).
    Removing daosheng_protocol.o(i.parse_ieee754), (24 bytes).
    Removing daosheng_protocol.o(i.parse_long), (24 bytes).
    Removing daosheng_protocol.o(i.parse_modbus_data), (124 bytes).
    Removing daosheng_protocol.o(i.process_meter_data), (152 bytes).
    Removing daosheng_protocol.o(.bss), (32 bytes).
    Removing daosheng_protocol.o(.constdata), (16 bytes).
    Removing flow_meter_protocol.o(.rev16_text), (4 bytes).
    Removing flow_meter_protocol.o(.revsh_text), (4 bytes).
    Removing water_meter_protocol.o(.rev16_text), (4 bytes).
    Removing water_meter_protocol.o(.revsh_text), (4 bytes).
    Removing modbus_rtu.o(.rev16_text), (4 bytes).
    Removing modbus_rtu.o(.revsh_text), (4 bytes).
    Removing modbus_rtu.o(i.modbus_rtu_read), (20 bytes).
    Removing modbus_rtu.o(i.modbus_rtu_send), (4 bytes).
    Removing modbus_rtu.o(i.modbus_rtu_send_buffer_init), (2 bytes).
    Removing modbus_rtu.o(i.modbus_rtu_write), (4 bytes).
    Removing pc_protocol.o(.rev16_text), (4 bytes).
    Removing pc_protocol.o(.revsh_text), (4 bytes).
    Removing pc_protocol.o(.constdata), (4 bytes).
    Removing net_protocol.o(.rev16_text), (4 bytes).
    Removing net_protocol.o(.revsh_text), (4 bytes).
    Removing net_protocol.o(i.net_heartbeat), (492 bytes).
    Removing bsp_board.o(.rev16_text), (4 bytes).
    Removing bsp_board.o(.revsh_text), (4 bytes).
    Removing bsp_board.o(i.EnterStandbyMode), (176 bytes).
    Removing bsp_board.o(i.Error_Handler), (20 bytes).
    Removing bsp_board.o(i.bsp_board_enter_standy), (28 bytes).
    Removing lcd12864.o(.rev16_text), (4 bytes).
    Removing lcd12864.o(.revsh_text), (4 bytes).
    Removing lcd12864.o(i.disp_col), (58 bytes).
    Removing lcd12864.o(i.disp_row), (58 bytes).
    Removing lcd12864.o(i.display_fourG_volage), (14 bytes).
    Removing lcd12864.o(i.display_power_voltage), (64 bytes).
    Removing lcd12864.o(i.display_signal_strength), (68 bytes).
    Removing lcd12864.o(i.dispstr), (96 bytes).
    Removing lcd12864.o(i.lcd_backlight_ctrl), (24 bytes).
    Removing lcd12864.o(i.lcd_draw_line), (64 bytes).
    Removing lcd12864.o(.bss), (36 bytes).
    Removing lcd12864.o(.constdata), (32 bytes).
    Removing lcd_font.o(.rev16_text), (4 bytes).
    Removing lcd_font.o(.revsh_text), (4 bytes).
    Removing net.o(.rev16_text), (4 bytes).
    Removing net.o(.revsh_text), (4 bytes).
    Removing ec600m_common.o(.rev16_text), (4 bytes).
    Removing ec600m_common.o(.revsh_text), (4 bytes).
    Removing ec600m_tcpip.o(.rev16_text), (4 bytes).
    Removing ec600m_tcpip.o(.revsh_text), (4 bytes).
    Removing uart.o(.rev16_text), (4 bytes).
    Removing uart.o(.revsh_text), (4 bytes).
    Removing debug.o(.rev16_text), (4 bytes).
    Removing debug.o(.revsh_text), (4 bytes).
    Removing stm32_flash.o(.rev16_text), (4 bytes).
    Removing stm32_flash.o(.revsh_text), (4 bytes).
    Removing stm32_flash.o(i.EEPROM_EraseAll), (26 bytes).
    Removing stm32_flash.o(i.EEPROM_ErasePage), (32 bytes).
    Removing stm32_flash.o(i.EEPROM_ReadByte), (32 bytes).
    Removing stm32_flash.o(i.stm32_flash_read_half_word), (4 bytes).
    Removing stm32_flash.o(i.stm32_flash_read_word), (4 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(i.ADC_Init_LowPower), (152 bytes).
    Removing adc.o(i.Read_VREFINT_Raw), (340 bytes).
    Removing adc.o(i.vdda_get_voltage), (640 bytes).
    Removing adc.o(.constdata), (4 bytes).
    Removing adc.o(.constdata), (4 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(i.rtc_set_alarm), (68 bytes).
    Removing system_stm32l0xx.o(.rev16_text), (4 bytes).
    Removing system_stm32l0xx.o(.revsh_text), (4 bytes).
    Removing system.o(.rev16_text), (4 bytes).
    Removing system.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_DBG_DisableLowPowerConfig), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_DBG_EnableLowPowerConfig), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_DeInit), (40 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32l0xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32l0xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal.o(i.HAL_ResumeTick), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_SYSCFG_Disable_Lock_VREFINT), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_SYSCFG_Enable_Lock_VREFINT), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_SYSCFG_GetBootMode), (16 bytes).
    Removing stm32l0xx_hal.o(i.HAL_SYSCFG_VREFINT_OutputSelect), (24 bytes).
    Removing stm32l0xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32l0xx_hal.o(i.HAL_SuspendTick), (16 bytes).
    Removing stm32l0xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_adc.o(i.ADC_DMAConvCplt), (110 bytes).
    Removing stm32l0xx_hal_adc.o(i.ADC_DMAError), (26 bytes).
    Removing stm32l0xx_hal_adc.o(i.ADC_DMAHalfConvCplt), (10 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (160 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_DeInit), (184 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_ErrorCallback), (2 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_IRQHandler), (216 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_PollForEvent), (120 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_Start_DMA), (164 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_Start_IT), (128 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (130 bytes).
    Removing stm32l0xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32l0xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_GetValue), (12 bytes).
    Removing stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue), (94 bytes).
    Removing stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start), (170 bytes).
    Removing stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVREFINT), (16 bytes).
    Removing stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVREFINTTempSensor), (16 bytes).
    Removing stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_EnableVREFINT), (48 bytes).
    Removing stm32l0xx_hal_adc_ex.o(i.HAL_ADCEx_EnableVREFINTTempSensor), (48 bytes).
    Removing stm32l0xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (80 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_MPU_Disable), (16 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_MPU_Enable), (24 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (24 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (32 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (60 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (24 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (28 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (28 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32l0xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_dma.o(i.DMA_SetConfig), (46 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_Abort), (68 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_Abort_IT), (76 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_DeInit), (104 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_IRQHandler), (180 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_Init), (148 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (220 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_Start), (76 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_Start_IT), (114 bytes).
    Removing stm32l0xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32l0xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_gpio.o(i.HAL_GPIO_DeInit), (232 bytes).
    Removing stm32l0xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32l0xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32l0xx_hal_gpio.o(i.HAL_GPIO_LockPin), (38 bytes).
    Removing stm32l0xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (14 bytes).
    Removing stm32l0xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (104 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (96 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (100 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32l0xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFastWakeUp), (16 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode), (76 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUltraLowPower), (16 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFastWakeUp), (16 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode), (24 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUltraLowPower), (16 bytes).
    Removing stm32l0xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32l0xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (20 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (196 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (160 bytes).
    Removing stm32l0xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (76 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (40 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (120 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (112 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableHSI48_VREFINT), (16 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (32 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableHSI48_VREFINT), (16 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (20 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (52 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (112 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (628 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (24 bytes).
    Removing stm32l0xx_hal_rtc.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_rtc.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_AlarmAEventCallback), (2 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_AlarmIRQHandler), (96 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_DeInit), (228 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_DeactivateAlarm), (194 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_GetAlarm), (184 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_GetState), (6 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_PollForAlarmAEvent), (72 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm), (378 bytes).
    Removing stm32l0xx_hal_rtc.o(i.HAL_RTC_SetAlarm_IT), (428 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_AlarmBEventCallback), (2 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateCalibrationOutPut), (60 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateRefClock), (98 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateTamper), (96 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateTimeStamp), (76 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DeactivateWakeUpTimer), (120 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_DisableBypassShadow), (60 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_EnableBypassShadow), (60 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_GetTimeStamp), (156 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_GetWakeUpTimer), (8 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForAlarmBEvent), (76 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper1Event), (76 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper2Event), (76 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTamper3Event), (76 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForTimeStampEvent), (92 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_PollForWakeUpTimerEvent), (76 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetCalibrationOutPut), (78 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetRefClock), (98 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSmoothCalib), (126 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetSynchroShift), (164 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTamper), (208 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTamper_IT), (232 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTimeStamp), (108 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetTimeStamp_IT), (140 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer), (204 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_SetWakeUpTimer_IT), (252 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper1EventCallback), (2 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper2EventCallback), (2 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_Tamper3EventCallback), (2 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TamperTimeStampIRQHandler), (192 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_TimeStampEventCallback), (2 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerEventCallback), (2 bytes).
    Removing stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_WakeUpTimerIRQHandler), (56 bytes).
    Removing stm32l0xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (96 bytes).
    Removing stm32l0xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32l0xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (54 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (54 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_HalfDuplex_Init), (122 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_LIN_Init), (160 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_LIN_SendBreak), (44 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (46 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (46 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (12 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_MultiProcessor_Init), (148 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Abort), (188 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive), (128 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit), (84 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Abort_IT), (228 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_DMAPause), (94 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_DMAResume), (86 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_DMAStop), (134 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_DeInit), (64 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_DisableReceiverTimeout), (72 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_EnableReceiverTimeout), (72 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_IRQHandler), (552 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Receive), (292 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Receive_DMA), (108 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Receive_IT), (108 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_ReceiverTimeout_Config), (24 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (184 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_Transmit_IT), (136 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMAAbortOnError), (20 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMAError), (76 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMAReceiveCplt), (94 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMARxAbortCallback), (64 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMARxHalfCplt), (28 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (38 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMATransmitCplt), (48 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMATxAbortCallback), (54 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_EndRxTransfer), (50 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_EndTransmit_IT), (26 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_EndTxTransfer), (16 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_RxISR_16BIT), (120 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_RxISR_8BIT), (120 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_Start_Receive_DMA), (136 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_Start_Receive_IT), (164 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_TxISR_16BIT), (64 bytes).
    Removing stm32l0xx_hal_uart.o(i.UART_TxISR_8BIT), (60 bytes).
    Removing stm32l0xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (52 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (144 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_DisableClockStopMode), (36 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (34 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_EnableClockStopMode), (36 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (34 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle), (360 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (108 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT), (108 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (128 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback), (2 bytes).
    Removing stm32l0xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig), (38 bytes).
    Removing stm32l0xx_hal_usart.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_usart.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_Abort), (164 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_AbortCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_Abort_IT), (200 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_DMAPause), (112 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_DMAResume), (98 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_DMAStop), (102 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_DeInit), (50 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_ErrorCallback), (2 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_GetError), (4 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_GetState), (6 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_IRQHandler), (348 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_Init), (102 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_MspDeInit), (2 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_MspInit), (2 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_Receive), (308 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_Receive_DMA), (244 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_Receive_IT), (220 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_RxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_Transmit), (248 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive), (468 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA), (288 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_TransmitReceive_IT), (256 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_Transmit_DMA), (172 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_Transmit_IT), (128 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_TxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback), (2 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_CheckIdleState), (96 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_DMAAbortOnError), (16 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_DMAError), (38 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_DMAReceiveCplt), (108 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_DMARxAbortCallback), (46 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_DMARxHalfCplt), (10 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_DMATransmitCplt), (62 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_DMATxAbortCallback), (46 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_DMATxHalfCplt), (10 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_EndTransfer), (30 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_EndTransmit_IT), (72 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_RxISR_16BIT), (120 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_RxISR_8BIT), (120 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_SetConfig), (404 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_TxISR_16BIT), (66 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_TxISR_8BIT), (62 bytes).
    Removing stm32l0xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout), (74 bytes).
    Removing stm32l0xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (220 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (32 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (68 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32l0xx_hal_flash.o(i.HAL_FLASH_Program_IT), (60 bytes).
    Removing stm32l0xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32l0xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_OB_BOOTBit1Config), (60 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_OB_BORConfig), (64 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_OB_BootConfig), (64 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_OB_GetBOOTBit1), (12 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_OB_GetWRP2), (12 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_OB_ProtectedSectorsConfig), (184 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_OB_RDPConfig), (60 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (68 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBGetConfig), (48 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram), (36 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_DisableFixedTimeProgram), (16 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_EnableFixedTimeProgram), (16 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Erase), (40 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Lock), (20 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (46 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (144 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_DeSelectPCROP), (52 bytes).
    Removing stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_SelectPCROP), (56 bytes).
    Removing board.o(.rev16_text), (4 bytes).
    Removing board.o(.revsh_text), (4 bytes).
    Removing clock.o(i.rt_system_tick_init), (2 bytes).
    Removing clock.o(i.rt_tick_from_millisecond), (46 bytes).
    Removing clock.o(i.rt_tick_set), (24 bytes).
    Removing idle.o(i.rt_thread_idle_delhook), (52 bytes).
    Removing idle.o(i.rt_thread_idle_gethandler), (8 bytes).
    Removing idle.o(i.rt_thread_idle_sethook), (52 bytes).
    Removing ipc.o(i.rt_event_control), (116 bytes).
    Removing ipc.o(i.rt_event_create), (76 bytes).
    Removing ipc.o(i.rt_event_delete), (212 bytes).
    Removing ipc.o(i.rt_event_detach), (164 bytes).
    Removing ipc.o(i.rt_event_init), (76 bytes).
    Removing ipc.o(i.rt_event_recv), (388 bytes).
    Removing ipc.o(i.rt_event_send), (232 bytes).
    Removing ipc.o(i.rt_ipc_list_resume_all), (46 bytes).
    Removing ipc.o(i.rt_mb_control), (124 bytes).
    Removing ipc.o(i.rt_mb_create), (124 bytes).
    Removing ipc.o(i.rt_mb_delete), (220 bytes).
    Removing ipc.o(i.rt_mb_detach), (164 bytes).
    Removing ipc.o(i.rt_mb_init), (92 bytes).
    Removing ipc.o(i.rt_mb_recv), (428 bytes).
    Removing ipc.o(i.rt_mb_send), (10 bytes).
    Removing ipc.o(i.rt_mb_send_wait), (400 bytes).
    Removing ipc.o(i.rt_mq_control), (148 bytes).
    Removing ipc.o(i.rt_mq_create), (168 bytes).
    Removing ipc.o(i.rt_mq_delete), (220 bytes).
    Removing ipc.o(i.rt_mq_detach), (164 bytes).
    Removing ipc.o(i.rt_mq_init), (144 bytes).
    Removing ipc.o(i.rt_mq_recv), (512 bytes).
    Removing ipc.o(i.rt_mq_send), (10 bytes).
    Removing ipc.o(i.rt_mq_send_wait), (492 bytes).
    Removing ipc.o(i.rt_mq_urgent), (276 bytes).
    Removing ipc.o(i.rt_mutex_control), (80 bytes).
    Removing ipc.o(i.rt_mutex_create), (88 bytes).
    Removing ipc.o(i.rt_mutex_delete), (212 bytes).
    Removing ipc.o(i.rt_mutex_detach), (164 bytes).
    Removing ipc.o(i.rt_mutex_init), (88 bytes).
    Removing ipc.o(i.rt_mutex_release), (300 bytes).
    Removing ipc.o(i.rt_mutex_take), (356 bytes).
    Removing ipc.o(i.rt_sem_control), (112 bytes).
    Removing ipc.o(i.rt_sem_create), (116 bytes).
    Removing ipc.o(i.rt_sem_delete), (204 bytes).
    Removing ipc.o(i.rt_sem_detach), (148 bytes).
    Removing ipc.o(i.rt_sem_trytake), (10 bytes).
    Removing irq.o(i.rt_interrupt_enter_sethook), (12 bytes).
    Removing irq.o(i.rt_interrupt_leave_sethook), (12 bytes).
    Removing kservice.o(i._rt_errno), (36 bytes).
    Removing kservice.o(i.rt_assert_set_hook), (12 bytes).
    Removing kservice.o(i.rt_free_align), (12 bytes).
    Removing kservice.o(i.rt_get_errno), (36 bytes).
    Removing kservice.o(i.rt_malloc_align), (52 bytes).
    Removing kservice.o(i.rt_memcmp), (28 bytes).
    Removing kservice.o(i.rt_memcpy), (78 bytes).
    Removing kservice.o(i.rt_memmove), (52 bytes).
    Removing kservice.o(i.rt_set_errno), (40 bytes).
    Removing kservice.o(i.rt_snprintf), (20 bytes).
    Removing kservice.o(i.rt_strcasecmp), (42 bytes).
    Removing kservice.o(i.rt_strcmp), (26 bytes).
    Removing kservice.o(i.rt_strdup), (42 bytes).
    Removing kservice.o(i.rt_strncmp), (36 bytes).
    Removing kservice.o(i.rt_strnlen), (22 bytes).
    Removing kservice.o(i.rt_strstr), (62 bytes).
    Removing mem.o(i.rt_calloc), (30 bytes).
    Removing mem.o(i.rt_free_sethook), (12 bytes).
    Removing mem.o(i.rt_malloc_sethook), (12 bytes).
    Removing mem.o(i.rt_memory_info), (36 bytes).
    Removing mem.o(i.rt_realloc), (268 bytes).
    Removing mempool.o(i.rt_list_isempty), (14 bytes).
    Removing mempool.o(i.rt_mp_alloc), (320 bytes).
    Removing mempool.o(i.rt_mp_alloc_sethook), (12 bytes).
    Removing mempool.o(i.rt_mp_create), (236 bytes).
    Removing mempool.o(i.rt_mp_delete), (288 bytes).
    Removing mempool.o(i.rt_mp_detach), (232 bytes).
    Removing mempool.o(i.rt_mp_free), (92 bytes).
    Removing mempool.o(i.rt_mp_free_sethook), (12 bytes).
    Removing mempool.o(i.rt_mp_init), (248 bytes).
    Removing mempool.o(.constdata), (62 bytes).
    Removing mempool.o(.data), (8 bytes).
    Removing object.o(i.rt_object_attach_sethook), (12 bytes).
    Removing object.o(i.rt_object_detach_sethook), (12 bytes).
    Removing object.o(i.rt_object_find), (156 bytes).
    Removing object.o(i.rt_object_put_sethook), (12 bytes).
    Removing object.o(i.rt_object_take_sethook), (12 bytes).
    Removing object.o(i.rt_object_trytake_sethook), (12 bytes).
    Removing object.o(i.rt_system_object_init), (2 bytes).
    Removing scheduler.o(i.rt_critical_level), (12 bytes).
    Removing scheduler.o(i.rt_scheduler_sethook), (12 bytes).
    Removing thread.o(i.rt_thread_control), (168 bytes).
    Removing thread.o(i.rt_thread_delay), (8 bytes).
    Removing thread.o(i.rt_thread_delay_until), (204 bytes).
    Removing thread.o(i.rt_thread_delete), (212 bytes).
    Removing thread.o(i.rt_thread_detach), (224 bytes).
    Removing thread.o(i.rt_thread_find), (132 bytes).
    Removing thread.o(i.rt_thread_inited_sethook), (12 bytes).
    Removing thread.o(i.rt_thread_mdelay), (12 bytes).
    Removing thread.o(i.rt_thread_resume_sethook), (12 bytes).
    Removing thread.o(i.rt_thread_sleep), (128 bytes).
    Removing thread.o(i.rt_thread_suspend_sethook), (12 bytes).
    Removing timer.o(i.rt_timer_create), (48 bytes).
    Removing timer.o(i.rt_timer_delete), (232 bytes).
    Removing timer.o(i.rt_timer_enter_sethook), (12 bytes).
    Removing timer.o(i.rt_timer_exit_sethook), (12 bytes).
    Removing timer.o(i.rt_timer_list_next_timeout), (26 bytes).
    Removing timer.o(i.rt_timer_next_timeout_tick), (16 bytes).
    Removing cpuport.o(i.rt_hw_cpu_reset), (16 bytes).
    Removing dflti.o(.text), (40 bytes).
    Removing ffixi.o(.text), (50 bytes).
    Removing ffixui.o(.text), (40 bytes).
    Removing dscalb.o(.text), (44 bytes).
    Removing dsqrt.o(.text), (162 bytes).
    Removing cdcmple.o(.text), (40 bytes).

556 unused section(s) (total 39217 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llmul.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncpy.c        0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\\app\\auto_ctrl_task.c                0x00000000   Number         0  auto_ctrl_task.o ABSOLUTE
    ..\\app\\main.c                          0x00000000   Number         0  main.o ABSOLUTE
    ..\\app\\user_config.c                   0x00000000   Number         0  user_config.o ABSOLUTE
    ..\\drivers\\bsp_board.c                 0x00000000   Number         0  bsp_board.o ABSOLUTE
    ..\\lcd12864\\lcd12864.c                 0x00000000   Number         0  lcd12864.o ABSOLUTE
    ..\\lcd12864\\lcd_font.c                 0x00000000   Number         0  lcd_font.o ABSOLUTE
    ..\\net\\EC600x\\ec600m_common.c         0x00000000   Number         0  ec600m_common.o ABSOLUTE
    ..\\net\\EC600x\\ec600m_tcpip.c          0x00000000   Number         0  ec600m_tcpip.o ABSOLUTE
    ..\\net\\net.c                           0x00000000   Number         0  net.o ABSOLUTE
    ..\\peripheral\\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\\peripheral\\debug.c                  0x00000000   Number         0  debug.o ABSOLUTE
    ..\\peripheral\\rtc.c                    0x00000000   Number         0  rtc.o ABSOLUTE
    ..\\peripheral\\stm32_flash.c            0x00000000   Number         0  stm32_flash.o ABSOLUTE
    ..\\peripheral\\uart.c                   0x00000000   Number         0  uart.o ABSOLUTE
    ..\\protocol\\daoSheng_protocol.c        0x00000000   Number         0  daosheng_protocol.o ABSOLUTE
    ..\\protocol\\flow_meter_protocol.c      0x00000000   Number         0  flow_meter_protocol.o ABSOLUTE
    ..\\protocol\\modbus_rtu.c               0x00000000   Number         0  modbus_rtu.o ABSOLUTE
    ..\\protocol\\net_protocol.c             0x00000000   Number         0  net_protocol.o ABSOLUTE
    ..\\protocol\\pc_protocol.c              0x00000000   Number         0  pc_protocol.o ABSOLUTE
    ..\\protocol\\protocol_common.c          0x00000000   Number         0  protocol_common.o ABSOLUTE
    ..\\protocol\\water_meter_protocol.c     0x00000000   Number         0  water_meter_protocol.o ABSOLUTE
    ..\\rtthread\\bsp\\board.c               0x00000000   Number         0  board.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal.c  0x00000000   Number         0  stm32l0xx_hal.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_adc.c 0x00000000   Number         0  stm32l0xx_hal_adc.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_adc_ex.c 0x00000000   Number         0  stm32l0xx_hal_adc_ex.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_cortex.c 0x00000000   Number         0  stm32l0xx_hal_cortex.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_dma.c 0x00000000   Number         0  stm32l0xx_hal_dma.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_exti.c 0x00000000   Number         0  stm32l0xx_hal_exti.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_flash.c 0x00000000   Number         0  stm32l0xx_hal_flash.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_flash_ex.c 0x00000000   Number         0  stm32l0xx_hal_flash_ex.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_gpio.c 0x00000000   Number         0  stm32l0xx_hal_gpio.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_pwr.c 0x00000000   Number         0  stm32l0xx_hal_pwr.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_pwr_ex.c 0x00000000   Number         0  stm32l0xx_hal_pwr_ex.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_rcc.c 0x00000000   Number         0  stm32l0xx_hal_rcc.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_rcc_ex.c 0x00000000   Number         0  stm32l0xx_hal_rcc_ex.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_rtc.c 0x00000000   Number         0  stm32l0xx_hal_rtc.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_rtc_ex.c 0x00000000   Number         0  stm32l0xx_hal_rtc_ex.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_tim_ex.c 0x00000000   Number         0  stm32l0xx_hal_tim_ex.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_uart.c 0x00000000   Number         0  stm32l0xx_hal_uart.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_uart_ex.c 0x00000000   Number         0  stm32l0xx_hal_uart_ex.o ABSOLUTE
    ..\\stm32L0xx_hal\\Src\\stm32l0xx_hal_usart.c 0x00000000   Number         0  stm32l0xx_hal_usart.o ABSOLUTE
    ..\\system\\system.c                     0x00000000   Number         0  system.o ABSOLUTE
    ..\\system\\system_stm32l0xx.c           0x00000000   Number         0  system_stm32l0xx.o ABSOLUTE
    ..\app\auto_ctrl_task.c                  0x00000000   Number         0  auto_ctrl_task.o ABSOLUTE
    ..\app\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ..\app\user_config.c                     0x00000000   Number         0  user_config.o ABSOLUTE
    ..\drivers\bsp_board.c                   0x00000000   Number         0  bsp_board.o ABSOLUTE
    ..\lcd12864\lcd12864.c                   0x00000000   Number         0  lcd12864.o ABSOLUTE
    ..\lcd12864\lcd_font.c                   0x00000000   Number         0  lcd_font.o ABSOLUTE
    ..\net\EC600x\ec600m_common.c            0x00000000   Number         0  ec600m_common.o ABSOLUTE
    ..\net\EC600x\ec600m_tcpip.c             0x00000000   Number         0  ec600m_tcpip.o ABSOLUTE
    ..\net\net.c                             0x00000000   Number         0  net.o ABSOLUTE
    ..\peripheral\adc.c                      0x00000000   Number         0  adc.o ABSOLUTE
    ..\peripheral\debug.c                    0x00000000   Number         0  debug.o ABSOLUTE
    ..\peripheral\rtc.c                      0x00000000   Number         0  rtc.o ABSOLUTE
    ..\peripheral\stm32_flash.c              0x00000000   Number         0  stm32_flash.o ABSOLUTE
    ..\peripheral\uart.c                     0x00000000   Number         0  uart.o ABSOLUTE
    ..\protocol\daoSheng_protocol.c          0x00000000   Number         0  daosheng_protocol.o ABSOLUTE
    ..\protocol\flow_meter_protocol.c        0x00000000   Number         0  flow_meter_protocol.o ABSOLUTE
    ..\protocol\modbus_rtu.c                 0x00000000   Number         0  modbus_rtu.o ABSOLUTE
    ..\protocol\net_protocol.c               0x00000000   Number         0  net_protocol.o ABSOLUTE
    ..\protocol\pc_protocol.c                0x00000000   Number         0  pc_protocol.o ABSOLUTE
    ..\protocol\protocol_common.c            0x00000000   Number         0  protocol_common.o ABSOLUTE
    ..\protocol\water_meter_protocol.c       0x00000000   Number         0  water_meter_protocol.o ABSOLUTE
    ..\rtthread\bsp\board.c                  0x00000000   Number         0  board.o ABSOLUTE
    ..\rtthread\libcpu\arm\cortex-m0\context_rvds.S 0x00000000   Number         0  context_rvds.o ABSOLUTE
    ..\rtthread\libcpu\arm\cortex-m0\cpuport.c 0x00000000   Number         0  cpuport.o ABSOLUTE
    ..\rtthread\src\clock.c                  0x00000000   Number         0  clock.o ABSOLUTE
    ..\rtthread\src\components.c             0x00000000   Number         0  components.o ABSOLUTE
    ..\rtthread\src\cpu.c                    0x00000000   Number         0  cpu.o ABSOLUTE
    ..\rtthread\src\device.c                 0x00000000   Number         0  device.o ABSOLUTE
    ..\rtthread\src\idle.c                   0x00000000   Number         0  idle.o ABSOLUTE
    ..\rtthread\src\ipc.c                    0x00000000   Number         0  ipc.o ABSOLUTE
    ..\rtthread\src\irq.c                    0x00000000   Number         0  irq.o ABSOLUTE
    ..\rtthread\src\kservice.c               0x00000000   Number         0  kservice.o ABSOLUTE
    ..\rtthread\src\mem.c                    0x00000000   Number         0  mem.o ABSOLUTE
    ..\rtthread\src\memheap.c                0x00000000   Number         0  memheap.o ABSOLUTE
    ..\rtthread\src\mempool.c                0x00000000   Number         0  mempool.o ABSOLUTE
    ..\rtthread\src\object.c                 0x00000000   Number         0  object.o ABSOLUTE
    ..\rtthread\src\scheduler.c              0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\rtthread\src\signal.c                 0x00000000   Number         0  signal.o ABSOLUTE
    ..\rtthread\src\slab.c                   0x00000000   Number         0  slab.o ABSOLUTE
    ..\rtthread\src\thread.c                 0x00000000   Number         0  thread.o ABSOLUTE
    ..\rtthread\src\timer.c                  0x00000000   Number         0  timer.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal.c     0x00000000   Number         0  stm32l0xx_hal.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_adc.c 0x00000000   Number         0  stm32l0xx_hal_adc.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_adc_ex.c 0x00000000   Number         0  stm32l0xx_hal_adc_ex.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_cortex.c 0x00000000   Number         0  stm32l0xx_hal_cortex.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_dma.c 0x00000000   Number         0  stm32l0xx_hal_dma.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_exti.c 0x00000000   Number         0  stm32l0xx_hal_exti.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_flash.c 0x00000000   Number         0  stm32l0xx_hal_flash.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_flash_ex.c 0x00000000   Number         0  stm32l0xx_hal_flash_ex.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_gpio.c 0x00000000   Number         0  stm32l0xx_hal_gpio.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_pwr.c 0x00000000   Number         0  stm32l0xx_hal_pwr.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_pwr_ex.c 0x00000000   Number         0  stm32l0xx_hal_pwr_ex.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_rcc.c 0x00000000   Number         0  stm32l0xx_hal_rcc.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_rcc_ex.c 0x00000000   Number         0  stm32l0xx_hal_rcc_ex.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_rtc.c 0x00000000   Number         0  stm32l0xx_hal_rtc.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_rtc_ex.c 0x00000000   Number         0  stm32l0xx_hal_rtc_ex.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_tim_ex.c 0x00000000   Number         0  stm32l0xx_hal_tim_ex.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_uart.c 0x00000000   Number         0  stm32l0xx_hal_uart.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_uart_ex.c 0x00000000   Number         0  stm32l0xx_hal_uart_ex.o ABSOLUTE
    ..\stm32L0xx_hal\Src\stm32l0xx_hal_usart.c 0x00000000   Number         0  stm32l0xx_hal_usart.o ABSOLUTE
    ..\system\startup_stm32l072xx.s          0x00000000   Number         0  startup_stm32l072xx.o ABSOLUTE
    ..\system\system.c                       0x00000000   Number         0  system.o ABSOLUTE
    ..\system\system_stm32l0xx.c             0x00000000   Number         0  system_stm32l0xx.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      192  startup_stm32l072xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080000d0   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080000d4   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080000d4   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080000d4   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080000d4   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080000d8   Section       28  startup_stm32l072xx.o(.text)
    .text                                    0x080000f4   Section      216  context_rvds.o(.text)
    .text                                    0x080001cc   Section        0  uidiv.o(.text)
    .text                                    0x080001f8   Section        0  uldiv.o(.text)
    .text                                    0x08000258   Section        0  llmul.o(.text)
    .text                                    0x080002d2   Section        0  memcpya.o(.text)
    .text                                    0x080002f6   Section        0  memseta.o(.text)
    .text                                    0x0800031a   Section        0  strstr.o(.text)
    .text                                    0x08000342   Section        0  strncpy.o(.text)
    .text                                    0x0800035c   Section        0  strchr.o(.text)
    .text                                    0x08000370   Section        0  strlen.o(.text)
    .text                                    0x0800037e   Section        0  memcmp.o(.text)
    .text                                    0x08000398   Section        0  strcpy.o(.text)
    .text                                    0x080003aa   Section        0  atoi.o(.text)
    .text                                    0x080003c4   Section        0  fmul.o(.text)
    .text                                    0x0800043e   Section        0  fdiv.o(.text)
    .text                                    0x080004bc   Section        0  dmul.o(.text)
    .text                                    0x0800058c   Section        0  ddiv.o(.text)
    .text                                    0x0800067c   Section        0  ffltui.o(.text)
    .text                                    0x0800068a   Section        0  f2d.o(.text)
    .text                                    0x080006b2   Section        0  llshl.o(.text)
    .text                                    0x080006d2   Section        0  llushr.o(.text)
    .text                                    0x080006f4   Section        0  strtol.o(.text)
    .text                                    0x08000764   Section        0  fepilogue.o(.text)
    .text                                    0x08000764   Section        0  iusefp.o(.text)
    .text                                    0x080007e6   Section        0  depilogue.o(.text)
    .text                                    0x080008a4   Section        0  dadd.o(.text)
    .text                                    0x08000a0c   Section        0  dfixul.o(.text)
    .text                                    0x08000a4c   Section       40  cdrcmple.o(.text)
    .text                                    0x08000a74   Section       36  init.o(.text)
    .text                                    0x08000a98   Section        0  llsshr.o(.text)
    .text                                    0x08000ac0   Section        0  ctype_o.o(.text)
    .text                                    0x08000ac8   Section        0  _strtoul.o(.text)
    .text                                    0x08000b6e   Section        0  _chval.o(.text)
    .text                                    0x08000b8c   Section        0  __dczerorl2.o(.text)
    i.$Sub$$main                             0x08000be2   Section        0  components.o(i.$Sub$$main)
    i.ADC_ConversionStop                     0x08000bec   Section        0  stm32l0xx_hal_adc.o(i.ADC_ConversionStop)
    ADC_ConversionStop                       0x08000bed   Thumb Code    82  stm32l0xx_hal_adc.o(i.ADC_ConversionStop)
    i.ADC_DelayMicroSecond                   0x08000c40   Section        0  stm32l0xx_hal_adc.o(i.ADC_DelayMicroSecond)
    ADC_DelayMicroSecond                     0x08000c41   Thumb Code    34  stm32l0xx_hal_adc.o(i.ADC_DelayMicroSecond)
    i.ADC_Disable                            0x08000c6c   Section        0  stm32l0xx_hal_adc.o(i.ADC_Disable)
    ADC_Disable                              0x08000c6d   Thumb Code   112  stm32l0xx_hal_adc.o(i.ADC_Disable)
    i.ADC_Enable                             0x08000cdc   Section        0  stm32l0xx_hal_adc.o(i.ADC_Enable)
    ADC_Enable                               0x08000cdd   Thumb Code   112  stm32l0xx_hal_adc.o(i.ADC_Enable)
    i.ADC_Voltage_Init                       0x08000d50   Section        0  adc.o(i.ADC_Voltage_Init)
    i.EEPROM_Init                            0x08000dcc   Section        0  stm32_flash.o(i.EEPROM_Init)
    i.EEPROM_ReadBuffer                      0x08000df0   Section        0  stm32_flash.o(i.EEPROM_ReadBuffer)
    i.EEPROM_WriteBuffer                     0x08000e1c   Section        0  stm32_flash.o(i.EEPROM_WriteBuffer)
    i.EEPROM_WriteByte                       0x08000e54   Section        0  stm32_flash.o(i.EEPROM_WriteByte)
    i.FLASH_PageErase                        0x08000e74   Section        0  stm32l0xx_hal_flash_ex.o(i.FLASH_PageErase)
    i.FLASH_SetErrorCode                     0x08000ea0   Section        0  stm32l0xx_hal_flash.o(i.FLASH_SetErrorCode)
    FLASH_SetErrorCode                       0x08000ea1   Thumb Code   134  stm32l0xx_hal_flash.o(i.FLASH_SetErrorCode)
    i.FLASH_WaitForLastOperation             0x08000f30   Section        0  stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation)
    i.Get_Power_Voltage                      0x08000fa0   Section        0  adc.o(i.Get_Power_Voltage)
    i.HAL_ADC_ConfigChannel                  0x0800103c   Section        0  stm32l0xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_GetValue                       0x080010d8   Section        0  stm32l0xx_hal_adc.o(i.HAL_ADC_GetValue)
    i.HAL_ADC_Init                           0x080010e0   Section        0  stm32l0xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x080012ac   Section        0  stm32l0xx_hal_adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_PollForConversion              0x080012ae   Section        0  stm32l0xx_hal_adc.o(i.HAL_ADC_PollForConversion)
    i.HAL_ADC_Start                          0x08001370   Section        0  stm32l0xx_hal_adc.o(i.HAL_ADC_Start)
    i.HAL_ADC_Stop                           0x080013d0   Section        0  stm32l0xx_hal_adc.o(i.HAL_ADC_Stop)
    i.HAL_Delay                              0x08001410   Section        0  stm32l0xx_hal.o(i.HAL_Delay)
    i.HAL_FLASHEx_DATAEEPROM_Program         0x08001434   Section        0  stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Program)
    i.HAL_FLASHEx_DATAEEPROM_Unlock          0x08001490   Section        0  stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Unlock)
    i.HAL_FLASHEx_Erase                      0x080014c4   Section        0  stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase)
    i.HAL_FLASH_Lock                         0x08001538   Section        0  stm32l0xx_hal_flash.o(i.HAL_FLASH_Lock)
    i.HAL_FLASH_Program                      0x08001554   Section        0  stm32l0xx_hal_flash.o(i.HAL_FLASH_Program)
    i.HAL_FLASH_Unlock                       0x08001590   Section        0  stm32l0xx_hal_flash.o(i.HAL_FLASH_Unlock)
    i.HAL_GPIO_Init                          0x080015f0   Section        0  stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_TogglePin                     0x08001758   Section        0  stm32l0xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x08001768   Section        0  stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08001774   Section        0  stm32l0xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08001780   Section        0  stm32l0xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08001790   Section        0  stm32l0xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080017c0   Section        0  stm32l0xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001808   Section        0  stm32l0xx_hal.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x0800180c   Section        0  stm32l0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08001824   Section        0  stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_PWR_EnableBkUpAccess               0x0800182c   Section        0  stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess)
    i.HAL_RCCEx_PeriphCLKConfig              0x0800183c   Section        0  stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x080019dc   Section        0  stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08001ba0   Section        0  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08001bac   Section        0  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08001bcc   Section        0  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08001bec   Section        0  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001c80   Section        0  stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_RTCEx_BKUPRead                     0x08002186   Section        0  stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPRead)
    i.HAL_RTCEx_BKUPWrite                    0x08002192   Section        0  stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPWrite)
    i.HAL_RTC_GetDate                        0x080021a0   Section        0  stm32l0xx_hal_rtc.o(i.HAL_RTC_GetDate)
    i.HAL_RTC_GetTime                        0x080021e4   Section        0  stm32l0xx_hal_rtc.o(i.HAL_RTC_GetTime)
    i.HAL_RTC_Init                           0x0800223c   Section        0  stm32l0xx_hal_rtc.o(i.HAL_RTC_Init)
    i.HAL_RTC_MspInit                        0x08002308   Section        0  stm32l0xx_hal_rtc.o(i.HAL_RTC_MspInit)
    i.HAL_RTC_SetDate                        0x0800230c   Section        0  stm32l0xx_hal_rtc.o(i.HAL_RTC_SetDate)
    i.HAL_RTC_SetTime                        0x080023e8   Section        0  stm32l0xx_hal_rtc.o(i.HAL_RTC_SetTime)
    i.HAL_RTC_WaitForSynchro                 0x080024e4   Section        0  stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro)
    i.HAL_SYSTICK_Config                     0x08002518   Section        0  stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_UART_Init                          0x08002548   Section        0  stm32l0xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x080025b8   Section        0  stm32l0xx_hal_uart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Transmit                      0x080025ba   Section        0  stm32l0xx_hal_uart.o(i.HAL_UART_Transmit)
    i.MX_GPIO_Init                           0x080026a4   Section        0  main.o(i.MX_GPIO_Init)
    MX_GPIO_Init                             0x080026a5   Thumb Code    54  main.o(i.MX_GPIO_Init)
    i.RTC_Bcd2ToByte                         0x080026e8   Section        0  stm32l0xx_hal_rtc.o(i.RTC_Bcd2ToByte)
    i.RTC_ByteToBcd2                         0x080026f8   Section        0  stm32l0xx_hal_rtc.o(i.RTC_ByteToBcd2)
    i.RTC_EnterInitMode                      0x0800270e   Section        0  stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode)
    i.SysTick_Handler                        0x08002746   Section        0  board.o(i.SysTick_Handler)
    i.SystemCoreClockUpdate                  0x08002760   Section        0  system_stm32l0xx.o(i.SystemCoreClockUpdate)
    i.SystemInit                             0x0800281c   Section        0  system_stm32l0xx.o(i.SystemInit)
    i.UART_AdvFeatureConfig                  0x0800282c   Section        0  stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x080028f8   Section        0  stm32l0xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_SetConfig                         0x08002960   Section        0  stm32l0xx_hal_uart.o(i.UART_SetConfig)
    i.UART_WaitOnFlagUntilTimeout            0x08002bc8   Section        0  stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08002c6c   Section        0  net.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08002cb4   Section        0  uart.o(i.USART2_IRQHandler)
    i.__0printf                              0x08002cf4   Section        0  printfa.o(i.__0printf)
    i.__0snprintf                            0x08002d14   Section        0  printfa.o(i.__0snprintf)
    i.__0sprintf                             0x08002d4c   Section        0  printfa.o(i.__0sprintf)
    i.__ARM_clz                              0x08002d74   Section        0  depilogue.o(i.__ARM_clz)
    i.__ARM_common_ll_muluu                  0x08002da2   Section        0  stm32l0xx_hal_rcc.o(i.__ARM_common_ll_muluu)
    i.__ARM_common_switch8                   0x08002dd2   Section        0  pc_protocol.o(i.__ARM_common_switch8)
    i.__NVIC_SetPriority                     0x08002dec   Section        0  stm32l0xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08002ded   Thumb Code    60  stm32l0xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__aeabi_errno_addr                     0x08002e30   Section        0  errno.o(i.__aeabi_errno_addr)
    i.__rt_ffs                               0x08002e38   Section        0  kservice.o(i.__rt_ffs)
    i.__scatterload_copy                     0x08002e7c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08002e8a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08002e8c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._cmd_get_ip_sim_                       0x08002e9c   Section        0  net_protocol.o(i._cmd_get_ip_sim_)
    _cmd_get_ip_sim_                         0x08002e9d   Thumb Code   126  net_protocol.o(i._cmd_get_ip_sim_)
    i._cmd_get_param_                        0x08002f24   Section        0  net_protocol.o(i._cmd_get_param_)
    _cmd_get_param_                          0x08002f25   Thumb Code   204  net_protocol.o(i._cmd_get_param_)
    i._cmd_get_time_                         0x08002ff8   Section        0  net_protocol.o(i._cmd_get_time_)
    _cmd_get_time_                           0x08002ff9   Thumb Code    18  net_protocol.o(i._cmd_get_time_)
    i._cmd_init_                             0x08003010   Section        0  net_protocol.o(i._cmd_init_)
    _cmd_init_                               0x08003011   Thumb Code    40  net_protocol.o(i._cmd_init_)
    i._cmd_set_address_                      0x0800303c   Section        0  net_protocol.o(i._cmd_set_address_)
    _cmd_set_address_                        0x0800303d   Thumb Code    36  net_protocol.o(i._cmd_set_address_)
    i._cmd_set_ip_sim_                       0x08003068   Section        0  net_protocol.o(i._cmd_set_ip_sim_)
    _cmd_set_ip_sim_                         0x08003069   Thumb Code   138  net_protocol.o(i._cmd_set_ip_sim_)
    i._cmd_set_param_                        0x080030fc   Section        0  net_protocol.o(i._cmd_set_param_)
    _cmd_set_param_                          0x080030fd   Thumb Code   268  net_protocol.o(i._cmd_set_param_)
    i._cmd_set_time_                         0x08003210   Section        0  net_protocol.o(i._cmd_set_time_)
    _cmd_set_time_                           0x08003211   Thumb Code    52  net_protocol.o(i._cmd_set_time_)
    i._cmd_timing_upload_                    0x08003248   Section        0  net_protocol.o(i._cmd_timing_upload_)
    _cmd_timing_upload_                      0x08003249   Thumb Code    42  net_protocol.o(i._cmd_timing_upload_)
    i._fp_digits                             0x08003280   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08003281   Thumb Code   344  printfa.o(i._fp_digits)
    i._get_highest_priority_thread           0x080033f4   Section        0  scheduler.o(i._get_highest_priority_thread)
    _get_highest_priority_thread             0x080033f5   Thumb Code    28  scheduler.o(i._get_highest_priority_thread)
    i._has_defunct_thread                    0x08003418   Section        0  idle.o(i._has_defunct_thread)
    _has_defunct_thread                      0x08003419   Thumb Code    16  idle.o(i._has_defunct_thread)
    i._printf_core                           0x0800342c   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x0800342d   Thumb Code  1760  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08003b1c   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08003b1d   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08003b3c   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08003b3d   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._rt_scheduler_stack_check              0x08003b68   Section        0  scheduler.o(i._rt_scheduler_stack_check)
    _rt_scheduler_stack_check                0x08003b69   Thumb Code    50  scheduler.o(i._rt_scheduler_stack_check)
    i._rt_thread_init                        0x08003bb4   Section        0  thread.o(i._rt_thread_init)
    _rt_thread_init                          0x08003bb5   Thumb Code   132  thread.o(i._rt_thread_init)
    i._rt_timer_init                         0x08003c6c   Section        0  timer.o(i._rt_timer_init)
    _rt_timer_init                           0x08003c6d   Thumb Code    38  timer.o(i._rt_timer_init)
    i._rt_timer_remove                       0x08003c92   Section        0  timer.o(i._rt_timer_remove)
    _rt_timer_remove                         0x08003c93   Thumb Code    34  timer.o(i._rt_timer_remove)
    i._snputc                                0x08003cb4   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08003cb5   Thumb Code    22  printfa.o(i._snputc)
    i._sputc                                 0x08003cca   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08003ccb   Thumb Code    10  printfa.o(i._sputc)
    i.auto_ctrl                              0x08003cd4   Section        0  auto_ctrl_task.o(i.auto_ctrl)
    i.auto_ctrl_task_process                 0x08003cd6   Section        0  main.o(i.auto_ctrl_task_process)
    auto_ctrl_task_process                   0x08003cd7   Thumb Code     8  main.o(i.auto_ctrl_task_process)
    i.bsp_board_init                         0x08003ce0   Section        0  bsp_board.o(i.bsp_board_init)
    i.build_read_command                     0x08003d20   Section        0  daosheng_protocol.o(i.build_read_command)
    build_read_command                       0x08003d21   Thumb Code    38  daosheng_protocol.o(i.build_read_command)
    i.close                                  0x08003d48   Section        0  ec600m_tcpip.o(i.close)
    close                                    0x08003d49   Thumb Code    54  ec600m_tcpip.o(i.close)
    i.config_refresh                         0x08003da0   Section        0  user_config.o(i.config_refresh)
    i.connect                                0x08003dc4   Section        0  ec600m_tcpip.o(i.connect)
    connect                                  0x08003dc5   Thumb Code   168  ec600m_tcpip.o(i.connect)
    i.debug_init                             0x08003edc   Section        0  debug.o(i.debug_init)
    i.dec_to_hex                             0x08003f50   Section        0  net_protocol.o(i.dec_to_hex)
    dec_to_hex                               0x08003f51   Thumb Code    60  net_protocol.o(i.dec_to_hex)
    i.dec_to_hex_to_byte                     0x08003f8c   Section        0  net_protocol.o(i.dec_to_hex_to_byte)
    dec_to_hex_to_byte                       0x08003f8d   Thumb Code    24  net_protocol.o(i.dec_to_hex_to_byte)
    i.delay_us                               0x08003fa4   Section        0  board.o(i.delay_us)
    i.dev_delay                              0x08003fdc   Section        0  board.o(i.dev_delay)
    dev_delay                                0x08003fdd   Thumb Code    38  board.o(i.dev_delay)
    i.display_battery_voltage                0x0800400c   Section        0  lcd12864.o(i.display_battery_voltage)
    i.display_flow_rate                      0x0800403c   Section        0  lcd12864.o(i.display_flow_rate)
    i.display_flow_rate1                     0x080040f8   Section        0  lcd12864.o(i.display_flow_rate1)
    display_flow_rate1                       0x080040f9   Thumb Code   150  lcd12864.o(i.display_flow_rate1)
    i.display_rotate                         0x080041fc   Section        0  lcd12864.o(i.display_rotate)
    i.ec600m_base_function_register          0x08004234   Section        0  ec600m_common.o(i.ec600m_base_function_register)
    i.ec600m_tcpip_register                  0x08004240   Section        0  ec600m_tcpip.o(i.ec600m_tcpip_register)
    i.flow_meter_protocol_parsing            0x0800424c   Section        0  flow_meter_protocol.o(i.flow_meter_protocol_parsing)
    i.fputc                                  0x08004250   Section        0  debug.o(i.fputc)
    i.free                                   0x08004268   Section        0  malloc.o(i.free)
    i.get_font_info                          0x080042bc   Section        0  lcd_font.o(i.get_font_info)
    i.get_font_size                          0x08004398   Section        0  lcd_font.o(i.get_font_size)
    i.get_font_type                          0x080043c4   Section        0  lcd_font.o(i.get_font_type)
    i.get_hex_data                           0x080043d4   Section        0  net_protocol.o(i.get_hex_data)
    get_hex_data                             0x080043d5   Thumb Code    40  net_protocol.o(i.get_hex_data)
    i.get_info                               0x08004404   Section        0  ec600m_common.o(i.get_info)
    get_info                                 0x08004405   Thumb Code   112  ec600m_common.o(i.get_info)
    i.get_net_info                           0x08004494   Section        0  net_protocol.o(i.get_net_info)
    get_net_info                             0x08004495   Thumb Code   126  net_protocol.o(i.get_net_info)
    i.get_net_sig                            0x0800451c   Section        0  ec600m_common.o(i.get_net_sig)
    get_net_sig                              0x0800451d   Thumb Code    70  ec600m_common.o(i.get_net_sig)
    i.get_serial_port                        0x0800457c   Section        0  protocol_common.o(i.get_serial_port)
    get_serial_port                          0x0800457d   Thumb Code    54  protocol_common.o(i.get_serial_port)
    i.get_water_value                        0x080045bc   Section        0  daosheng_protocol.o(i.get_water_value)
    i.hex_to_dec                             0x080045c4   Section        0  net_protocol.o(i.hex_to_dec)
    hex_to_dec                               0x080045c5   Thumb Code    68  net_protocol.o(i.hex_to_dec)
    i.lcd_clear                              0x08004608   Section        0  lcd12864.o(i.lcd_clear)
    i.lcd_draw_pixel                         0x08004638   Section        0  lcd12864.o(i.lcd_draw_pixel)
    lcd_draw_pixel                           0x08004639   Thumb Code    52  lcd12864.o(i.lcd_draw_pixel)
    i.lcd_gpio_init                          0x08004670   Section        0  lcd12864.o(i.lcd_gpio_init)
    lcd_gpio_init                            0x08004671   Thumb Code    88  lcd12864.o(i.lcd_gpio_init)
    i.lcd_init                               0x080046d0   Section        0  lcd12864.o(i.lcd_init)
    i.lcd_refresh                            0x08004784   Section        0  lcd12864.o(i.lcd_refresh)
    i.lcd_show_char                          0x080047c8   Section        0  lcd12864.o(i.lcd_show_char)
    lcd_show_char                            0x080047c9   Thumb Code   128  lcd12864.o(i.lcd_show_char)
    i.lcd_show_string                        0x08004848   Section        0  lcd12864.o(i.lcd_show_string)
    i.lcd_write_command                      0x080048f0   Section        0  lcd12864.o(i.lcd_write_command)
    lcd_write_command                        0x080048f1   Thumb Code    82  lcd12864.o(i.lcd_write_command)
    i.lcd_write_data                         0x08004948   Section        0  lcd12864.o(i.lcd_write_data)
    lcd_write_data                           0x08004949   Thumb Code    82  lcd12864.o(i.lcd_write_data)
    i.main                                   0x080049a0   Section        0  main.o(i.main)
    i.main_thread_entry                      0x08004adc   Section        0  components.o(i.main_thread_entry)
    i.malloc                                 0x08004ae4   Section        0  malloc.o(i.malloc)
    i.mbus_crc16                             0x08004b50   Section        0  daosheng_protocol.o(i.mbus_crc16)
    i.modbus_crc16                           0x08004b88   Section        0  modbus_rtu.o(i.modbus_crc16)
    i.modbus_crc16                           0x08004bc0   Section        0  net_protocol.o(i.modbus_crc16)
    modbus_crc16                             0x08004bc1   Thumb Code    48  net_protocol.o(i.modbus_crc16)
    i.net_clean                              0x08004bf8   Section        0  net.o(i.net_clean)
    net_clean                                0x08004bf9   Thumb Code    24  net.o(i.net_clean)
    i.net_data_parsing                       0x08004c14   Section        0  net_protocol.o(i.net_data_parsing)
    net_data_parsing                         0x08004c15   Thumb Code    70  net_protocol.o(i.net_data_parsing)
    i.net_init                               0x08004c64   Section        0  net.o(i.net_init)
    i.net_module_init                        0x08004cb0   Section        0  net.o(i.net_module_init)
    net_module_init                          0x08004cb1   Thumb Code    70  net.o(i.net_module_init)
    i.net_module_reboot                      0x08004d00   Section        0  net.o(i.net_module_reboot)
    i.net_protocol_parsing                   0x08004d84   Section        0  net_protocol.o(i.net_protocol_parsing)
    i.net_send_cmd                           0x08004e2c   Section        0  net.o(i.net_send_cmd)
    net_send_cmd                             0x08004e2d   Thumb Code    32  net.o(i.net_send_cmd)
    i.net_send_data                          0x08004e54   Section        0  net.o(i.net_send_data)
    net_send_data                            0x08004e55   Thumb Code    20  net.o(i.net_send_data)
    i.net_send_packet                        0x08004e6c   Section        0  net_protocol.o(i.net_send_packet)
    net_send_packet                          0x08004e6d   Thumb Code   326  net_protocol.o(i.net_send_packet)
    i.net_serial_init                        0x08004fc4   Section        0  net.o(i.net_serial_init)
    net_serial_init                          0x08004fc5   Thumb Code   164  net.o(i.net_serial_init)
    i.net_task_init                          0x08005078   Section        0  main.o(i.net_task_init)
    net_task_init                            0x08005079   Thumb Code   128  main.o(i.net_task_init)
    i.net_task_process                       0x08005110   Section        0  main.o(i.net_task_process)
    net_task_process                         0x08005111   Thumb Code    40  main.o(i.net_task_process)
    i.pc_protocol_parsing                    0x0800513c   Section        0  pc_protocol.o(i.pc_protocol_parsing)
    i.plug_holes                             0x08005374   Section        0  mem.o(i.plug_holes)
    plug_holes                               0x08005375   Thumb Code   134  mem.o(i.plug_holes)
    i.print_number                           0x08005460   Section        0  kservice.o(i.print_number)
    print_number                             0x08005461   Thumb Code   318  kservice.o(i.print_number)
    i.protocol_parsing                       0x080055a4   Section        0  protocol_common.o(i.protocol_parsing)
    i.read_config                            0x08005680   Section        0  user_config.o(i.read_config)
    i.recv                                   0x080056f4   Section        0  ec600m_tcpip.o(i.recv)
    recv                                     0x080056f5   Thumb Code    28  ec600m_tcpip.o(i.recv)
    i.rt_application_init                    0x08005718   Section        0  components.o(i.rt_application_init)
    i.rt_assert_handler                      0x08005768   Section        0  kservice.o(i.rt_assert_handler)
    i.rt_enter_critical                      0x0800578c   Section        0  scheduler.o(i.rt_enter_critical)
    i.rt_exit_critical                       0x080057a4   Section        0  scheduler.o(i.rt_exit_critical)
    i.rt_free                                0x080057d8   Section        0  mem.o(i.rt_free)
    i.rt_heap_begin_get                      0x0800590c   Section        0  board.o(i.rt_heap_begin_get)
    i.rt_heap_end_get                        0x08005914   Section        0  board.o(i.rt_heap_end_get)
    i.rt_hw_board_init                       0x0800591c   Section        0  board.o(i.rt_hw_board_init)
    i.rt_hw_hard_fault_exception             0x0800594c   Section        0  cpuport.o(i.rt_hw_hard_fault_exception)
    i.rt_hw_stack_init                       0x08005950   Section        0  cpuport.o(i.rt_hw_stack_init)
    i.rt_interrupt_enter                     0x0800598c   Section        0  irq.o(i.rt_interrupt_enter)
    i.rt_interrupt_get_nest                  0x080059b0   Section        0  irq.o(i.rt_interrupt_get_nest)
    i.rt_interrupt_leave                     0x080059c8   Section        0  irq.o(i.rt_interrupt_leave)
    i.rt_ipc_list_resume                     0x080059ec   Section        0  ipc.o(i.rt_ipc_list_resume)
    rt_ipc_list_resume                       0x080059ed   Thumb Code    14  ipc.o(i.rt_ipc_list_resume)
    i.rt_ipc_list_suspend                    0x080059fa   Section        0  ipc.o(i.rt_ipc_list_suspend)
    rt_ipc_list_suspend                      0x080059fb   Thumb Code    90  ipc.o(i.rt_ipc_list_suspend)
    i.rt_list_insert_after                   0x08005a54   Section        0  object.o(i.rt_list_insert_after)
    rt_list_insert_after                     0x08005a55   Thumb Code    14  object.o(i.rt_list_insert_after)
    i.rt_list_insert_after                   0x08005a62   Section        0  thread.o(i.rt_list_insert_after)
    rt_list_insert_after                     0x08005a63   Thumb Code    14  thread.o(i.rt_list_insert_after)
    i.rt_list_insert_after                   0x08005a70   Section        0  timer.o(i.rt_list_insert_after)
    rt_list_insert_after                     0x08005a71   Thumb Code    14  timer.o(i.rt_list_insert_after)
    i.rt_list_insert_before                  0x08005a7e   Section        0  ipc.o(i.rt_list_insert_before)
    rt_list_insert_before                    0x08005a7f   Thumb Code    14  ipc.o(i.rt_list_insert_before)
    i.rt_list_isempty                        0x08005a8c   Section        0  ipc.o(i.rt_list_isempty)
    rt_list_isempty                          0x08005a8d   Thumb Code    14  ipc.o(i.rt_list_isempty)
    i.rt_list_isempty                        0x08005a9a   Section        0  timer.o(i.rt_list_isempty)
    rt_list_isempty                          0x08005a9b   Thumb Code    14  timer.o(i.rt_list_isempty)
    i.rt_list_remove                         0x08005aa8   Section        0  object.o(i.rt_list_remove)
    rt_list_remove                           0x08005aa9   Thumb Code    18  object.o(i.rt_list_remove)
    i.rt_list_remove                         0x08005aba   Section        0  thread.o(i.rt_list_remove)
    rt_list_remove                           0x08005abb   Thumb Code    18  thread.o(i.rt_list_remove)
    i.rt_malloc                              0x08005acc   Section        0  mem.o(i.rt_malloc)
    i.rt_memset                              0x08005cb0   Section        0  kservice.o(i.rt_memset)
    i.rt_object_allocate                     0x08005cf8   Section        0  object.o(i.rt_object_allocate)
    i.rt_object_delete                       0x08005da8   Section        0  object.o(i.rt_object_delete)
    i.rt_object_detach                       0x08005e44   Section        0  object.o(i.rt_object_detach)
    i.rt_object_get_information              0x08005e9c   Section        0  object.o(i.rt_object_get_information)
    i.rt_object_get_type                     0x08005ebc   Section        0  object.o(i.rt_object_get_type)
    i.rt_object_init                         0x08005ef0   Section        0  object.o(i.rt_object_init)
    i.rt_object_is_systemobject              0x08005f9c   Section        0  object.o(i.rt_object_is_systemobject)
    i.rt_schedule                            0x08005fd8   Section        0  scheduler.o(i.rt_schedule)
    i.rt_schedule_insert_thread              0x080060b0   Section        0  scheduler.o(i.rt_schedule_insert_thread)
    i.rt_schedule_remove_thread              0x08006134   Section        0  scheduler.o(i.rt_schedule_remove_thread)
    i.rt_sem_init                            0x080061b0   Section        0  ipc.o(i.rt_sem_init)
    i.rt_sem_release                         0x0800621c   Section        0  ipc.o(i.rt_sem_release)
    i.rt_sem_take                            0x080062a8   Section        0  ipc.o(i.rt_sem_take)
    i.rt_show_version                        0x080063c0   Section        0  kservice.o(i.rt_show_version)
    i.rt_sprintf                             0x080063c2   Section        0  kservice.o(i.rt_sprintf)
    i.rt_strlen                              0x080063d6   Section        0  kservice.o(i.rt_strlen)
    i.rt_strncpy                             0x080063e6   Section        0  kservice.o(i.rt_strncpy)
    i.rt_system_heap_init                    0x08006410   Section        0  mem.o(i.rt_system_heap_init)
    i.rt_system_scheduler_init               0x08006498   Section        0  scheduler.o(i.rt_system_scheduler_init)
    i.rt_system_scheduler_start              0x080064c8   Section        0  scheduler.o(i.rt_system_scheduler_start)
    i.rt_system_timer_init                   0x080064f0   Section        0  timer.o(i.rt_system_timer_init)
    i.rt_system_timer_thread_init            0x08006508   Section        0  timer.o(i.rt_system_timer_thread_init)
    i.rt_thread_create                       0x0800650a   Section        0  thread.o(i.rt_thread_create)
    i.rt_thread_exit                         0x08006550   Section        0  thread.o(i.rt_thread_exit)
    i.rt_thread_idle_entry                   0x080065a4   Section        0  idle.o(i.rt_thread_idle_entry)
    rt_thread_idle_entry                     0x080065a5   Thumb Code    28  idle.o(i.rt_thread_idle_entry)
    i.rt_thread_idle_excute                  0x080065c4   Section        0  idle.o(i.rt_thread_idle_excute)
    i.rt_thread_idle_init                    0x0800666c   Section        0  idle.o(i.rt_thread_idle_init)
    i.rt_thread_init                         0x080066c4   Section        0  thread.o(i.rt_thread_init)
    i.rt_thread_resume                       0x0800673c   Section        0  thread.o(i.rt_thread_resume)
    i.rt_thread_self                         0x080067cc   Section        0  thread.o(i.rt_thread_self)
    i.rt_thread_startup                      0x080067d8   Section        0  thread.o(i.rt_thread_startup)
    i.rt_thread_suspend                      0x08006890   Section        0  thread.o(i.rt_thread_suspend)
    i.rt_thread_timeout                      0x0800695c   Section        0  thread.o(i.rt_thread_timeout)
    i.rt_thread_yield                        0x08006a14   Section        0  thread.o(i.rt_thread_yield)
    i.rt_tick_get                            0x08006a20   Section        0  clock.o(i.rt_tick_get)
    i.rt_tick_increase                       0x08006a2c   Section        0  clock.o(i.rt_tick_increase)
    i.rt_timer_check                         0x08006a60   Section        0  timer.o(i.rt_timer_check)
    i.rt_timer_control                       0x08006ae4   Section        0  timer.o(i.rt_timer_control)
    i.rt_timer_detach                        0x08006b9c   Section        0  timer.o(i.rt_timer_detach)
    i.rt_timer_init                          0x08006c74   Section        0  timer.o(i.rt_timer_init)
    i.rt_timer_start                         0x08006cc4   Section        0  timer.o(i.rt_timer_start)
    i.rt_timer_stop                          0x08006e50   Section        0  timer.o(i.rt_timer_stop)
    i.rt_vsnprintf                           0x08006f08   Section        0  kservice.o(i.rt_vsnprintf)
    i.rt_vsprintf                            0x080071a4   Section        0  kservice.o(i.rt_vsprintf)
    i.rtc_get_calendar                       0x080071b4   Section        0  rtc.o(i.rtc_get_calendar)
    i.rtc_init                               0x08007228   Section        0  rtc.o(i.rtc_init)
    i.rtc_set_time                           0x080072e0   Section        0  rtc.o(i.rtc_set_time)
    i.rtthread_startup                       0x08007320   Section        0  components.o(i.rtthread_startup)
    i.schedule_commands                      0x0800734c   Section        0  daosheng_protocol.o(i.schedule_commands)
    schedule_commands                        0x0800734d   Thumb Code   176  daosheng_protocol.o(i.schedule_commands)
    i.send                                   0x08007408   Section        0  ec600m_tcpip.o(i.send)
    send                                     0x08007409   Thumb Code    24  ec600m_tcpip.o(i.send)
    i.send_cmd                               0x08007424   Section        0  ec600m_common.o(i.send_cmd)
    send_cmd                                 0x08007425   Thumb Code   110  ec600m_common.o(i.send_cmd)
    i.send_data                              0x08007498   Section        0  ec600m_common.o(i.send_data)
    send_data                                0x08007499   Thumb Code    12  ec600m_common.o(i.send_data)
    i.serial_485_init                        0x080074a8   Section        0  uart.o(i.serial_485_init)
    i.serial_485_send_dat                    0x080075fc   Section        0  uart.o(i.serial_485_send_dat)
    i.set_net_info                           0x0800763c   Section        0  net_protocol.o(i.set_net_info)
    set_net_info                             0x0800763d   Thumb Code    92  net_protocol.o(i.set_net_info)
    i.skip_atoi                              0x0800769c   Section        0  kservice.o(i.skip_atoi)
    skip_atoi                                0x0800769d   Thumb Code    32  kservice.o(i.skip_atoi)
    i.stm32_flash_erase                      0x080076bc   Section        0  stm32_flash.o(i.stm32_flash_erase)
    stm32_flash_erase                        0x080076bd   Thumb Code    42  stm32_flash.o(i.stm32_flash_erase)
    i.stm32_flash_read                       0x080076e6   Section        0  stm32_flash.o(i.stm32_flash_read)
    i.stm32_flash_read_byte                  0x08007700   Section        0  stm32_flash.o(i.stm32_flash_read_byte)
    i.stm32_flash_write                      0x08007704   Section        0  stm32_flash.o(i.stm32_flash_write)
    i.stm32_flash_write_nbyte                0x08007810   Section        0  stm32_flash.o(i.stm32_flash_write_nbyte)
    stm32_flash_write_nbyte                  0x08007811   Thumb Code    40  stm32_flash.o(i.stm32_flash_write_nbyte)
    i.sync_time                              0x08007838   Section        0  ec600m_common.o(i.sync_time)
    sync_time                                0x08007839   Thumb Code   254  ec600m_common.o(i.sync_time)
    i.system_clock_init                      0x08007958   Section        0  system.o(i.system_clock_init)
    i.water_meter_protocol_parsing           0x080079d4   Section        0  water_meter_protocol.o(i.water_meter_protocol_parsing)
    i.watting                                0x080079d8   Section        0  ec600m_common.o(i.watting)
    watting                                  0x080079d9   Thumb Code    88  ec600m_common.o(i.watting)
    .constdata                               0x08007a34   Section       14  pc_protocol.o(.constdata)
    .constdata                               0x08007a42   Section     7212  lcd_font.o(.constdata)
    ascii_08_08                              0x08007a42   Data         760  lcd_font.o(.constdata)
    ascii_16_08                              0x08007d3a   Data        1520  lcd_font.o(.constdata)
    ascii_24_24                              0x0800832a   Data        3420  lcd_font.o(.constdata)
    chiness_16_16                            0x08009086   Data        1280  lcd_font.o(.constdata)
    chiness_24_24                            0x08009586   Data          72  lcd_font.o(.constdata)
    font_table                               0x080095ce   Data         160  lcd_font.o(.constdata)
    .constdata                               0x0800966e   Section       25  system_stm32l0xx.o(.constdata)
    .constdata                               0x08009687   Section        8  system_stm32l0xx.o(.constdata)
    .constdata                               0x0800968f   Section       20  components.o(.constdata)
    __FUNCTION__                             0x0800968f   Data          20  components.o(.constdata)
    .constdata                               0x080096a3   Section       22  idle.o(.constdata)
    __FUNCTION__                             0x080096a3   Data          22  idle.o(.constdata)
    .constdata                               0x080096b9   Section      508  ipc.o(.constdata)
    __FUNCTION__                             0x080096b9   Data          12  ipc.o(.constdata)
    __FUNCTION__                             0x080096c5   Data          14  ipc.o(.constdata)
    __FUNCTION__                             0x080096d3   Data          14  ipc.o(.constdata)
    __FUNCTION__                             0x080096e1   Data          14  ipc.o(.constdata)
    __FUNCTION__                             0x080096ef   Data          12  ipc.o(.constdata)
    __FUNCTION__                             0x080096fb   Data          15  ipc.o(.constdata)
    __FUNCTION__                             0x0800970a   Data          15  ipc.o(.constdata)
    __FUNCTION__                             0x08009719   Data          14  ipc.o(.constdata)
    __FUNCTION__                             0x08009727   Data          16  ipc.o(.constdata)
    __FUNCTION__                             0x08009737   Data          16  ipc.o(.constdata)
    __FUNCTION__                             0x08009747   Data          16  ipc.o(.constdata)
    __FUNCTION__                             0x08009757   Data          14  ipc.o(.constdata)
    __FUNCTION__                             0x08009765   Data          17  ipc.o(.constdata)
    __FUNCTION__                             0x08009776   Data          17  ipc.o(.constdata)
    __FUNCTION__                             0x08009787   Data          14  ipc.o(.constdata)
    __FUNCTION__                             0x08009795   Data          16  ipc.o(.constdata)
    __FUNCTION__                             0x080097a5   Data          16  ipc.o(.constdata)
    __FUNCTION__                             0x080097b5   Data          16  ipc.o(.constdata)
    __FUNCTION__                             0x080097c5   Data          14  ipc.o(.constdata)
    __FUNCTION__                             0x080097d3   Data          14  ipc.o(.constdata)
    __FUNCTION__                             0x080097e1   Data          17  ipc.o(.constdata)
    __FUNCTION__                             0x080097f2   Data          11  ipc.o(.constdata)
    __FUNCTION__                             0x080097fd   Data          13  ipc.o(.constdata)
    __FUNCTION__                             0x0800980a   Data          13  ipc.o(.constdata)
    __FUNCTION__                             0x08009817   Data          13  ipc.o(.constdata)
    __FUNCTION__                             0x08009824   Data          16  ipc.o(.constdata)
    __FUNCTION__                             0x08009834   Data          11  ipc.o(.constdata)
    __FUNCTION__                             0x0800983f   Data          14  ipc.o(.constdata)
    __FUNCTION__                             0x0800984d   Data          11  ipc.o(.constdata)
    __FUNCTION__                             0x08009858   Data          13  ipc.o(.constdata)
    __FUNCTION__                             0x08009865   Data          13  ipc.o(.constdata)
    __FUNCTION__                             0x08009872   Data          13  ipc.o(.constdata)
    __FUNCTION__                             0x0800987f   Data          16  ipc.o(.constdata)
    __FUNCTION__                             0x0800988f   Data          13  ipc.o(.constdata)
    __FUNCTION__                             0x0800989c   Data          11  ipc.o(.constdata)
    __FUNCTION__                             0x080098a7   Data          14  ipc.o(.constdata)
    .constdata                               0x080098b5   Section      290  kservice.o(.constdata)
    small_digits                             0x080099b5   Data          17  kservice.o(.constdata)
    large_digits                             0x080099c6   Data          17  kservice.o(.constdata)
    .constdata                               0x080099d7   Section       60  mem.o(.constdata)
    __FUNCTION__                             0x080099d7   Data           8  mem.o(.constdata)
    __FUNCTION__                             0x080099df   Data          11  mem.o(.constdata)
    __FUNCTION__                             0x080099ea   Data          20  mem.o(.constdata)
    __FUNCTION__                             0x080099fe   Data          10  mem.o(.constdata)
    __FUNCTION__                             0x08009a08   Data          11  mem.o(.constdata)
    .constdata                               0x08009a13   Section      128  object.o(.constdata)
    __FUNCTION__                             0x08009a13   Data          15  object.o(.constdata)
    __FUNCTION__                             0x08009a22   Data          17  object.o(.constdata)
    __FUNCTION__                             0x08009a33   Data          19  object.o(.constdata)
    __FUNCTION__                             0x08009a46   Data          17  object.o(.constdata)
    __FUNCTION__                             0x08009a57   Data          26  object.o(.constdata)
    __FUNCTION__                             0x08009a71   Data          19  object.o(.constdata)
    __FUNCTION__                             0x08009a84   Data          15  object.o(.constdata)
    .constdata                               0x08009a93   Section       78  scheduler.o(.constdata)
    __FUNCTION__                             0x08009a93   Data          26  scheduler.o(.constdata)
    __FUNCTION__                             0x08009aad   Data          26  scheduler.o(.constdata)
    __FUNCTION__                             0x08009ac7   Data          26  scheduler.o(.constdata)
    .constdata                               0x08009ae1   Section      207  thread.o(.constdata)
    __FUNCTION__                             0x08009ae1   Data          16  thread.o(.constdata)
    __FUNCTION__                             0x08009af1   Data          15  thread.o(.constdata)
    __FUNCTION__                             0x08009b00   Data          18  thread.o(.constdata)
    __FUNCTION__                             0x08009b12   Data          17  thread.o(.constdata)
    __FUNCTION__                             0x08009b23   Data          17  thread.o(.constdata)
    __FUNCTION__                             0x08009b34   Data          16  thread.o(.constdata)
    __FUNCTION__                             0x08009b44   Data          22  thread.o(.constdata)
    __FUNCTION__                             0x08009b5a   Data          18  thread.o(.constdata)
    __FUNCTION__                             0x08009b6c   Data          18  thread.o(.constdata)
    __FUNCTION__                             0x08009b7e   Data          17  thread.o(.constdata)
    __FUNCTION__                             0x08009b8f   Data          18  thread.o(.constdata)
    __FUNCTION__                             0x08009ba1   Data          15  thread.o(.constdata)
    .constdata                               0x08009bb0   Section       92  timer.o(.constdata)
    __FUNCTION__                             0x08009bb0   Data          14  timer.o(.constdata)
    __FUNCTION__                             0x08009bbe   Data          16  timer.o(.constdata)
    __FUNCTION__                             0x08009bce   Data          16  timer.o(.constdata)
    __FUNCTION__                             0x08009bde   Data          15  timer.o(.constdata)
    __FUNCTION__                             0x08009bed   Data          14  timer.o(.constdata)
    __FUNCTION__                             0x08009bfb   Data          17  timer.o(.constdata)
    .constdata                               0x08009c0c   Section      129  ctype_o.o(.constdata)
    .constdata                               0x08009c90   Section        4  ctype_o.o(.constdata)
    table                                    0x08009c90   Data           4  ctype_o.o(.constdata)
    .conststring                             0x08009c94   Section       69  ipc.o(.conststring)
    .conststring                             0x08009cdc   Section      235  mem.o(.conststring)
    .conststring                             0x08009dc8   Section       66  thread.o(.conststring)
    .data                                    0x20000000   Section       28  main.o(.data)
    .data                                    0x2000001c   Section        4  protocol_common.o(.data)
    .data                                    0x20000020   Section       32  daosheng_protocol.o(.data)
    cmd_state                                0x20000020   Data           1  daosheng_protocol.o(.data)
    last_send_time                           0x20000024   Data           4  daosheng_protocol.o(.data)
    last_reset_time                          0x20000028   Data           4  daosheng_protocol.o(.data)
    .data                                    0x20000040   Section        4  net_protocol.o(.data)
    tcpip_connect_flag                       0x20000040   Data           1  net_protocol.o(.data)
    tcpip_disconnect_timeout                 0x20000042   Data           2  net_protocol.o(.data)
    .data                                    0x20000044   Section       20  lcd12864.o(.data)
    last_switch_time                         0x20000048   Data           4  lcd12864.o(.data)
    .data                                    0x20000058   Section      532  net.o(.data)
    .data                                    0x2000026c   Section       24  ec600m_common.o(.data)
    .data                                    0x20000284   Section       16  ec600m_tcpip.o(.data)
    ec600m_tcpip                             0x20000284   Data          16  ec600m_tcpip.o(.data)
    .data                                    0x20000294   Section        4  system_stm32l0xx.o(.data)
    .data                                    0x20000298   Section       12  stm32l0xx_hal.o(.data)
    .data                                    0x200002a4   Section        4  clock.o(.data)
    rt_tick                                  0x200002a4   Data           4  clock.o(.data)
    .data                                    0x200002a8   Section       12  irq.o(.data)
    rt_interrupt_enter_hook                  0x200002ac   Data           4  irq.o(.data)
    rt_interrupt_leave_hook                  0x200002b0   Data           4  irq.o(.data)
    .data                                    0x200002b4   Section        8  kservice.o(.data)
    __rt_errno                               0x200002b4   Data           4  kservice.o(.data)
    .data                                    0x200002bc   Section       32  mem.o(.data)
    rt_malloc_hook                           0x200002bc   Data           4  mem.o(.data)
    rt_free_hook                             0x200002c0   Data           4  mem.o(.data)
    heap_ptr                                 0x200002c4   Data           4  mem.o(.data)
    heap_end                                 0x200002c8   Data           4  mem.o(.data)
    lfree                                    0x200002cc   Data           4  mem.o(.data)
    mem_size_aligned                         0x200002d0   Data           4  mem.o(.data)
    used_mem                                 0x200002d4   Data           4  mem.o(.data)
    max_mem                                  0x200002d8   Data           4  mem.o(.data)
    .data                                    0x200002dc   Section      148  object.o(.data)
    rt_object_attach_hook                    0x200002dc   Data           4  object.o(.data)
    rt_object_detach_hook                    0x200002e0   Data           4  object.o(.data)
    rt_object_container                      0x200002f0   Data         128  object.o(.data)
    .data                                    0x20000370   Section       24  scheduler.o(.data)
    rt_scheduler_lock_nest                   0x20000372   Data           2  scheduler.o(.data)
    rt_scheduler_hook                        0x2000037c   Data           4  scheduler.o(.data)
    .data                                    0x20000388   Section       12  thread.o(.data)
    rt_thread_suspend_hook                   0x20000388   Data           4  thread.o(.data)
    rt_thread_resume_hook                    0x2000038c   Data           4  thread.o(.data)
    rt_thread_inited_hook                    0x20000390   Data           4  thread.o(.data)
    .data                                    0x20000394   Section       20  timer.o(.data)
    random_nr                                0x20000394   Data           4  timer.o(.data)
    rt_timer_enter_hook                      0x20000398   Data           4  timer.o(.data)
    rt_timer_exit_hook                       0x2000039c   Data           4  timer.o(.data)
    rt_timer_list                            0x200003a0   Data           8  timer.o(.data)
    .data                                    0x200003a8   Section        4  cpuport.o(.data)
    .data                                    0x200003ac   Section        4  cpuport.o(.data)
    .data                                    0x200003b0   Section        4  cpuport.o(.data)
    .data                                    0x200003b4   Section        4  stdout.o(.data)
    .data                                    0x200003b8   Section        4  mvars.o(.data)
    .data                                    0x200003bc   Section        4  mvars.o(.data)
    .data                                    0x200003c0   Section        4  errno.o(.data)
    _errno                                   0x200003c0   Data           4  errno.o(.data)
    .bss                                     0x200003c4   Section       12  main.o(.bss)
    .bss                                     0x200003d0   Section       60  user_config.o(.bss)
    .bss                                     0x2000040c   Section       66  protocol_common.o(.bss)
    serial_data                              0x2000040c   Data          66  protocol_common.o(.bss)
    .bss                                     0x2000044e   Section       16  daosheng_protocol.o(.bss)
    .bss                                     0x2000045e   Section       30  pc_protocol.o(.bss)
    .bss                                     0x2000047c   Section      128  net_protocol.o(.bss)
    hex_buffer                               0x2000047c   Data         128  net_protocol.o(.bss)
    .bss                                     0x200004fc   Section     1024  lcd12864.o(.bss)
    lcd_ram                                  0x200004fc   Data        1024  lcd12864.o(.bss)
    .bss                                     0x200008fc   Section      132  net.o(.bss)
    .bss                                     0x20000980   Section      200  uart.o(.bss)
    .bss                                     0x20000a48   Section      132  debug.o(.bss)
    .bss                                     0x20000acc   Section      128  stm32_flash.o(.bss)
    buffer                                   0x20000acc   Data         128  stm32_flash.o(.bss)
    .bss                                     0x20000b4c   Section      104  adc.o(.bss)
    .bss                                     0x20000bb4   Section       36  rtc.o(.bss)
    .bss                                     0x20000bd8   Section       24  stm32l0xx_hal_flash.o(.bss)
    .bss                                     0x20000bf0   Section    14336  board.o(.bss)
    rt_heap                                  0x20000bf0   Data       14336  board.o(.bss)
    .bss                                     0x200043f0   Section      400  idle.o(.bss)
    idle                                     0x200043f0   Data         128  idle.o(.bss)
    rt_thread_stack                          0x20004470   Data         256  idle.o(.bss)
    idle_hook_list                           0x20004570   Data          16  idle.o(.bss)
    .bss                                     0x20004580   Section       32  mem.o(.bss)
    heap_sem                                 0x20004580   Data          32  mem.o(.bss)
    .bss                                     0x200045a0   Section      256  scheduler.o(.bss)
    HEAP                                     0x200046a0   Section        0  startup_stm32l072xx.o(HEAP)
    STACK                                    0x200046a0   Section      768  startup_stm32l072xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000c0   Number         0  startup_stm32l072xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32l072xx.o(RESET)
    __Vectors_End                            0x080000c0   Data           0  startup_stm32l072xx.o(RESET)
    __main                                   0x080000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080000d1   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080000d5   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080000d5   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080000d9   Thumb Code     8  startup_stm32l072xx.o(.text)
    NMI_Handler                              0x080000e1   Thumb Code     2  startup_stm32l072xx.o(.text)
    SVC_Handler                              0x080000e5   Thumb Code     2  startup_stm32l072xx.o(.text)
    ADC1_COMP_IRQHandler                     0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    DMA1_Channel1_IRQHandler                 0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    DMA1_Channel2_3_IRQHandler               0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    DMA1_Channel4_5_6_7_IRQHandler           0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    EXTI0_1_IRQHandler                       0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    EXTI2_3_IRQHandler                       0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    EXTI4_15_IRQHandler                      0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    FLASH_IRQHandler                         0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    I2C1_IRQHandler                          0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    I2C2_IRQHandler                          0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    I2C3_IRQHandler                          0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    LPTIM1_IRQHandler                        0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    PVD_IRQHandler                           0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    RCC_CRS_IRQHandler                       0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    RNG_LPUART1_IRQHandler                   0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    RTC_IRQHandler                           0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    SPI1_IRQHandler                          0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    SPI2_IRQHandler                          0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    TIM21_IRQHandler                         0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    TIM22_IRQHandler                         0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    TIM2_IRQHandler                          0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    TIM3_IRQHandler                          0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    TIM7_IRQHandler                          0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    TSC_IRQHandler                           0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    USART4_5_IRQHandler                      0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    USB_IRQHandler                           0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    WWDG_IRQHandler                          0x080000eb   Thumb Code     0  startup_stm32l072xx.o(.text)
    rt_hw_interrupt_disable                  0x080000f5   Thumb Code     8  context_rvds.o(.text)
    rt_hw_interrupt_enable                   0x080000fd   Thumb Code     6  context_rvds.o(.text)
    rt_hw_context_switch                     0x08000103   Thumb Code    28  context_rvds.o(.text)
    rt_hw_context_switch_interrupt           0x08000103   Thumb Code     0  context_rvds.o(.text)
    PendSV_Handler                           0x0800011f   Thumb Code    84  context_rvds.o(.text)
    rt_hw_context_switch_to                  0x08000173   Thumb Code    44  context_rvds.o(.text)
    rt_hw_interrupt_thread_switch            0x0800019f   Thumb Code     2  context_rvds.o(.text)
    HardFault_Handler                        0x080001a1   Thumb Code    12  context_rvds.o(.text)
    __aeabi_uidiv                            0x080001cd   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080001cd   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x080001f9   Thumb Code    96  uldiv.o(.text)
    __aeabi_lmul                             0x08000259   Thumb Code   122  llmul.o(.text)
    _ll_mul                                  0x08000259   Thumb Code     0  llmul.o(.text)
    __aeabi_memcpy                           0x080002d3   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x080002d3   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x080002d3   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x080002f7   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x080002f7   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x080002f7   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000305   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000305   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000305   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000309   Thumb Code    18  memseta.o(.text)
    strstr                                   0x0800031b   Thumb Code    40  strstr.o(.text)
    strncpy                                  0x08000343   Thumb Code    26  strncpy.o(.text)
    strchr                                   0x0800035d   Thumb Code    20  strchr.o(.text)
    strlen                                   0x08000371   Thumb Code    14  strlen.o(.text)
    memcmp                                   0x0800037f   Thumb Code    26  memcmp.o(.text)
    strcpy                                   0x08000399   Thumb Code    18  strcpy.o(.text)
    atoi                                     0x080003ab   Thumb Code    26  atoi.o(.text)
    __aeabi_fmul                             0x080003c5   Thumb Code   122  fmul.o(.text)
    __aeabi_fdiv                             0x0800043f   Thumb Code   124  fdiv.o(.text)
    __aeabi_dmul                             0x080004bd   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x0800058d   Thumb Code   234  ddiv.o(.text)
    __aeabi_ui2f                             0x0800067d   Thumb Code    14  ffltui.o(.text)
    __aeabi_f2d                              0x0800068b   Thumb Code    40  f2d.o(.text)
    __aeabi_llsl                             0x080006b3   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x080006b3   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080006d3   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x080006d3   Thumb Code     0  llushr.o(.text)
    strtol                                   0x080006f5   Thumb Code   108  strtol.o(.text)
    __I$use$fp                               0x08000765   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000765   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x08000775   Thumb Code   114  fepilogue.o(.text)
    _double_round                            0x080007e7   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x08000801   Thumb Code   164  depilogue.o(.text)
    __aeabi_dadd                             0x080008a5   Thumb Code   330  dadd.o(.text)
    __aeabi_dsub                             0x080009ef   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x080009fb   Thumb Code    12  dadd.o(.text)
    __aeabi_d2ulz                            0x08000a0d   Thumb Code    54  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000a4d   Thumb Code    38  cdrcmple.o(.text)
    __scatterload                            0x08000a75   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000a75   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x08000a99   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x08000a99   Thumb Code     0  llsshr.o(.text)
    __rt_ctype_table                         0x08000ac1   Thumb Code     4  ctype_o.o(.text)
    _strtoul                                 0x08000ac9   Thumb Code   166  _strtoul.o(.text)
    _chval                                   0x08000b6f   Thumb Code    30  _chval.o(.text)
    __decompress                             0x08000b8d   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x08000b8d   Thumb Code    86  __dczerorl2.o(.text)
    main                                     0x08000be3   Thumb Code    10  components.o(i.$Sub$$main)
    ADC_Voltage_Init                         0x08000d51   Thumb Code   108  adc.o(i.ADC_Voltage_Init)
    EEPROM_Init                              0x08000dcd   Thumb Code    32  stm32_flash.o(i.EEPROM_Init)
    EEPROM_ReadBuffer                        0x08000df1   Thumb Code    40  stm32_flash.o(i.EEPROM_ReadBuffer)
    EEPROM_WriteBuffer                       0x08000e1d   Thumb Code    56  stm32_flash.o(i.EEPROM_WriteBuffer)
    EEPROM_WriteByte                         0x08000e55   Thumb Code    28  stm32_flash.o(i.EEPROM_WriteByte)
    FLASH_PageErase                          0x08000e75   Thumb Code    34  stm32l0xx_hal_flash_ex.o(i.FLASH_PageErase)
    FLASH_WaitForLastOperation               0x08000f31   Thumb Code   106  stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation)
    Get_Power_Voltage                        0x08000fa1   Thumb Code    80  adc.o(i.Get_Power_Voltage)
    HAL_ADC_ConfigChannel                    0x0800103d   Thumb Code   146  stm32l0xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_GetValue                         0x080010d9   Thumb Code     6  stm32l0xx_hal_adc.o(i.HAL_ADC_GetValue)
    HAL_ADC_Init                             0x080010e1   Thumb Code   452  stm32l0xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x080012ad   Thumb Code     2  stm32l0xx_hal_adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_PollForConversion                0x080012af   Thumb Code   192  stm32l0xx_hal_adc.o(i.HAL_ADC_PollForConversion)
    HAL_ADC_Start                            0x08001371   Thumb Code    90  stm32l0xx_hal_adc.o(i.HAL_ADC_Start)
    HAL_ADC_Stop                             0x080013d1   Thumb Code    62  stm32l0xx_hal_adc.o(i.HAL_ADC_Stop)
    HAL_Delay                                0x08001411   Thumb Code    32  stm32l0xx_hal.o(i.HAL_Delay)
    HAL_FLASHEx_DATAEEPROM_Program           0x08001435   Thumb Code    82  stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Program)
    HAL_FLASHEx_DATAEEPROM_Unlock            0x08001491   Thumb Code    40  stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Unlock)
    HAL_FLASHEx_Erase                        0x080014c5   Thumb Code   102  stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase)
    HAL_FLASH_Lock                           0x08001539   Thumb Code    22  stm32l0xx_hal_flash.o(i.HAL_FLASH_Lock)
    HAL_FLASH_Program                        0x08001555   Thumb Code    50  stm32l0xx_hal_flash.o(i.HAL_FLASH_Program)
    HAL_FLASH_Unlock                         0x08001591   Thumb Code    74  stm32l0xx_hal_flash.o(i.HAL_FLASH_Unlock)
    HAL_GPIO_Init                            0x080015f1   Thumb Code   326  stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_TogglePin                       0x08001759   Thumb Code    16  stm32l0xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08001769   Thumb Code    12  stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001775   Thumb Code     6  stm32l0xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08001781   Thumb Code    12  stm32l0xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08001791   Thumb Code    44  stm32l0xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080017c1   Thumb Code    62  stm32l0xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001809   Thumb Code     2  stm32l0xx_hal.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x0800180d   Thumb Code    18  stm32l0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001825   Thumb Code     8  stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_PWR_EnableBkUpAccess                 0x0800182d   Thumb Code    12  stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess)
    HAL_RCCEx_PeriphCLKConfig                0x0800183d   Thumb Code   404  stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x080019dd   Thumb Code   428  stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08001ba1   Thumb Code     6  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08001bad   Thumb Code    22  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001bcd   Thumb Code    22  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08001bed   Thumb Code   136  stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001c81   Thumb Code  1286  stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_RTCEx_BKUPRead                       0x08002187   Thumb Code    12  stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPRead)
    HAL_RTCEx_BKUPWrite                      0x08002193   Thumb Code    12  stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPWrite)
    HAL_RTC_GetDate                          0x080021a1   Thumb Code    64  stm32l0xx_hal_rtc.o(i.HAL_RTC_GetDate)
    HAL_RTC_GetTime                          0x080021e5   Thumb Code    84  stm32l0xx_hal_rtc.o(i.HAL_RTC_GetTime)
    HAL_RTC_Init                             0x0800223d   Thumb Code   198  stm32l0xx_hal_rtc.o(i.HAL_RTC_Init)
    HAL_RTC_MspInit                          0x08002309   Thumb Code     2  stm32l0xx_hal_rtc.o(i.HAL_RTC_MspInit)
    HAL_RTC_SetDate                          0x0800230d   Thumb Code   214  stm32l0xx_hal_rtc.o(i.HAL_RTC_SetDate)
    HAL_RTC_SetTime                          0x080023e9   Thumb Code   246  stm32l0xx_hal_rtc.o(i.HAL_RTC_SetTime)
    HAL_RTC_WaitForSynchro                   0x080024e5   Thumb Code    52  stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro)
    HAL_SYSTICK_Config                       0x08002519   Thumb Code    38  stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_UART_Init                            0x08002549   Thumb Code   112  stm32l0xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x080025b9   Thumb Code     2  stm32l0xx_hal_uart.o(i.HAL_UART_MspInit)
    HAL_UART_Transmit                        0x080025bb   Thumb Code   232  stm32l0xx_hal_uart.o(i.HAL_UART_Transmit)
    RTC_Bcd2ToByte                           0x080026e9   Thumb Code    16  stm32l0xx_hal_rtc.o(i.RTC_Bcd2ToByte)
    RTC_ByteToBcd2                           0x080026f9   Thumb Code    22  stm32l0xx_hal_rtc.o(i.RTC_ByteToBcd2)
    RTC_EnterInitMode                        0x0800270f   Thumb Code    56  stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode)
    SysTick_Handler                          0x08002747   Thumb Code    24  board.o(i.SysTick_Handler)
    SystemCoreClockUpdate                    0x08002761   Thumb Code   164  system_stm32l0xx.o(i.SystemCoreClockUpdate)
    SystemInit                               0x0800281d   Thumb Code    10  system_stm32l0xx.o(i.SystemInit)
    UART_AdvFeatureConfig                    0x0800282d   Thumb Code   202  stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x080028f9   Thumb Code    98  stm32l0xx_hal_uart.o(i.UART_CheckIdleState)
    UART_SetConfig                           0x08002961   Thumb Code   576  stm32l0xx_hal_uart.o(i.UART_SetConfig)
    UART_WaitOnFlagUntilTimeout              0x08002bc9   Thumb Code   164  stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    USART1_IRQHandler                        0x08002c6d   Thumb Code    60  net.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08002cb5   Thumb Code    58  uart.o(i.USART2_IRQHandler)
    __0printf                                0x08002cf5   Thumb Code    24  printfa.o(i.__0printf)
    __1printf                                0x08002cf5   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08002cf5   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08002cf5   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08002cf5   Thumb Code     0  printfa.o(i.__0printf)
    __0snprintf                              0x08002d15   Thumb Code    50  printfa.o(i.__0snprintf)
    __1snprintf                              0x08002d15   Thumb Code     0  printfa.o(i.__0snprintf)
    __2snprintf                              0x08002d15   Thumb Code     0  printfa.o(i.__0snprintf)
    __c89snprintf                            0x08002d15   Thumb Code     0  printfa.o(i.__0snprintf)
    snprintf                                 0x08002d15   Thumb Code     0  printfa.o(i.__0snprintf)
    __0sprintf                               0x08002d4d   Thumb Code    36  printfa.o(i.__0sprintf)
    __1sprintf                               0x08002d4d   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08002d4d   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08002d4d   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08002d4d   Thumb Code     0  printfa.o(i.__0sprintf)
    __ARM_clz                                0x08002d75   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __ARM_common_ll_muluu                    0x08002da3   Thumb Code    48  stm32l0xx_hal_rcc.o(i.__ARM_common_ll_muluu)
    __ARM_common_switch8                     0x08002dd3   Thumb Code    26  pc_protocol.o(i.__ARM_common_switch8)
    __aeabi_errno_addr                       0x08002e31   Thumb Code     4  errno.o(i.__aeabi_errno_addr)
    __rt_errno_addr                          0x08002e31   Thumb Code     0  errno.o(i.__aeabi_errno_addr)
    __rt_ffs                                 0x08002e39   Thumb Code    64  kservice.o(i.__rt_ffs)
    __scatterload_copy                       0x08002e7d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08002e8b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08002e8d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    auto_ctrl                                0x08003cd5   Thumb Code     2  auto_ctrl_task.o(i.auto_ctrl)
    bsp_board_init                           0x08003ce1   Thumb Code    54  bsp_board.o(i.bsp_board_init)
    config_refresh                           0x08003da1   Thumb Code    26  user_config.o(i.config_refresh)
    debug_init                               0x08003edd   Thumb Code   102  debug.o(i.debug_init)
    delay_us                                 0x08003fa5   Thumb Code    50  board.o(i.delay_us)
    display_battery_voltage                  0x0800400d   Thumb Code    40  lcd12864.o(i.display_battery_voltage)
    display_flow_rate                        0x0800403d   Thumb Code   118  lcd12864.o(i.display_flow_rate)
    display_rotate                           0x080041fd   Thumb Code    46  lcd12864.o(i.display_rotate)
    ec600m_base_function_register            0x08004235   Thumb Code     8  ec600m_common.o(i.ec600m_base_function_register)
    ec600m_tcpip_register                    0x08004241   Thumb Code     8  ec600m_tcpip.o(i.ec600m_tcpip_register)
    flow_meter_protocol_parsing              0x0800424d   Thumb Code     2  flow_meter_protocol.o(i.flow_meter_protocol_parsing)
    fputc                                    0x08004251   Thumb Code    18  debug.o(i.fputc)
    free                                     0x08004269   Thumb Code    80  malloc.o(i.free)
    get_font_info                            0x080042bd   Thumb Code   198  lcd_font.o(i.get_font_info)
    get_font_size                            0x08004399   Thumb Code    44  lcd_font.o(i.get_font_size)
    get_font_type                            0x080043c5   Thumb Code    16  lcd_font.o(i.get_font_type)
    get_water_value                          0x080045bd   Thumb Code     8  daosheng_protocol.o(i.get_water_value)
    lcd_clear                                0x08004609   Thumb Code    42  lcd12864.o(i.lcd_clear)
    lcd_init                                 0x080046d1   Thumb Code   170  lcd12864.o(i.lcd_init)
    lcd_refresh                              0x08004785   Thumb Code    64  lcd12864.o(i.lcd_refresh)
    lcd_show_string                          0x08004849   Thumb Code   168  lcd12864.o(i.lcd_show_string)
    $Super$$main                             0x080049a1   Thumb Code   254  main.o(i.main)
    main_thread_entry                        0x08004add   Thumb Code     8  components.o(i.main_thread_entry)
    malloc                                   0x08004ae5   Thumb Code    92  malloc.o(i.malloc)
    mbus_crc16                               0x08004b51   Thumb Code    48  daosheng_protocol.o(i.mbus_crc16)
    modbus_crc16                             0x08004b89   Thumb Code    48  modbus_rtu.o(i.modbus_crc16)
    net_init                                 0x08004c65   Thumb Code    58  net.o(i.net_init)
    net_module_reboot                        0x08004d01   Thumb Code   118  net.o(i.net_module_reboot)
    net_protocol_parsing                     0x08004d85   Thumb Code   152  net_protocol.o(i.net_protocol_parsing)
    pc_protocol_parsing                      0x0800513d   Thumb Code   554  pc_protocol.o(i.pc_protocol_parsing)
    protocol_parsing                         0x080055a5   Thumb Code   200  protocol_common.o(i.protocol_parsing)
    read_config                              0x08005681   Thumb Code   102  user_config.o(i.read_config)
    rt_application_init                      0x08005719   Thumb Code    48  components.o(i.rt_application_init)
    rt_assert_handler                        0x08005769   Thumb Code    32  kservice.o(i.rt_assert_handler)
    rt_enter_critical                        0x0800578d   Thumb Code    20  scheduler.o(i.rt_enter_critical)
    rt_exit_critical                         0x080057a5   Thumb Code    46  scheduler.o(i.rt_exit_critical)
    rt_free                                  0x080057d9   Thumb Code   196  mem.o(i.rt_free)
    rt_heap_begin_get                        0x0800590d   Thumb Code     4  board.o(i.rt_heap_begin_get)
    rt_heap_end_get                          0x08005915   Thumb Code     4  board.o(i.rt_heap_end_get)
    rt_hw_board_init                         0x0800591d   Thumb Code    48  board.o(i.rt_hw_board_init)
    rt_hw_hard_fault_exception               0x0800594d   Thumb Code     2  cpuport.o(i.rt_hw_hard_fault_exception)
    rt_hw_stack_init                         0x08005951   Thumb Code    56  cpuport.o(i.rt_hw_stack_init)
    rt_interrupt_enter                       0x0800598d   Thumb Code    32  irq.o(i.rt_interrupt_enter)
    rt_interrupt_get_nest                    0x080059b1   Thumb Code    18  irq.o(i.rt_interrupt_get_nest)
    rt_interrupt_leave                       0x080059c9   Thumb Code    32  irq.o(i.rt_interrupt_leave)
    rt_malloc                                0x08005acd   Thumb Code   370  mem.o(i.rt_malloc)
    rt_memset                                0x08005cb1   Thumb Code    70  kservice.o(i.rt_memset)
    rt_object_allocate                       0x08005cf9   Thumb Code   140  object.o(i.rt_object_allocate)
    rt_object_delete                         0x08005da9   Thumb Code    82  object.o(i.rt_object_delete)
    rt_object_detach                         0x08005e45   Thumb Code    58  object.o(i.rt_object_detach)
    rt_object_get_information                0x08005e9d   Thumb Code    28  object.o(i.rt_object_get_information)
    rt_object_get_type                       0x08005ebd   Thumb Code    28  object.o(i.rt_object_get_type)
    rt_object_init                           0x08005ef1   Thumb Code   124  object.o(i.rt_object_init)
    rt_object_is_systemobject                0x08005f9d   Thumb Code    34  object.o(i.rt_object_is_systemobject)
    rt_schedule                              0x08005fd9   Thumb Code   208  scheduler.o(i.rt_schedule)
    rt_schedule_insert_thread                0x080060b1   Thumb Code   100  scheduler.o(i.rt_schedule_insert_thread)
    rt_schedule_remove_thread                0x08006135   Thumb Code    88  scheduler.o(i.rt_schedule_remove_thread)
    rt_sem_init                              0x080061b1   Thumb Code    68  ipc.o(i.rt_sem_init)
    rt_sem_release                           0x0800621d   Thumb Code   112  ipc.o(i.rt_sem_release)
    rt_sem_take                              0x080062a9   Thumb Code   244  ipc.o(i.rt_sem_take)
    rt_show_version                          0x080063c1   Thumb Code     2  kservice.o(i.rt_show_version)
    rt_sprintf                               0x080063c3   Thumb Code    20  kservice.o(i.rt_sprintf)
    rt_strlen                                0x080063d7   Thumb Code    16  kservice.o(i.rt_strlen)
    rt_strncpy                               0x080063e7   Thumb Code    40  kservice.o(i.rt_strncpy)
    rt_system_heap_init                      0x08006411   Thumb Code   112  mem.o(i.rt_system_heap_init)
    rt_system_scheduler_init                 0x08006499   Thumb Code    38  scheduler.o(i.rt_system_scheduler_init)
    rt_system_scheduler_start                0x080064c9   Thumb Code    36  scheduler.o(i.rt_system_scheduler_start)
    rt_system_timer_init                     0x080064f1   Thumb Code    20  timer.o(i.rt_system_timer_init)
    rt_system_timer_thread_init              0x08006509   Thumb Code     2  timer.o(i.rt_system_timer_thread_init)
    rt_thread_create                         0x0800650b   Thumb Code    68  thread.o(i.rt_thread_create)
    rt_thread_exit                           0x08006551   Thumb Code    80  thread.o(i.rt_thread_exit)
    rt_thread_idle_excute                    0x080065c5   Thumb Code   154  idle.o(i.rt_thread_idle_excute)
    rt_thread_idle_init                      0x0800666d   Thumb Code    70  idle.o(i.rt_thread_idle_init)
    rt_thread_init                           0x080066c5   Thumb Code    70  thread.o(i.rt_thread_init)
    rt_thread_resume                         0x0800673d   Thumb Code   108  thread.o(i.rt_thread_resume)
    rt_thread_self                           0x080067cd   Thumb Code     6  thread.o(i.rt_thread_self)
    rt_thread_startup                        0x080067d9   Thumb Code   100  thread.o(i.rt_thread_startup)
    rt_thread_suspend                        0x08006891   Thumb Code   140  thread.o(i.rt_thread_suspend)
    rt_thread_timeout                        0x0800695d   Thumb Code    90  thread.o(i.rt_thread_timeout)
    rt_thread_yield                          0x08006a15   Thumb Code    10  thread.o(i.rt_thread_yield)
    rt_tick_get                              0x08006a21   Thumb Code     6  clock.o(i.rt_tick_get)
    rt_tick_increase                         0x08006a2d   Thumb Code    48  clock.o(i.rt_tick_increase)
    rt_timer_check                           0x08006a61   Thumb Code   124  timer.o(i.rt_timer_check)
    rt_timer_control                         0x08006ae5   Thumb Code    98  timer.o(i.rt_timer_control)
    rt_timer_detach                          0x08006b9d   Thumb Code    86  timer.o(i.rt_timer_detach)
    rt_timer_init                            0x08006c75   Thumb Code    54  timer.o(i.rt_timer_init)
    rt_timer_start                           0x08006cc5   Thumb Code   262  timer.o(i.rt_timer_start)
    rt_timer_stop                            0x08006e51   Thumb Code    96  timer.o(i.rt_timer_stop)
    rt_vsnprintf                             0x08006f09   Thumb Code   658  kservice.o(i.rt_vsnprintf)
    rt_vsprintf                              0x080071a5   Thumb Code    16  kservice.o(i.rt_vsprintf)
    rtc_get_calendar                         0x080071b5   Thumb Code   110  rtc.o(i.rtc_get_calendar)
    rtc_init                                 0x08007229   Thumb Code   164  rtc.o(i.rtc_init)
    rtc_set_time                             0x080072e1   Thumb Code    60  rtc.o(i.rtc_set_time)
    rtthread_startup                         0x08007321   Thumb Code    42  components.o(i.rtthread_startup)
    serial_485_init                          0x080074a9   Thumb Code   324  uart.o(i.serial_485_init)
    serial_485_send_dat                      0x080075fd   Thumb Code    54  uart.o(i.serial_485_send_dat)
    stm32_flash_read                         0x080076e7   Thumb Code    26  stm32_flash.o(i.stm32_flash_read)
    stm32_flash_read_byte                    0x08007701   Thumb Code     4  stm32_flash.o(i.stm32_flash_read_byte)
    stm32_flash_write                        0x08007705   Thumb Code   264  stm32_flash.o(i.stm32_flash_write)
    system_clock_init                        0x08007959   Thumb Code   116  system.o(i.system_clock_init)
    water_meter_protocol_parsing             0x080079d5   Thumb Code     2  water_meter_protocol.o(i.water_meter_protocol_parsing)
    query_commands                           0x08007a34   Data          14  pc_protocol.o(.constdata)
    AHBPrescTable                            0x0800966e   Data          16  system_stm32l0xx.o(.constdata)
    PLLMulTable                              0x0800967e   Data           9  system_stm32l0xx.o(.constdata)
    APBPrescTable                            0x08009687   Data           8  system_stm32l0xx.o(.constdata)
    __lowest_bit_bitmap                      0x080098b5   Data         256  kservice.o(.constdata)
    __ctype_table                            0x08009c0c   Data         129  ctype_o.o(.constdata)
    Region$$Table$$Base                      0x08009e0c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08009e2c   Number         0  anon$$obj.o(Region$$Table)
    net_initialized                          0x20000000   Data           1  main.o(.data)
    vdda_voltage                             0x20000004   Data           4  main.o(.data)
    net_task_timer                           0x20000008   Data           4  main.o(.data)
    auto_ctrl_timer                          0x2000000c   Data           4  main.o(.data)
    main_task_timer                          0x20000010   Data           4  main.o(.data)
    calendar                                 0x20000014   Data           8  main.o(.data)
    CR_time                                  0x2000001c   Data           4  protocol_common.o(.data)
    netFlow                                  0x2000002c   Data           4  daosheng_protocol.o(.data)
    posFlow                                  0x20000030   Data           4  daosheng_protocol.o(.data)
    negFlow                                  0x20000034   Data           4  daosheng_protocol.o(.data)
    instFlow                                 0x20000038   Data           4  daosheng_protocol.o(.data)
    velocity                                 0x2000003c   Data           4  daosheng_protocol.o(.data)
    display_state                            0x20000044   Data           1  lcd12864.o(.data)
    str2                                     0x2000004c   Data           6  lcd12864.o(.data)
    str3                                     0x20000052   Data           6  lcd12864.o(.data)
    serial_port                              0x20000058   Data         532  net.o(.data)
    ec600x_base_functions                    0x2000026c   Data          24  ec600m_common.o(.data)
    SystemCoreClock                          0x20000294   Data           4  system_stm32l0xx.o(.data)
    uwTickFreq                               0x20000298   Data           1  stm32l0xx_hal.o(.data)
    uwTickPrio                               0x2000029c   Data           4  stm32l0xx_hal.o(.data)
    uwTick                                   0x200002a0   Data           4  stm32l0xx_hal.o(.data)
    rt_interrupt_nest                        0x200002a8   Data           1  irq.o(.data)
    rt_assert_hook                           0x200002b8   Data           4  kservice.o(.data)
    rt_object_trytake_hook                   0x200002e4   Data           4  object.o(.data)
    rt_object_take_hook                      0x200002e8   Data           4  object.o(.data)
    rt_object_put_hook                       0x200002ec   Data           4  object.o(.data)
    rt_current_priority                      0x20000370   Data           1  scheduler.o(.data)
    rt_thread_ready_priority_group           0x20000374   Data           4  scheduler.o(.data)
    rt_current_thread                        0x20000378   Data           4  scheduler.o(.data)
    rt_thread_defunct                        0x20000380   Data           8  scheduler.o(.data)
    rt_interrupt_from_thread                 0x200003a8   Data           4  cpuport.o(.data)
    rt_interrupt_to_thread                   0x200003ac   Data           4  cpuport.o(.data)
    rt_thread_switch_interrupt_flag          0x200003b0   Data           4  cpuport.o(.data)
    __stdout                                 0x200003b4   Data           4  stdout.o(.data)
    __microlib_freelist                      0x200003b8   Data           4  mvars.o(.data)
    __microlib_freelist_initialised          0x200003bc   Data           4  mvars.o(.data)
    net_info                                 0x200003c4   Data          12  main.o(.bss)
    config                                   0x200003d0   Data          60  user_config.o(.bss)
    TxModbusbuf                              0x2000044e   Data          16  daosheng_protocol.o(.bss)
    Txbuf                                    0x2000045e   Data          30  pc_protocol.o(.bss)
    net_handler                              0x200008fc   Data         132  net.o(.bss)
    serial_485                               0x20000980   Data          66  uart.o(.bss)
    serial_485_handler                       0x200009c4   Data         132  uart.o(.bss)
    debug_handler                            0x20000a48   Data         132  debug.o(.bss)
    pwr_mon                                  0x20000b4c   Data          12  adc.o(.bss)
    hadc                                     0x20000b58   Data          92  adc.o(.bss)
    rtc_handle                               0x20000bb4   Data          36  rtc.o(.bss)
    pFlash                                   0x20000bd8   Data          24  stm32l0xx_hal_flash.o(.bss)
    rt_thread_priority_table                 0x200045a0   Data         256  scheduler.o(.bss)
    __heap_base                              0x200046a0   Data           0  startup_stm32l072xx.o(HEAP)
    __heap_limit                             0x200046a0   Data           0  startup_stm32l072xx.o(HEAP)
    __initial_sp                             0x200049a0   Data           0  startup_stm32l072xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000c1

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000a1f0, Max: 0x0001c000, ABSOLUTE, COMPRESSED[0x00009ec4])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00009e2c, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000c0   Data   RO         1428    RESET               startup_stm32l072xx.o
    0x080000c0   0x080000c0   0x00000000   Code   RO         5337  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x080000c0   0x080000c0   0x00000004   Code   RO         5702    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x080000c4   0x080000c4   0x00000004   Code   RO         5705    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x080000c8   0x080000c8   0x00000000   Code   RO         5707    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x080000c8   0x080000c8   0x00000000   Code   RO         5709    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x080000c8   0x080000c8   0x00000008   Code   RO         5710    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x080000d0   0x080000d0   0x00000004   Code   RO         5717    .ARM.Collect$$$$0000000E  mc_p.l(entry12b.o)
    0x080000d4   0x080000d4   0x00000000   Code   RO         5712    .ARM.Collect$$$$0000000F  mc_p.l(entry10a.o)
    0x080000d4   0x080000d4   0x00000000   Code   RO         5714    .ARM.Collect$$$$00000011  mc_p.l(entry11a.o)
    0x080000d4   0x080000d4   0x00000004   Code   RO         5703    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x080000d8   0x080000d8   0x0000001c   Code   RO         1429    .text               startup_stm32l072xx.o
    0x080000f4   0x080000f4   0x000000d8   Code   RO         5323    .text               context_rvds.o
    0x080001cc   0x080001cc   0x0000002c   Code   RO         5340    .text               mc_p.l(uidiv.o)
    0x080001f8   0x080001f8   0x00000060   Code   RO         5344    .text               mc_p.l(uldiv.o)
    0x08000258   0x08000258   0x0000007a   Code   RO         5346    .text               mc_p.l(llmul.o)
    0x080002d2   0x080002d2   0x00000024   Code   RO         5348    .text               mc_p.l(memcpya.o)
    0x080002f6   0x080002f6   0x00000024   Code   RO         5350    .text               mc_p.l(memseta.o)
    0x0800031a   0x0800031a   0x00000028   Code   RO         5352    .text               mc_p.l(strstr.o)
    0x08000342   0x08000342   0x0000001a   Code   RO         5354    .text               mc_p.l(strncpy.o)
    0x0800035c   0x0800035c   0x00000014   Code   RO         5356    .text               mc_p.l(strchr.o)
    0x08000370   0x08000370   0x0000000e   Code   RO         5358    .text               mc_p.l(strlen.o)
    0x0800037e   0x0800037e   0x0000001a   Code   RO         5360    .text               mc_p.l(memcmp.o)
    0x08000398   0x08000398   0x00000012   Code   RO         5362    .text               mc_p.l(strcpy.o)
    0x080003aa   0x080003aa   0x0000001a   Code   RO         5655    .text               mc_p.l(atoi.o)
    0x080003c4   0x080003c4   0x0000007a   Code   RO         5657    .text               mf_p.l(fmul.o)
    0x0800043e   0x0800043e   0x0000007c   Code   RO         5659    .text               mf_p.l(fdiv.o)
    0x080004ba   0x080004ba   0x00000002   PAD
    0x080004bc   0x080004bc   0x000000d0   Code   RO         5661    .text               mf_p.l(dmul.o)
    0x0800058c   0x0800058c   0x000000f0   Code   RO         5663    .text               mf_p.l(ddiv.o)
    0x0800067c   0x0800067c   0x0000000e   Code   RO         5665    .text               mf_p.l(ffltui.o)
    0x0800068a   0x0800068a   0x00000028   Code   RO         5673    .text               mf_p.l(f2d.o)
    0x080006b2   0x080006b2   0x00000020   Code   RO         5719    .text               mc_p.l(llshl.o)
    0x080006d2   0x080006d2   0x00000022   Code   RO         5721    .text               mc_p.l(llushr.o)
    0x080006f4   0x080006f4   0x00000070   Code   RO         5732    .text               mc_p.l(strtol.o)
    0x08000764   0x08000764   0x00000000   Code   RO         5734    .text               mc_p.l(iusefp.o)
    0x08000764   0x08000764   0x00000082   Code   RO         5735    .text               mf_p.l(fepilogue.o)
    0x080007e6   0x080007e6   0x000000be   Code   RO         5737    .text               mf_p.l(depilogue.o)
    0x080008a4   0x080008a4   0x00000168   Code   RO         5741    .text               mf_p.l(dadd.o)
    0x08000a0c   0x08000a0c   0x00000040   Code   RO         5745    .text               mf_p.l(dfixul.o)
    0x08000a4c   0x08000a4c   0x00000028   Code   RO         5747    .text               mf_p.l(cdrcmple.o)
    0x08000a74   0x08000a74   0x00000024   Code   RO         5749    .text               mc_p.l(init.o)
    0x08000a98   0x08000a98   0x00000026   Code   RO         5751    .text               mc_p.l(llsshr.o)
    0x08000abe   0x08000abe   0x00000002   PAD
    0x08000ac0   0x08000ac0   0x00000008   Code   RO         5753    .text               mc_p.l(ctype_o.o)
    0x08000ac8   0x08000ac8   0x000000a6   Code   RO         5781    .text               mc_p.l(_strtoul.o)
    0x08000b6e   0x08000b6e   0x0000001e   Code   RO         5787    .text               mc_p.l(_chval.o)
    0x08000b8c   0x08000b8c   0x00000056   Code   RO         5797    .text               mc_p.l(__dczerorl2.o)
    0x08000be2   0x08000be2   0x0000000a   Code   RO         4107    i.$Sub$$main        components.o
    0x08000bec   0x08000bec   0x00000052   Code   RO         1661    i.ADC_ConversionStop  stm32l0xx_hal_adc.o
    0x08000c3e   0x08000c3e   0x00000002   PAD
    0x08000c40   0x08000c40   0x0000002c   Code   RO         1665    i.ADC_DelayMicroSecond  stm32l0xx_hal_adc.o
    0x08000c6c   0x08000c6c   0x00000070   Code   RO         1666    i.ADC_Disable       stm32l0xx_hal_adc.o
    0x08000cdc   0x08000cdc   0x00000074   Code   RO         1667    i.ADC_Enable        stm32l0xx_hal_adc.o
    0x08000d50   0x08000d50   0x0000007c   Code   RO         1300    i.ADC_Voltage_Init  adc.o
    0x08000dcc   0x08000dcc   0x00000024   Code   RO         1203    i.EEPROM_Init       stm32_flash.o
    0x08000df0   0x08000df0   0x0000002c   Code   RO         1204    i.EEPROM_ReadBuffer  stm32_flash.o
    0x08000e1c   0x08000e1c   0x00000038   Code   RO         1206    i.EEPROM_WriteBuffer  stm32_flash.o
    0x08000e54   0x08000e54   0x00000020   Code   RO         1207    i.EEPROM_WriteByte  stm32_flash.o
    0x08000e74   0x08000e74   0x0000002c   Code   RO         3809    i.FLASH_PageErase   stm32l0xx_hal_flash_ex.o
    0x08000ea0   0x08000ea0   0x00000090   Code   RO         3700    i.FLASH_SetErrorCode  stm32l0xx_hal_flash.o
    0x08000f30   0x08000f30   0x00000070   Code   RO         3701    i.FLASH_WaitForLastOperation  stm32l0xx_hal_flash.o
    0x08000fa0   0x08000fa0   0x0000009c   Code   RO         1301    i.Get_Power_Voltage  adc.o
    0x0800103c   0x0800103c   0x0000009c   Code   RO         1669    i.HAL_ADC_ConfigChannel  stm32l0xx_hal_adc.o
    0x080010d8   0x080010d8   0x00000006   Code   RO         1676    i.HAL_ADC_GetValue  stm32l0xx_hal_adc.o
    0x080010de   0x080010de   0x00000002   PAD
    0x080010e0   0x080010e0   0x000001cc   Code   RO         1678    i.HAL_ADC_Init      stm32l0xx_hal_adc.o
    0x080012ac   0x080012ac   0x00000002   Code   RO         1681    i.HAL_ADC_MspInit   stm32l0xx_hal_adc.o
    0x080012ae   0x080012ae   0x000000c0   Code   RO         1682    i.HAL_ADC_PollForConversion  stm32l0xx_hal_adc.o
    0x0800136e   0x0800136e   0x00000002   PAD
    0x08001370   0x08001370   0x00000060   Code   RO         1684    i.HAL_ADC_Start     stm32l0xx_hal_adc.o
    0x080013d0   0x080013d0   0x0000003e   Code   RO         1687    i.HAL_ADC_Stop      stm32l0xx_hal_adc.o
    0x0800140e   0x0800140e   0x00000002   PAD
    0x08001410   0x08001410   0x00000024   Code   RO         1464    i.HAL_Delay         stm32l0xx_hal.o
    0x08001434   0x08001434   0x0000005c   Code   RO         3816    i.HAL_FLASHEx_DATAEEPROM_Program  stm32l0xx_hal_flash_ex.o
    0x08001490   0x08001490   0x00000034   Code   RO         3817    i.HAL_FLASHEx_DATAEEPROM_Unlock  stm32l0xx_hal_flash_ex.o
    0x080014c4   0x080014c4   0x00000074   Code   RO         3818    i.HAL_FLASHEx_Erase  stm32l0xx_hal_flash_ex.o
    0x08001538   0x08001538   0x0000001c   Code   RO         3705    i.HAL_FLASH_Lock    stm32l0xx_hal_flash.o
    0x08001554   0x08001554   0x0000003c   Code   RO         3710    i.HAL_FLASH_Program  stm32l0xx_hal_flash.o
    0x08001590   0x08001590   0x00000060   Code   RO         3712    i.HAL_FLASH_Unlock  stm32l0xx_hal_flash.o
    0x080015f0   0x080015f0   0x00000168   Code   RO         2118    i.HAL_GPIO_Init     stm32l0xx_hal_gpio.o
    0x08001758   0x08001758   0x00000010   Code   RO         2121    i.HAL_GPIO_TogglePin  stm32l0xx_hal_gpio.o
    0x08001768   0x08001768   0x0000000c   Code   RO         2122    i.HAL_GPIO_WritePin  stm32l0xx_hal_gpio.o
    0x08001774   0x08001774   0x0000000c   Code   RO         1468    i.HAL_GetTick       stm32l0xx_hal.o
    0x08001780   0x08001780   0x00000010   Code   RO         1474    i.HAL_IncTick       stm32l0xx_hal.o
    0x08001790   0x08001790   0x00000030   Code   RO         1475    i.HAL_Init          stm32l0xx_hal.o
    0x080017c0   0x080017c0   0x00000048   Code   RO         1476    i.HAL_InitTick      stm32l0xx_hal.o
    0x08001808   0x08001808   0x00000002   Code   RO         1478    i.HAL_MspInit       stm32l0xx_hal.o
    0x0800180a   0x0800180a   0x00000002   PAD
    0x0800180c   0x0800180c   0x00000018   Code   RO         1903    i.HAL_NVIC_EnableIRQ  stm32l0xx_hal_cortex.o
    0x08001824   0x08001824   0x00000008   Code   RO         1907    i.HAL_NVIC_SetPriority  stm32l0xx_hal_cortex.o
    0x0800182c   0x0800182c   0x00000010   Code   RO         2184    i.HAL_PWR_EnableBkUpAccess  stm32l0xx_hal_pwr.o
    0x0800183c   0x0800183c   0x000001a0   Code   RO         2461    i.HAL_RCCEx_PeriphCLKConfig  stm32l0xx_hal_rcc_ex.o
    0x080019dc   0x080019dc   0x000001c4   Code   RO         2350    i.HAL_RCC_ClockConfig  stm32l0xx_hal_rcc.o
    0x08001ba0   0x08001ba0   0x0000000c   Code   RO         2354    i.HAL_RCC_GetHCLKFreq  stm32l0xx_hal_rcc.o
    0x08001bac   0x08001bac   0x00000020   Code   RO         2356    i.HAL_RCC_GetPCLK1Freq  stm32l0xx_hal_rcc.o
    0x08001bcc   0x08001bcc   0x00000020   Code   RO         2357    i.HAL_RCC_GetPCLK2Freq  stm32l0xx_hal_rcc.o
    0x08001bec   0x08001bec   0x00000094   Code   RO         2358    i.HAL_RCC_GetSysClockFreq  stm32l0xx_hal_rcc.o
    0x08001c80   0x08001c80   0x00000506   Code   RO         2361    i.HAL_RCC_OscConfig  stm32l0xx_hal_rcc.o
    0x08002186   0x08002186   0x0000000c   Code   RO         2706    i.HAL_RTCEx_BKUPRead  stm32l0xx_hal_rtc_ex.o
    0x08002192   0x08002192   0x0000000c   Code   RO         2707    i.HAL_RTCEx_BKUPWrite  stm32l0xx_hal_rtc_ex.o
    0x0800219e   0x0800219e   0x00000002   PAD
    0x080021a0   0x080021a0   0x00000044   Code   RO         2576    i.HAL_RTC_GetDate   stm32l0xx_hal_rtc.o
    0x080021e4   0x080021e4   0x00000058   Code   RO         2578    i.HAL_RTC_GetTime   stm32l0xx_hal_rtc.o
    0x0800223c   0x0800223c   0x000000cc   Code   RO         2579    i.HAL_RTC_Init      stm32l0xx_hal_rtc.o
    0x08002308   0x08002308   0x00000002   Code   RO         2581    i.HAL_RTC_MspInit   stm32l0xx_hal_rtc.o
    0x0800230a   0x0800230a   0x00000002   PAD
    0x0800230c   0x0800230c   0x000000dc   Code   RO         2585    i.HAL_RTC_SetDate   stm32l0xx_hal_rtc.o
    0x080023e8   0x080023e8   0x000000fc   Code   RO         2586    i.HAL_RTC_SetTime   stm32l0xx_hal_rtc.o
    0x080024e4   0x080024e4   0x00000034   Code   RO         2587    i.HAL_RTC_WaitForSynchro  stm32l0xx_hal_rtc.o
    0x08002518   0x08002518   0x00000030   Code   RO         1911    i.HAL_SYSTICK_Config  stm32l0xx_hal_cortex.o
    0x08002548   0x08002548   0x00000070   Code   RO         2984    i.HAL_UART_Init     stm32l0xx_hal_uart.o
    0x080025b8   0x080025b8   0x00000002   Code   RO         2986    i.HAL_UART_MspInit  stm32l0xx_hal_uart.o
    0x080025ba   0x080025ba   0x000000e8   Code   RO         2993    i.HAL_UART_Transmit  stm32l0xx_hal_uart.o
    0x080026a2   0x080026a2   0x00000002   PAD
    0x080026a4   0x080026a4   0x00000044   Code   RO            3    i.MX_GPIO_Init      main.o
    0x080026e8   0x080026e8   0x00000010   Code   RO         2588    i.RTC_Bcd2ToByte    stm32l0xx_hal_rtc.o
    0x080026f8   0x080026f8   0x00000016   Code   RO         2589    i.RTC_ByteToBcd2    stm32l0xx_hal_rtc.o
    0x0800270e   0x0800270e   0x00000038   Code   RO         2590    i.RTC_EnterInitMode  stm32l0xx_hal_rtc.o
    0x08002746   0x08002746   0x00000018   Code   RO         3962    i.SysTick_Handler   board.o
    0x0800275e   0x0800275e   0x00000002   PAD
    0x08002760   0x08002760   0x000000bc   Code   RO         1394    i.SystemCoreClockUpdate  system_stm32l0xx.o
    0x0800281c   0x0800281c   0x00000010   Code   RO         1395    i.SystemInit        system_stm32l0xx.o
    0x0800282c   0x0800282c   0x000000ca   Code   RO         2998    i.UART_AdvFeatureConfig  stm32l0xx_hal_uart.o
    0x080028f6   0x080028f6   0x00000002   PAD
    0x080028f8   0x080028f8   0x00000068   Code   RO         2999    i.UART_CheckIdleState  stm32l0xx_hal_uart.o
    0x08002960   0x08002960   0x00000268   Code   RO         3015    i.UART_SetConfig    stm32l0xx_hal_uart.o
    0x08002bc8   0x08002bc8   0x000000a4   Code   RO         3020    i.UART_WaitOnFlagUntilTimeout  stm32l0xx_hal_uart.o
    0x08002c6c   0x08002c6c   0x00000048   Code   RO          938    i.USART1_IRQHandler  net.o
    0x08002cb4   0x08002cb4   0x00000040   Code   RO         1126    i.USART2_IRQHandler  uart.o
    0x08002cf4   0x08002cf4   0x00000020   Code   RO         5599    i.__0printf         mc_p.l(printfa.o)
    0x08002d14   0x08002d14   0x00000038   Code   RO         5600    i.__0snprintf       mc_p.l(printfa.o)
    0x08002d4c   0x08002d4c   0x00000028   Code   RO         5601    i.__0sprintf        mc_p.l(printfa.o)
    0x08002d74   0x08002d74   0x0000002e   Code   RO         5739    i.__ARM_clz         mf_p.l(depilogue.o)
    0x08002da2   0x08002da2   0x00000030   Code   RO         2436    i.__ARM_common_ll_muluu  stm32l0xx_hal_rcc.o
    0x08002dd2   0x08002dd2   0x0000001a   Code   RO          594    i.__ARM_common_switch8  pc_protocol.o
    0x08002dec   0x08002dec   0x00000044   Code   RO         1913    i.__NVIC_SetPriority  stm32l0xx_hal_cortex.o
    0x08002e30   0x08002e30   0x00000008   Code   RO         5725    i.__aeabi_errno_addr  mc_p.l(errno.o)
    0x08002e38   0x08002e38   0x00000044   Code   RO         4513    i.__rt_ffs          kservice.o
    0x08002e7c   0x08002e7c   0x0000000e   Code   RO         5791    i.__scatterload_copy  mc_p.l(handlers.o)
    0x08002e8a   0x08002e8a   0x00000002   Code   RO         5792    i.__scatterload_null  mc_p.l(handlers.o)
    0x08002e8c   0x08002e8c   0x0000000e   Code   RO         5793    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x08002e9a   0x08002e9a   0x00000002   PAD
    0x08002e9c   0x08002e9c   0x00000088   Code   RO          601    i._cmd_get_ip_sim_  net_protocol.o
    0x08002f24   0x08002f24   0x000000d4   Code   RO          602    i._cmd_get_param_   net_protocol.o
    0x08002ff8   0x08002ff8   0x00000018   Code   RO          603    i._cmd_get_time_    net_protocol.o
    0x08003010   0x08003010   0x0000002c   Code   RO          604    i._cmd_init_        net_protocol.o
    0x0800303c   0x0800303c   0x0000002c   Code   RO          605    i._cmd_set_address_  net_protocol.o
    0x08003068   0x08003068   0x00000094   Code   RO          606    i._cmd_set_ip_sim_  net_protocol.o
    0x080030fc   0x080030fc   0x00000114   Code   RO          607    i._cmd_set_param_   net_protocol.o
    0x08003210   0x08003210   0x00000038   Code   RO          608    i._cmd_set_time_    net_protocol.o
    0x08003248   0x08003248   0x00000038   Code   RO          609    i._cmd_timing_upload_  net_protocol.o
    0x08003280   0x08003280   0x00000174   Code   RO         5606    i._fp_digits        mc_p.l(printfa.o)
    0x080033f4   0x080033f4   0x00000024   Code   RO         4928    i._get_highest_priority_thread  scheduler.o
    0x08003418   0x08003418   0x00000014   Code   RO         4150    i._has_defunct_thread  idle.o
    0x0800342c   0x0800342c   0x000006f0   Code   RO         5607    i._printf_core      mc_p.l(printfa.o)
    0x08003b1c   0x08003b1c   0x00000020   Code   RO         5608    i._printf_post_padding  mc_p.l(printfa.o)
    0x08003b3c   0x08003b3c   0x0000002c   Code   RO         5609    i._printf_pre_padding  mc_p.l(printfa.o)
    0x08003b68   0x08003b68   0x0000004c   Code   RO         4929    i._rt_scheduler_stack_check  scheduler.o
    0x08003bb4   0x08003bb4   0x000000b8   Code   RO         5028    i._rt_thread_init   thread.o
    0x08003c6c   0x08003c6c   0x00000026   Code   RO         5177    i._rt_timer_init    timer.o
    0x08003c92   0x08003c92   0x00000022   Code   RO         5178    i._rt_timer_remove  timer.o
    0x08003cb4   0x08003cb4   0x00000016   Code   RO         5610    i._snputc           mc_p.l(printfa.o)
    0x08003cca   0x08003cca   0x0000000a   Code   RO         5611    i._sputc            mc_p.l(printfa.o)
    0x08003cd4   0x08003cd4   0x00000002   Code   RO          289    i.auto_ctrl         auto_ctrl_task.o
    0x08003cd6   0x08003cd6   0x00000008   Code   RO            4    i.auto_ctrl_task_process  main.o
    0x08003cde   0x08003cde   0x00000002   PAD
    0x08003ce0   0x08003ce0   0x00000040   Code   RO          723    i.bsp_board_init    bsp_board.o
    0x08003d20   0x08003d20   0x00000026   Code   RO          387    i.build_read_command  daosheng_protocol.o
    0x08003d46   0x08003d46   0x00000002   PAD
    0x08003d48   0x08003d48   0x00000058   Code   RO         1073    i.close             ec600m_tcpip.o
    0x08003da0   0x08003da0   0x00000024   Code   RO          309    i.config_refresh    user_config.o
    0x08003dc4   0x08003dc4   0x00000118   Code   RO         1074    i.connect           ec600m_tcpip.o
    0x08003edc   0x08003edc   0x00000074   Code   RO         1168    i.debug_init        debug.o
    0x08003f50   0x08003f50   0x0000003c   Code   RO          610    i.dec_to_hex        net_protocol.o
    0x08003f8c   0x08003f8c   0x00000018   Code   RO          611    i.dec_to_hex_to_byte  net_protocol.o
    0x08003fa4   0x08003fa4   0x00000038   Code   RO         3963    i.delay_us          board.o
    0x08003fdc   0x08003fdc   0x00000030   Code   RO         3964    i.dev_delay         board.o
    0x0800400c   0x0800400c   0x00000030   Code   RO          760    i.display_battery_voltage  lcd12864.o
    0x0800403c   0x0800403c   0x000000bc   Code   RO          761    i.display_flow_rate  lcd12864.o
    0x080040f8   0x080040f8   0x00000104   Code   RO          762    i.display_flow_rate1  lcd12864.o
    0x080041fc   0x080041fc   0x00000038   Code   RO          765    i.display_rotate    lcd12864.o
    0x08004234   0x08004234   0x0000000c   Code   RO         1013    i.ec600m_base_function_register  ec600m_common.o
    0x08004240   0x08004240   0x0000000c   Code   RO         1075    i.ec600m_tcpip_register  ec600m_tcpip.o
    0x0800424c   0x0800424c   0x00000002   Code   RO          488    i.flow_meter_protocol_parsing  flow_meter_protocol.o
    0x0800424e   0x0800424e   0x00000002   PAD
    0x08004250   0x08004250   0x00000018   Code   RO         1169    i.fputc             debug.o
    0x08004268   0x08004268   0x00000054   Code   RO         5627    i.free              mc_p.l(malloc.o)
    0x080042bc   0x080042bc   0x000000dc   Code   RO          904    i.get_font_info     lcd_font.o
    0x08004398   0x08004398   0x0000002c   Code   RO          905    i.get_font_size     lcd_font.o
    0x080043c4   0x080043c4   0x00000010   Code   RO          906    i.get_font_type     lcd_font.o
    0x080043d4   0x080043d4   0x00000030   Code   RO          612    i.get_hex_data      net_protocol.o
    0x08004404   0x08004404   0x00000090   Code   RO         1014    i.get_info          ec600m_common.o
    0x08004494   0x08004494   0x00000088   Code   RO          613    i.get_net_info      net_protocol.o
    0x0800451c   0x0800451c   0x00000060   Code   RO         1015    i.get_net_sig       ec600m_common.o
    0x0800457c   0x0800457c   0x00000040   Code   RO          341    i.get_serial_port   protocol_common.o
    0x080045bc   0x080045bc   0x00000008   Code   RO          389    i.get_water_value   daosheng_protocol.o
    0x080045c4   0x080045c4   0x00000044   Code   RO          614    i.hex_to_dec        net_protocol.o
    0x08004608   0x08004608   0x00000030   Code   RO          769    i.lcd_clear         lcd12864.o
    0x08004638   0x08004638   0x00000038   Code   RO          771    i.lcd_draw_pixel    lcd12864.o
    0x08004670   0x08004670   0x00000060   Code   RO          772    i.lcd_gpio_init     lcd12864.o
    0x080046d0   0x080046d0   0x000000b4   Code   RO          773    i.lcd_init          lcd12864.o
    0x08004784   0x08004784   0x00000044   Code   RO          774    i.lcd_refresh       lcd12864.o
    0x080047c8   0x080047c8   0x00000080   Code   RO          775    i.lcd_show_char     lcd12864.o
    0x08004848   0x08004848   0x000000a8   Code   RO          776    i.lcd_show_string   lcd12864.o
    0x080048f0   0x080048f0   0x00000058   Code   RO          777    i.lcd_write_command  lcd12864.o
    0x08004948   0x08004948   0x00000058   Code   RO          778    i.lcd_write_data    lcd12864.o
    0x080049a0   0x080049a0   0x0000013c   Code   RO            5    i.main              main.o
    0x08004adc   0x08004adc   0x00000008   Code   RO         4108    i.main_thread_entry  components.o
    0x08004ae4   0x08004ae4   0x0000006c   Code   RO         5628    i.malloc            mc_p.l(malloc.o)
    0x08004b50   0x08004b50   0x00000038   Code   RO          390    i.mbus_crc16        daosheng_protocol.o
    0x08004b88   0x08004b88   0x00000038   Code   RO          528    i.modbus_crc16      modbus_rtu.o
    0x08004bc0   0x08004bc0   0x00000038   Code   RO          615    i.modbus_crc16      net_protocol.o
    0x08004bf8   0x08004bf8   0x0000001c   Code   RO          939    i.net_clean         net.o
    0x08004c14   0x08004c14   0x00000050   Code   RO          616    i.net_data_parsing  net_protocol.o
    0x08004c64   0x08004c64   0x0000004c   Code   RO          940    i.net_init          net.o
    0x08004cb0   0x08004cb0   0x00000050   Code   RO          941    i.net_module_init   net.o
    0x08004d00   0x08004d00   0x00000084   Code   RO          942    i.net_module_reboot  net.o
    0x08004d84   0x08004d84   0x000000a8   Code   RO          618    i.net_protocol_parsing  net_protocol.o
    0x08004e2c   0x08004e2c   0x00000028   Code   RO          943    i.net_send_cmd      net.o
    0x08004e54   0x08004e54   0x00000018   Code   RO          944    i.net_send_data     net.o
    0x08004e6c   0x08004e6c   0x00000158   Code   RO          619    i.net_send_packet   net_protocol.o
    0x08004fc4   0x08004fc4   0x000000b4   Code   RO          945    i.net_serial_init   net.o
    0x08005078   0x08005078   0x00000098   Code   RO            6    i.net_task_init     main.o
    0x08005110   0x08005110   0x0000002c   Code   RO            7    i.net_task_process  main.o
    0x0800513c   0x0800513c   0x00000238   Code   RO          574    i.pc_protocol_parsing  pc_protocol.o
    0x08005374   0x08005374   0x000000ec   Code   RO         4686    i.plug_holes        mem.o
    0x08005460   0x08005460   0x00000144   Code   RO         4515    i.print_number      kservice.o
    0x080055a4   0x080055a4   0x000000dc   Code   RO          342    i.protocol_parsing  protocol_common.o
    0x08005680   0x08005680   0x00000074   Code   RO          310    i.read_config       user_config.o
    0x080056f4   0x080056f4   0x00000024   Code   RO         1076    i.recv              ec600m_tcpip.o
    0x08005718   0x08005718   0x00000050   Code   RO         4109    i.rt_application_init  components.o
    0x08005768   0x08005768   0x00000024   Code   RO         4516    i.rt_assert_handler  kservice.o
    0x0800578c   0x0800578c   0x00000018   Code   RO         4931    i.rt_enter_critical  scheduler.o
    0x080057a4   0x080057a4   0x00000034   Code   RO         4932    i.rt_exit_critical  scheduler.o
    0x080057d8   0x080057d8   0x00000134   Code   RO         4688    i.rt_free           mem.o
    0x0800590c   0x0800590c   0x00000008   Code   RO         3965    i.rt_heap_begin_get  board.o
    0x08005914   0x08005914   0x00000008   Code   RO         3966    i.rt_heap_end_get   board.o
    0x0800591c   0x0800591c   0x00000030   Code   RO         3967    i.rt_hw_board_init  board.o
    0x0800594c   0x0800594c   0x00000002   Code   RO         5294    i.rt_hw_hard_fault_exception  cpuport.o
    0x0800594e   0x0800594e   0x00000002   PAD
    0x08005950   0x08005950   0x0000003c   Code   RO         5295    i.rt_hw_stack_init  cpuport.o
    0x0800598c   0x0800598c   0x00000024   Code   RO         4474    i.rt_interrupt_enter  irq.o
    0x080059b0   0x080059b0   0x00000018   Code   RO         4476    i.rt_interrupt_get_nest  irq.o
    0x080059c8   0x080059c8   0x00000024   Code   RO         4477    i.rt_interrupt_leave  irq.o
    0x080059ec   0x080059ec   0x0000000e   Code   RO         4210    i.rt_ipc_list_resume  ipc.o
    0x080059fa   0x080059fa   0x0000005a   Code   RO         4212    i.rt_ipc_list_suspend  ipc.o
    0x08005a54   0x08005a54   0x0000000e   Code   RO         4821    i.rt_list_insert_after  object.o
    0x08005a62   0x08005a62   0x0000000e   Code   RO         5029    i.rt_list_insert_after  thread.o
    0x08005a70   0x08005a70   0x0000000e   Code   RO         5179    i.rt_list_insert_after  timer.o
    0x08005a7e   0x08005a7e   0x0000000e   Code   RO         4213    i.rt_list_insert_before  ipc.o
    0x08005a8c   0x08005a8c   0x0000000e   Code   RO         4214    i.rt_list_isempty   ipc.o
    0x08005a9a   0x08005a9a   0x0000000e   Code   RO         5180    i.rt_list_isempty   timer.o
    0x08005aa8   0x08005aa8   0x00000012   Code   RO         4822    i.rt_list_remove    object.o
    0x08005aba   0x08005aba   0x00000012   Code   RO         5030    i.rt_list_remove    thread.o
    0x08005acc   0x08005acc   0x000001e4   Code   RO         4690    i.rt_malloc         mem.o
    0x08005cb0   0x08005cb0   0x00000046   Code   RO         4524    i.rt_memset         kservice.o
    0x08005cf6   0x08005cf6   0x00000002   PAD
    0x08005cf8   0x08005cf8   0x000000b0   Code   RO         4823    i.rt_object_allocate  object.o
    0x08005da8   0x08005da8   0x0000009c   Code   RO         4825    i.rt_object_delete  object.o
    0x08005e44   0x08005e44   0x00000058   Code   RO         4826    i.rt_object_detach  object.o
    0x08005e9c   0x08005e9c   0x00000020   Code   RO         4829    i.rt_object_get_information  object.o
    0x08005ebc   0x08005ebc   0x00000034   Code   RO         4830    i.rt_object_get_type  object.o
    0x08005ef0   0x08005ef0   0x000000ac   Code   RO         4831    i.rt_object_init    object.o
    0x08005f9c   0x08005f9c   0x0000003c   Code   RO         4832    i.rt_object_is_systemobject  object.o
    0x08005fd8   0x08005fd8   0x000000d8   Code   RO         4933    i.rt_schedule       scheduler.o
    0x080060b0   0x080060b0   0x00000084   Code   RO         4934    i.rt_schedule_insert_thread  scheduler.o
    0x08006134   0x08006134   0x0000007c   Code   RO         4935    i.rt_schedule_remove_thread  scheduler.o
    0x080061b0   0x080061b0   0x0000006c   Code   RO         4243    i.rt_sem_init       ipc.o
    0x0800621c   0x0800621c   0x0000008c   Code   RO         4244    i.rt_sem_release    ipc.o
    0x080062a8   0x080062a8   0x00000118   Code   RO         4245    i.rt_sem_take       ipc.o
    0x080063c0   0x080063c0   0x00000002   Code   RO         4526    i.rt_show_version   kservice.o
    0x080063c2   0x080063c2   0x00000014   Code   RO         4528    i.rt_sprintf        kservice.o
    0x080063d6   0x080063d6   0x00000010   Code   RO         4532    i.rt_strlen         kservice.o
    0x080063e6   0x080063e6   0x00000028   Code   RO         4534    i.rt_strncpy        kservice.o
    0x0800640e   0x0800640e   0x00000002   PAD
    0x08006410   0x08006410   0x00000088   Code   RO         4694    i.rt_system_heap_init  mem.o
    0x08006498   0x08006498   0x00000030   Code   RO         4937    i.rt_system_scheduler_init  scheduler.o
    0x080064c8   0x080064c8   0x00000028   Code   RO         4938    i.rt_system_scheduler_start  scheduler.o
    0x080064f0   0x080064f0   0x00000018   Code   RO         5181    i.rt_system_timer_init  timer.o
    0x08006508   0x08006508   0x00000002   Code   RO         5182    i.rt_system_timer_thread_init  timer.o
    0x0800650a   0x0800650a   0x00000044   Code   RO         5032    i.rt_thread_create  thread.o
    0x0800654e   0x0800654e   0x00000002   PAD
    0x08006550   0x08006550   0x00000054   Code   RO         5037    i.rt_thread_exit    thread.o
    0x080065a4   0x080065a4   0x00000020   Code   RO         4152    i.rt_thread_idle_entry  idle.o
    0x080065c4   0x080065c4   0x000000a8   Code   RO         4153    i.rt_thread_idle_excute  idle.o
    0x0800666c   0x0800666c   0x00000058   Code   RO         4155    i.rt_thread_idle_init  idle.o
    0x080066c4   0x080066c4   0x00000078   Code   RO         5039    i.rt_thread_init    thread.o
    0x0800673c   0x0800673c   0x00000090   Code   RO         5042    i.rt_thread_resume  thread.o
    0x080067cc   0x080067cc   0x0000000c   Code   RO         5044    i.rt_thread_self    thread.o
    0x080067d8   0x080067d8   0x000000b8   Code   RO         5046    i.rt_thread_startup  thread.o
    0x08006890   0x08006890   0x000000cc   Code   RO         5047    i.rt_thread_suspend  thread.o
    0x0800695c   0x0800695c   0x000000b8   Code   RO         5049    i.rt_thread_timeout  thread.o
    0x08006a14   0x08006a14   0x0000000a   Code   RO         5050    i.rt_thread_yield   thread.o
    0x08006a1e   0x08006a1e   0x00000002   PAD
    0x08006a20   0x08006a20   0x0000000c   Code   RO         4071    i.rt_tick_get       clock.o
    0x08006a2c   0x08006a2c   0x00000034   Code   RO         4072    i.rt_tick_increase  clock.o
    0x08006a60   0x08006a60   0x00000084   Code   RO         5183    i.rt_timer_check    timer.o
    0x08006ae4   0x08006ae4   0x000000b8   Code   RO         5184    i.rt_timer_control  timer.o
    0x08006b9c   0x08006b9c   0x000000d8   Code   RO         5187    i.rt_timer_detach   timer.o
    0x08006c74   0x08006c74   0x00000050   Code   RO         5190    i.rt_timer_init     timer.o
    0x08006cc4   0x08006cc4   0x0000018c   Code   RO         5193    i.rt_timer_start    timer.o
    0x08006e50   0x08006e50   0x000000b8   Code   RO         5194    i.rt_timer_stop     timer.o
    0x08006f08   0x08006f08   0x0000029c   Code   RO         4537    i.rt_vsnprintf      kservice.o
    0x080071a4   0x080071a4   0x00000010   Code   RO         4538    i.rt_vsprintf       kservice.o
    0x080071b4   0x080071b4   0x00000074   Code   RO         1353    i.rtc_get_calendar  rtc.o
    0x08007228   0x08007228   0x000000b8   Code   RO         1354    i.rtc_init          rtc.o
    0x080072e0   0x080072e0   0x00000040   Code   RO         1356    i.rtc_set_time      rtc.o
    0x08007320   0x08007320   0x0000002a   Code   RO         4110    i.rtthread_startup  components.o
    0x0800734a   0x0800734a   0x00000002   PAD
    0x0800734c   0x0800734c   0x000000bc   Code   RO          396    i.schedule_commands  daosheng_protocol.o
    0x08007408   0x08007408   0x0000001c   Code   RO         1077    i.send              ec600m_tcpip.o
    0x08007424   0x08007424   0x00000074   Code   RO         1016    i.send_cmd          ec600m_common.o
    0x08007498   0x08007498   0x00000010   Code   RO         1017    i.send_data         ec600m_common.o
    0x080074a8   0x080074a8   0x00000154   Code   RO         1127    i.serial_485_init   uart.o
    0x080075fc   0x080075fc   0x00000040   Code   RO         1128    i.serial_485_send_dat  uart.o
    0x0800763c   0x0800763c   0x00000060   Code   RO          620    i.set_net_info      net_protocol.o
    0x0800769c   0x0800769c   0x00000020   Code   RO         4539    i.skip_atoi         kservice.o
    0x080076bc   0x080076bc   0x0000002a   Code   RO         1208    i.stm32_flash_erase  stm32_flash.o
    0x080076e6   0x080076e6   0x0000001a   Code   RO         1209    i.stm32_flash_read  stm32_flash.o
    0x08007700   0x08007700   0x00000004   Code   RO         1210    i.stm32_flash_read_byte  stm32_flash.o
    0x08007704   0x08007704   0x0000010c   Code   RO         1213    i.stm32_flash_write  stm32_flash.o
    0x08007810   0x08007810   0x00000028   Code   RO         1214    i.stm32_flash_write_nbyte  stm32_flash.o
    0x08007838   0x08007838   0x00000120   Code   RO         1018    i.sync_time         ec600m_common.o
    0x08007958   0x08007958   0x0000007c   Code   RO         1435    i.system_clock_init  system.o
    0x080079d4   0x080079d4   0x00000002   Code   RO          508    i.water_meter_protocol_parsing  water_meter_protocol.o
    0x080079d6   0x080079d6   0x00000002   PAD
    0x080079d8   0x080079d8   0x0000005c   Code   RO         1019    i.watting           ec600m_common.o
    0x08007a34   0x08007a34   0x0000000e   Data   RO          577    .constdata          pc_protocol.o
    0x08007a42   0x08007a42   0x00001c2c   Data   RO          907    .constdata          lcd_font.o
    0x0800966e   0x0800966e   0x00000019   Data   RO         1396    .constdata          system_stm32l0xx.o
    0x08009687   0x08009687   0x00000008   Data   RO         1397    .constdata          system_stm32l0xx.o
    0x0800968f   0x0800968f   0x00000014   Data   RO         4111    .constdata          components.o
    0x080096a3   0x080096a3   0x00000016   Data   RO         4158    .constdata          idle.o
    0x080096b9   0x080096b9   0x000001fc   Data   RO         4247    .constdata          ipc.o
    0x080098b5   0x080098b5   0x00000122   Data   RO         4540    .constdata          kservice.o
    0x080099d7   0x080099d7   0x0000003c   Data   RO         4696    .constdata          mem.o
    0x08009a13   0x08009a13   0x00000080   Data   RO         4837    .constdata          object.o
    0x08009a93   0x08009a93   0x0000004e   Data   RO         4940    .constdata          scheduler.o
    0x08009ae1   0x08009ae1   0x000000cf   Data   RO         5051    .constdata          thread.o
    0x08009bb0   0x08009bb0   0x0000005c   Data   RO         5195    .constdata          timer.o
    0x08009c0c   0x08009c0c   0x00000081   Data   RO         5754    .constdata          mc_p.l(ctype_o.o)
    0x08009c8d   0x08009c8d   0x00000003   PAD
    0x08009c90   0x08009c90   0x00000004   Data   RO         5755    .constdata          mc_p.l(ctype_o.o)
    0x08009c94   0x08009c94   0x00000045   Data   RO         4248    .conststring        ipc.o
    0x08009cd9   0x08009cd9   0x00000003   PAD
    0x08009cdc   0x08009cdc   0x000000eb   Data   RO         4697    .conststring        mem.o
    0x08009dc7   0x08009dc7   0x00000001   PAD
    0x08009dc8   0x08009dc8   0x00000042   Data   RO         5052    .conststring        thread.o
    0x08009e0a   0x08009e0a   0x00000002   PAD
    0x08009e0c   0x08009e0c   0x00000020   Data   RO         5789    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08009e2c, Size: 0x000049a0, Max: 0x00005000, ABSOLUTE, COMPRESSED[0x00000098])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x0000001c   Data   RW            9    .data               main.o
    0x2000001c   COMPRESSED   0x00000004   Data   RW          344    .data               protocol_common.o
    0x20000020   COMPRESSED   0x00000020   Data   RW          400    .data               daosheng_protocol.o
    0x20000040   COMPRESSED   0x00000004   Data   RW          622    .data               net_protocol.o
    0x20000044   COMPRESSED   0x00000014   Data   RW          782    .data               lcd12864.o
    0x20000058   COMPRESSED   0x00000214   Data   RW          947    .data               net.o
    0x2000026c   COMPRESSED   0x00000018   Data   RW         1020    .data               ec600m_common.o
    0x20000284   COMPRESSED   0x00000010   Data   RW         1078    .data               ec600m_tcpip.o
    0x20000294   COMPRESSED   0x00000004   Data   RW         1398    .data               system_stm32l0xx.o
    0x20000298   COMPRESSED   0x0000000c   Data   RW         1486    .data               stm32l0xx_hal.o
    0x200002a4   COMPRESSED   0x00000004   Data   RW         4074    .data               clock.o
    0x200002a8   COMPRESSED   0x0000000c   Data   RW         4479    .data               irq.o
    0x200002b4   COMPRESSED   0x00000008   Data   RW         4541    .data               kservice.o
    0x200002bc   COMPRESSED   0x00000020   Data   RW         4698    .data               mem.o
    0x200002dc   COMPRESSED   0x00000094   Data   RW         4838    .data               object.o
    0x20000370   COMPRESSED   0x00000018   Data   RW         4941    .data               scheduler.o
    0x20000388   COMPRESSED   0x0000000c   Data   RW         5053    .data               thread.o
    0x20000394   COMPRESSED   0x00000014   Data   RW         5196    .data               timer.o
    0x200003a8   COMPRESSED   0x00000004   Data   RW         5296    .data               cpuport.o
    0x200003ac   COMPRESSED   0x00000004   Data   RW         5297    .data               cpuport.o
    0x200003b0   COMPRESSED   0x00000004   Data   RW         5298    .data               cpuport.o
    0x200003b4   COMPRESSED   0x00000004   Data   RW         5718    .data               mc_p.l(stdout.o)
    0x200003b8   COMPRESSED   0x00000004   Data   RW         5723    .data               mc_p.l(mvars.o)
    0x200003bc   COMPRESSED   0x00000004   Data   RW         5724    .data               mc_p.l(mvars.o)
    0x200003c0   COMPRESSED   0x00000004   Data   RW         5728    .data               mc_p.l(errno.o)
    0x200003c4        -       0x0000000c   Zero   RW            8    .bss                main.o
    0x200003d0        -       0x0000003c   Zero   RW          311    .bss                user_config.o
    0x2000040c        -       0x00000042   Zero   RW          343    .bss                protocol_common.o
    0x2000044e        -       0x00000010   Zero   RW          398    .bss                daosheng_protocol.o
    0x2000045e        -       0x0000001e   Zero   RW          575    .bss                pc_protocol.o
    0x2000047c        -       0x00000080   Zero   RW          621    .bss                net_protocol.o
    0x200004fc        -       0x00000400   Zero   RW          779    .bss                lcd12864.o
    0x200008fc        -       0x00000084   Zero   RW          946    .bss                net.o
    0x20000980        -       0x000000c8   Zero   RW         1129    .bss                uart.o
    0x20000a48        -       0x00000084   Zero   RW         1170    .bss                debug.o
    0x20000acc        -       0x00000080   Zero   RW         1215    .bss                stm32_flash.o
    0x20000b4c        -       0x00000068   Zero   RW         1304    .bss                adc.o
    0x20000bb4        -       0x00000024   Zero   RW         1357    .bss                rtc.o
    0x20000bd8        -       0x00000018   Zero   RW         3713    .bss                stm32l0xx_hal_flash.o
    0x20000bf0        -       0x00003800   Zero   RW         3968    .bss                board.o
    0x200043f0        -       0x00000190   Zero   RW         4157    .bss                idle.o
    0x20004580        -       0x00000020   Zero   RW         4695    .bss                mem.o
    0x200045a0        -       0x00000100   Zero   RW         4939    .bss                scheduler.o
    0x200046a0        -       0x00000000   Zero   RW         1427    HEAP                startup_stm32l072xx.o
    0x200046a0        -       0x00000300   Zero   RW         1426    STACK               startup_stm32l072xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       280         92          0          0        104       2423   adc.o
         2          0          0          0          0        376   auto_ctrl_task.o
       192         24          0          0      14336       9890   board.o
        64         10          0          0          0       1013   bsp_board.o
        64         10          0          4          0       1069   clock.o
       140         32         20          0          0       1938   components.o
       216         32          0          0          0        756   context_rvds.o
        62          4          0         12          0       2914   cpuport.o
       290         20          0         32         16       3922   daosheng_protocol.o
       140         20          0          0        132       2961   debug.o
       764        110          0         24          0       5812   ec600m_common.o
       444        162          0         16          0       3520   ec600m_tcpip.o
         2          0          0          0          0        446   flow_meter_protocol.o
       308         40         22          0        400       5670   idle.o
       660        104        577          0          0      34192   ipc.o
        96         14          0         12          0       2086   irq.o
      1292         24        290          8          0      10521   kservice.o
      1472        242          0         20       1024       9807   lcd12864.o
       280         22       7212          0          0       2912   lcd_font.o
       588        104          0         28         12     413372   main.o
      1164        352        295         32         32       5445   mem.o
        56          8          0          0          0       1147   modbus_rtu.o
       632         86          0        532        132       6125   net.o
      2076        158          0          4        128      13631   net_protocol.o
       768        242        128        148          0      10102   object.o
       594         24         14          0         30       2262   pc_protocol.o
       284         30          0          4         66       1337   protocol_common.o
       364         30          0          0         36       2898   rtc.o
       748        134         78         24        256       9298   scheduler.o
        28          8        192          0        768        592   startup_stm32l072xx.o
       548         16          0          0        128       6697   stm32_flash.o
       186         28          0         12          0       9203   stm32l0xx_hal.o
      1328         38          0          0          0       7999   stm32l0xx_hal_adc.o
       148         24          0          0          0      11865   stm32l0xx_hal_cortex.o
       440         54          0          0         24       6094   stm32l0xx_hal_flash.o
       304         46          0          0          0       3480   stm32l0xx_hal_flash_ex.o
       388         34          0          0          0       2769   stm32l0xx_hal_gpio.o
        16          4          0          0          0        451   stm32l0xx_hal_pwr.o
      2010         94          0          0          0       6904   stm32l0xx_hal_rcc.o
       416         12          0          0          0       1540   stm32l0xx_hal_rcc_ex.o
       980         26          0          0          0       7236   stm32l0xx_hal_rtc.o
        24          0          0          0          0       2119   stm32l0xx_hal_rtc_ex.o
      1432         70          0          0          0       6796   stm32l0xx_hal_uart.o
       124          8          0          0          0        544   system.o
       204         30         33          4          0       2051   system_stm32l0xx.o
      1226        390        273         12          0      15018   thread.o
      1318        476         92         20          0      11407   timer.o
       468         42          0          0        200       3124   uart.o
       152         24          0          0         60       2728   user_config.o
         2          0          0          0          0        455   water_meter_protocol.o

    ----------------------------------------------------------------------
     25824       <USER>       <GROUP>        948      17884     676917   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        40          0          6          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
        30          0          0          0          0         60   _chval.o
       166          0          0          0          0         84   _strtoul.o
        26          0          0          0          0         72   atoi.o
         8          4        133          0          0         60   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
         8          4          0          4          0         60   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
       122          0          0          0          0         84   llmul.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
       192         20          0          0          0        144   malloc.o
        26          0          0          0          0         72   memcmp.o
        36          0          0          0          0         60   memcpya.o
        36          0          0          0          0        100   memseta.o
         0          0          0          8          0          0   mvars.o
      2384        108          0          0          0        708   printfa.o
         0          0          0          4          0          0   stdout.o
        20          0          0          0          0         60   strchr.o
        18          0          0          0          0         60   strcpy.o
        14          0          0          0          0         60   strlen.o
        26          0          0          0          0         68   strncpy.o
        40          0          0          0          0         72   strstr.o
       112          4          0          0          0         76   strtol.o
        44          0          0          0          0         72   uidiv.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdrcmple.o
       360          6          0          0          0        140   dadd.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        64         10          0          0          0         68   dfixul.o
       208          6          0          0          0         88   dmul.o
        40          0          0          0          0         60   f2d.o
       124          0          0          0          0         72   fdiv.o
       130          0          0          0          0        144   fepilogue.o
        14          0          0          0          0         68   ffltui.o
       122          0          0          0          0         72   fmul.o

    ----------------------------------------------------------------------
      5268        <USER>        <GROUP>         16          0       3408   Library Totals
         6          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      3684        156        133         16          0       2328   mc_p.l
      1578         30          0          0          0       1080   mf_p.l

    ----------------------------------------------------------------------
      5268        <USER>        <GROUP>         16          0       3408   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     31092       3740       9400        964      17884     668213   Grand Totals
     31092       3740       9400        152      17884     668213   ELF Image Totals (compressed)
     31092       3740       9400        152          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                40492 (  39.54kB)
    Total RW  Size (RW Data + ZI Data)             18848 (  18.41kB)
    Total ROM Size (Code + RO Data + RW Data)      40644 (  39.69kB)

==============================================================================

