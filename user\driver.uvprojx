<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>driver</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060960::V5.06 update 7 (build 960)::.\ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32L072CBTx</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32L0xx_DFP.3.0.0</PackID>
          <PackURL>https://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00005000) IROM(0x08000000,0x00020000) CPUTYPE("Cortex-M0+") CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0STM32L0xx_128 -********** -FL020000 -FP0($$Device:STM32L072CBTx$CMSIS\Flash\STM32L0xx_128.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile></RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32L072CBTx$CMSIS\SVD\STM32L0x2.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>driver</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>1</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP-MPU </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0+</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments>-MPU </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0+</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M0+"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x5000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x20000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x1c000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x5000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>2</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>1</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>5</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>--locale=english</MiscControls>
              <Define>STM32L072xx,USE_HAL_DRIVER,</Define>
              <Undefine></Undefine>
              <IncludePath>..\app\include;..\drivers\include;..\peripheral\include;..\rtthread\bsp;..\rtthread\include;..\stm32L0xx_hal\Inc;..\system\include;..\lcd12864;..\protocol\include;..\net;..\net\EC600x\include;..\stm32L0xx_hal\Inc\Legacy</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>1</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>app</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\app\main.c</FilePath>
            </File>
            <File>
              <FileName>auto_ctrl_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\app\auto_ctrl_task.c</FilePath>
            </File>
            <File>
              <FileName>user_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\app\user_config.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>protocol</GroupName>
          <Files>
            <File>
              <FileName>protocol_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\protocol\protocol_common.c</FilePath>
            </File>
            <File>
              <FileName>daoSheng_protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\protocol\daoSheng_protocol.c</FilePath>
            </File>
            <File>
              <FileName>flow_meter_protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\protocol\flow_meter_protocol.c</FilePath>
            </File>
            <File>
              <FileName>water_meter_protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\protocol\water_meter_protocol.c</FilePath>
            </File>
            <File>
              <FileName>modbus_rtu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\protocol\modbus_rtu.c</FilePath>
            </File>
            <File>
              <FileName>pc_protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\protocol\pc_protocol.c</FilePath>
            </File>
            <File>
              <FileName>net_protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\protocol\net_protocol.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>drivers</GroupName>
          <Files>
            <File>
              <FileName>bsp_board.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\drivers\bsp_board.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lcd_12864</GroupName>
          <Files>
            <File>
              <FileName>lcd12864.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lcd12864\lcd12864.c</FilePath>
            </File>
            <File>
              <FileName>lcd_font.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\lcd12864\lcd_font.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>net</GroupName>
          <Files>
            <File>
              <FileName>net.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\net\net.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>ec600x</GroupName>
          <Files>
            <File>
              <FileName>ec600m_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\net\EC600x\ec600m_common.c</FilePath>
            </File>
            <File>
              <FileName>ec600m_tcpip.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\net\EC600x\ec600m_tcpip.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>peripheral</GroupName>
          <Files>
            <File>
              <FileName>uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\peripheral\uart.c</FilePath>
            </File>
            <File>
              <FileName>debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\peripheral\debug.c</FilePath>
            </File>
            <File>
              <FileName>stm32_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\peripheral\stm32_flash.c</FilePath>
            </File>
            <File>
              <FileName>adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\peripheral\adc.c</FilePath>
            </File>
            <File>
              <FileName>rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\peripheral\rtc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>system</GroupName>
          <Files>
            <File>
              <FileName>system_stm32l0xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\system\system_stm32l0xx.c</FilePath>
            </File>
            <File>
              <FileName>startup_stm32l072xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\system\startup_stm32l072xx.s</FilePath>
            </File>
            <File>
              <FileName>system.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\system\system.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>stm32l0xx_hal</GroupName>
          <Files>
            <File>
              <FileName>stm32l0xx_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_adc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_adc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_exti.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_pwr_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_pwr_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_rtc.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_rtc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_rtc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_tim_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_tim_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_uart.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_uart_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_uart_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_usart.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32l0xx_hal_flash_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\stm32L0xx_hal\Src\stm32l0xx_hal_flash_ex.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>rtthread_bsp</GroupName>
          <Files>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\bsp\board.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>rtthread_core</GroupName>
          <Files>
            <File>
              <FileName>clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\clock.c</FilePath>
            </File>
            <File>
              <FileName>components.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\components.c</FilePath>
            </File>
            <File>
              <FileName>cpu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\cpu.c</FilePath>
            </File>
            <File>
              <FileName>device.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\device.c</FilePath>
            </File>
            <File>
              <FileName>idle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\idle.c</FilePath>
            </File>
            <File>
              <FileName>ipc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\ipc.c</FilePath>
            </File>
            <File>
              <FileName>irq.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\irq.c</FilePath>
            </File>
            <File>
              <FileName>kservice.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\kservice.c</FilePath>
            </File>
            <File>
              <FileName>mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\mem.c</FilePath>
            </File>
            <File>
              <FileName>memheap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\memheap.c</FilePath>
            </File>
            <File>
              <FileName>mempool.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\mempool.c</FilePath>
            </File>
            <File>
              <FileName>object.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\object.c</FilePath>
            </File>
            <File>
              <FileName>scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\scheduler.c</FilePath>
            </File>
            <File>
              <FileName>signal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\signal.c</FilePath>
            </File>
            <File>
              <FileName>slab.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\slab.c</FilePath>
            </File>
            <File>
              <FileName>thread.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\thread.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\src\timer.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>1</IncludeInBuild>
                  <AlwaysBuild>1</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>rtthread_port</GroupName>
          <Files>
            <File>
              <FileName>cpuport.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\rtthread\libcpu\arm\cortex-m0\cpuport.c</FilePath>
            </File>
            <File>
              <FileName>context_rvds.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\rtthread\libcpu\arm\cortex-m0\context_rvds.S</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>driver</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
