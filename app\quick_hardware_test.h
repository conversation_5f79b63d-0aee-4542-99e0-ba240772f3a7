/**
 * 快速硬件资源测试函数头文件
 */

#ifndef __QUICK_HARDWARE_TEST_H__
#define __QUICK_HARDWARE_TEST_H__

#include <stdint.h>

/**
 * 快速硬件资源测试
 * 在系统启动时进行基本的硬件验证
 */
void quick_hardware_test(void);

/**
 * 硬件状态监控函数
 * 定期检查关键硬件状态
 */
void hardware_status_monitor(void);

/**
 * 硬件错误检测函数
 */
void hardware_error_check(void);

/**
 * 硬件诊断信息输出
 */
void hardware_diagnostic_info(void);

#endif /* __QUICK_HARDWARE_TEST_H__ */
