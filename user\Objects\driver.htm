<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\driver.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\driver.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Fri Aug 29 16:00:04 2025
<BR><P>
<H3>Maximum Stack Usage =        288 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; rtthread_startup &rArr; rt_thread_idle_init &rArr; rt_thread_init &rArr; _rt_thread_init &rArr; rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[5]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">NMI_Handler</a><BR>
 <LI><a href="#[7]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">SVC_Handler</a><BR>
 <LI><a href="#[16]">ADC1_COMP_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[16]">ADC1_COMP_IRQHandler</a><BR>
 <LI><a href="#[36]">rt_hw_hard_fault_exception</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[36]">rt_hw_hard_fault_exception</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[16]">ADC1_COMP_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[13]">DMA1_Channel1_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[14]">DMA1_Channel2_3_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[15]">DMA1_Channel4_5_6_7_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[f]">EXTI0_1_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[10]">EXTI2_3_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[11]">EXTI4_15_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[d]">FLASH_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[6]">HardFault_Handler</a> from context_rvds.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[20]">I2C1_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[21]">I2C2_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[1e]">I2C3_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[17]">LPTIM1_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[5]">NMI_Handler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[b]">PVD_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from context_rvds.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[e]">RCC_CRS_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[26]">RNG_LPUART1_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[c]">RTC_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[4]">Reset_Handler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[22]">SPI1_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[23]">SPI2_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[7]">SVC_Handler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from board.o(i.SysTick_Handler) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[29]">SystemInit</a> from system_stm32l0xx.o(i.SystemInit) referenced from startup_stm32l072xx.o(.text)
 <LI><a href="#[1d]">TIM21_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[1f]">TIM22_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[19]">TIM2_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[1a]">TIM3_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[1b]">TIM6_DAC_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[1c]">TIM7_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[12]">TSC_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[24]">USART1_IRQHandler</a> from net.o(i.USART1_IRQHandler) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[25]">USART2_IRQHandler</a> from uart.o(i.USART2_IRQHandler) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[18]">USART4_5_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[27]">USB_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[a]">WWDG_IRQHandler</a> from startup_stm32l072xx.o(.text) referenced from startup_stm32l072xx.o(RESET)
 <LI><a href="#[2a]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32l072xx.o(.text)
 <LI><a href="#[2c]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0snprintf)
 <LI><a href="#[2d]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[2b]">fputc</a> from debug.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[28]">main</a> from components.o(i.$Sub$$main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[30]">main_thread_entry</a> from components.o(i.main_thread_entry) referenced from components.o(i.rt_application_init)
 <LI><a href="#[1]">net_clean</a> from net.o(i.net_clean) referenced 2 times from net.o(.data)
 <LI><a href="#[3]">net_send_cmd</a> from net.o(i.net_send_cmd) referenced 2 times from net.o(.data)
 <LI><a href="#[2]">net_send_data</a> from net.o(i.net_send_data) referenced 2 times from net.o(.data)
 <LI><a href="#[0]">net_serial_init</a> from net.o(i.net_serial_init) referenced 2 times from net.o(.data)
 <LI><a href="#[2e]">rt_thread_exit</a> from thread.o(i.rt_thread_exit) referenced from thread.o(i._rt_thread_init)
 <LI><a href="#[31]">rt_thread_idle_entry</a> from idle.o(i.rt_thread_idle_entry) referenced from idle.o(i.rt_thread_idle_init)
 <LI><a href="#[2f]">rt_thread_timeout</a> from thread.o(i.rt_thread_timeout) referenced from thread.o(i._rt_thread_init)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[2a]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(.text)
</UL>
<P><STRONG><a name="[117]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[32]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[4b]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[118]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[119]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[11a]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[34]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))
<BR><BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__arm_fini_ (Weak Reference)
</UL>

<P><STRONG><a name="[11b]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[11c]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[4]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>ADC1_COMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_COMP_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_COMP_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>DMA1_Channel2_3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA1_Channel4_5_6_7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>EXTI0_1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI2_3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI4_15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>I2C1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>I2C2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>I2C3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>LPTIM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>RCC_CRS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>RNG_LPUART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>TIM21_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>TIM22_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>TSC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>USART4_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>USB_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32l072xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[9b]"></a>rt_hw_interrupt_disable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_malloc
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_detach
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_delete
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_get_nest
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_enter_critical
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_heap_init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_stop
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_detach
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_remove_thread
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_insert_thread
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_scheduler_stack_check
</UL>

<P><STRONG><a name="[d6]"></a>rt_hw_interrupt_enable</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_malloc
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_detach
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_delete
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_get_nest
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_enter_critical
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_heap_init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_stop
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_detach
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_remove_thread
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_insert_thread
</UL>

<P><STRONG><a name="[f4]"></a>rt_hw_context_switch</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
</UL>

<P><STRONG><a name="[f3]"></a>rt_hw_context_switch_interrupt</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
</UL>

<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[fd]"></a>rt_hw_context_switch_to</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_scheduler_start
</UL>

<P><STRONG><a name="[11d]"></a>rt_hw_interrupt_thread_switch</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, context_rvds.o(.text), UNUSED)

<P><STRONG><a name="[6]"></a>HardFault_Handler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, context_rvds.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_hard_fault_exception
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[11e]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[50]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemCoreClockUpdate
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DelayMicroSecond
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[37]"></a>__aeabi_uldivmod</STRONG> (Thumb, 96 bytes, Stack size 48 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[5a]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_serial_port
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_ReadBuffer
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_parsing
</UL>

<P><STRONG><a name="[11f]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[120]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[3b]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[121]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[122]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[3a]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_serial_port
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_parsing
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[86]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_485_init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_init
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;net_clean
</UL>

<P><STRONG><a name="[123]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[3c]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[b2]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_font_info
</UL>

<P><STRONG><a name="[ac]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate1
</UL>

<P><STRONG><a name="[66]"></a>__aeabi_fmul</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, fmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Power_Voltage
</UL>

<P><STRONG><a name="[3d]"></a>__aeabi_fdiv</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, fdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Power_Voltage
</UL>

<P><STRONG><a name="[3f]"></a>__aeabi_dmul</STRONG> (Thumb, 202 bytes, Stack size 72 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[41]"></a>__aeabi_ddiv</STRONG> (Thumb, 234 bytes, Stack size 40 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[43]"></a>__aeabi_ui2f</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, ffltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_ui2f &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Power_Voltage
</UL>

<P><STRONG><a name="[a6]"></a>__aeabi_f2d</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_status_monitor
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_error_check
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_diagnostic_info
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_battery_voltage
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate1
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[39]"></a>__aeabi_llsl</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, llshl.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[124]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[38]"></a>__aeabi_llsr</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, llushr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[125]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[126]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[3e]"></a>_float_round</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>

<P><STRONG><a name="[44]"></a>_float_epilogue</STRONG> (Thumb, 114 bytes, Stack size 12 bytes, fepilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
</UL>

<P><STRONG><a name="[42]"></a>_double_round</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[40]"></a>_double_epilogue</STRONG> (Thumb, 164 bytes, Stack size 48 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_clz
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[46]"></a>__aeabi_dadd</STRONG> (Thumb, 330 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[48]"></a>__aeabi_dsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[49]"></a>__aeabi_drsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[4a]"></a>__aeabi_d2ulz</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[94]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[33]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[127]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[47]"></a>__aeabi_lasr</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[128]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[129]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[12a]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[28]"></a>main</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, components.o(i.$Sub$$main))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = main &rArr; rtthread_startup &rArr; rt_thread_idle_init &rArr; rt_thread_init &rArr; _rt_thread_init &rArr; rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[53]"></a>ADC_Voltage_Init</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, adc.o(i.ADC_Voltage_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = ADC_Voltage_Init &rArr; HAL_ADC_ConfigChannel &rArr; ADC_DelayMicroSecond &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[56]"></a>EEPROM_Init</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32_flash.o(i.EEPROM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EEPROM_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWR_EnableBkUpAccess
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_DATAEEPROM_Unlock
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[59]"></a>EEPROM_ReadBuffer</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32_flash.o(i.EEPROM_ReadBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EEPROM_ReadBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config
</UL>

<P><STRONG><a name="[5b]"></a>EEPROM_WriteBuffer</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, stm32_flash.o(i.EEPROM_WriteBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = EEPROM_WriteBuffer &rArr; EEPROM_WriteByte &rArr; HAL_FLASHEx_DATAEEPROM_Program &rArr; FLASH_WaitForLastOperation &rArr; FLASH_SetErrorCode
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config
</UL>

<P><STRONG><a name="[5c]"></a>EEPROM_WriteByte</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32_flash.o(i.EEPROM_WriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = EEPROM_WriteByte &rArr; HAL_FLASHEx_DATAEEPROM_Program &rArr; FLASH_WaitForLastOperation &rArr; FLASH_SetErrorCode
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_DATAEEPROM_Program
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteBuffer
</UL>

<P><STRONG><a name="[5e]"></a>FLASH_WaitForLastOperation</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, stm32l0xx_hal_flash.o(i.FLASH_WaitForLastOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = FLASH_WaitForLastOperation &rArr; FLASH_SetErrorCode
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_SetErrorCode
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_DATAEEPROM_Program
</UL>

<P><STRONG><a name="[60]"></a>Get_Power_Voltage</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, adc.o(i.Get_Power_Voltage))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = Get_Power_Voltage &rArr; HAL_ADC_Start &rArr; ADC_Enable &rArr; ADC_DelayMicroSecond &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_PollForConversion
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_GetValue
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_battery_voltage
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate1
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[55]"></a>HAL_ADC_ConfigChannel</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, stm32l0xx_hal_adc.o(i.HAL_ADC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_ADC_ConfigChannel &rArr; ADC_DelayMicroSecond &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DelayMicroSecond
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Voltage_Init
</UL>

<P><STRONG><a name="[64]"></a>HAL_ADC_GetValue</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32l0xx_hal_adc.o(i.HAL_ADC_GetValue))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Power_Voltage
</UL>

<P><STRONG><a name="[54]"></a>HAL_ADC_Init</STRONG> (Thumb, 452 bytes, Stack size 16 bytes, stm32l0xx_hal_adc.o(i.HAL_ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_ADC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Voltage_Init
</UL>

<P><STRONG><a name="[67]"></a>HAL_ADC_MspInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l0xx_hal_adc.o(i.HAL_ADC_MspInit))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[63]"></a>HAL_ADC_PollForConversion</STRONG> (Thumb, 192 bytes, Stack size 24 bytes, stm32l0xx_hal_adc.o(i.HAL_ADC_PollForConversion))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_ADC_PollForConversion
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Power_Voltage
</UL>

<P><STRONG><a name="[61]"></a>HAL_ADC_Start</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, stm32l0xx_hal_adc.o(i.HAL_ADC_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_ADC_Start &rArr; ADC_Enable &rArr; ADC_DelayMicroSecond &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Power_Voltage
</UL>

<P><STRONG><a name="[65]"></a>HAL_ADC_Stop</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, stm32l0xx_hal_adc.o(i.HAL_ADC_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_ADC_Stop &rArr; ADC_Disable
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Disable
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConversionStop
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Power_Voltage
</UL>

<P><STRONG><a name="[68]"></a>HAL_Delay</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32l0xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;quick_hardware_test
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[5d]"></a>HAL_FLASHEx_DATAEEPROM_Program</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Program))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_FLASHEx_DATAEEPROM_Program &rArr; FLASH_WaitForLastOperation &rArr; FLASH_SetErrorCode
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteByte
</UL>

<P><STRONG><a name="[58]"></a>HAL_FLASHEx_DATAEEPROM_Unlock</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, stm32l0xx_hal_flash_ex.o(i.HAL_FLASHEx_DATAEEPROM_Unlock))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_Init
</UL>

<P><STRONG><a name="[87]"></a>HAL_GPIO_Init</STRONG> (Thumb, 326 bytes, Stack size 20 bytes, stm32l0xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_485_init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_init
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;net_serial_init
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_gpio_init
</UL>

<P><STRONG><a name="[b9]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32l0xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;quick_hardware_test
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_status_monitor
</UL>

<P><STRONG><a name="[c9]"></a>HAL_GPIO_TogglePin</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32l0xx_hal_gpio.o(i.HAL_GPIO_TogglePin))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[bc]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32l0xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_485_send_dat
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_485_init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;quick_hardware_test
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_write_data
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_write_command
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[4e]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32l0xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;schedule_commands
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_parsing
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_rotate
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;quick_hardware_test
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_status_monitor
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_diagnostic_info
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_PollForConversion
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Disable
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConversionStop
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[8a]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32l0xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[69]"></a>HAL_Init</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32l0xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[6a]"></a>HAL_InitTick</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, stm32l0xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_InitTick &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[6b]"></a>HAL_MspInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l0xx_hal.o(i.HAL_MspInit))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[ca]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32l0xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_485_init
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;net_serial_init
</UL>

<P><STRONG><a name="[6d]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32l0xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_485_init
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;net_serial_init
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[57]"></a>HAL_PWR_EnableBkUpAccess</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32l0xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess))
<BR><BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_Init
</UL>

<P><STRONG><a name="[6f]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 404 bytes, Stack size 24 bytes, stm32l0xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[70]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 428 bytes, Stack size 24 bytes, stm32l0xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_init
</UL>

<P><STRONG><a name="[73]"></a>HAL_RCC_GetHCLKFreq</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32l0xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;quick_hardware_test
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_error_check
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_diagnostic_info
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[72]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_diagnostic_info
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[74]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, stm32l0xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_diagnostic_info
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[71]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, stm32l0xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_common_ll_muluu
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;quick_hardware_test
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_diagnostic_info
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[76]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1286 bytes, Stack size 32 bytes, stm32l0xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_RCC_OscConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_init
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[110]"></a>HAL_RTCEx_BKUPRead</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPRead))
<BR><BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[112]"></a>HAL_RTCEx_BKUPWrite</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32l0xx_hal_rtc_ex.o(i.HAL_RTCEx_BKUPWrite))
<BR><BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[77]"></a>HAL_RTC_Init</STRONG> (Thumb, 198 bytes, Stack size 24 bytes, stm32l0xx_hal_rtc.o(i.HAL_RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RTC_Init &rArr; RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[78]"></a>HAL_RTC_MspInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l0xx_hal_rtc.o(i.HAL_RTC_MspInit))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[7b]"></a>HAL_RTC_SetDate</STRONG> (Thumb, 214 bytes, Stack size 24 bytes, stm32l0xx_hal_rtc.o(i.HAL_RTC_SetDate))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RTC_SetDate &rArr; RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ByteToBcd2
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time
</UL>

<P><STRONG><a name="[7d]"></a>HAL_RTC_SetTime</STRONG> (Thumb, 246 bytes, Stack size 24 bytes, stm32l0xx_hal_rtc.o(i.HAL_RTC_SetTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RTC_SetTime &rArr; RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_ByteToBcd2
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time
</UL>

<P><STRONG><a name="[7a]"></a>HAL_RTC_WaitForSynchro</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, stm32l0xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_RTC_WaitForSynchro
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[6c]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32l0xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[7e]"></a>HAL_UART_Init</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, stm32l0xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_UART_Init &rArr; UART_SetConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_AdvFeatureConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_485_init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;net_serial_init
</UL>

<P><STRONG><a name="[7f]"></a>HAL_UART_MspInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32l0xx_hal_uart.o(i.HAL_UART_MspInit))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[83]"></a>HAL_UART_Transmit</STRONG> (Thumb, 232 bytes, Stack size 48 bytes, stm32l0xx_hal_uart.o(i.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_485_send_dat
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;net_send_data
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;net_send_cmd
</UL>

<P><STRONG><a name="[7c]"></a>RTC_ByteToBcd2</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32l0xx_hal_rtc.o(i.RTC_ByteToBcd2))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
</UL>

<P><STRONG><a name="[79]"></a>RTC_EnterInitMode</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32l0xx_hal_rtc.o(i.RTC_EnterInitMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, board.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SysTick_Handler &rArr; rt_tick_increase &rArr; rt_timer_check &rArr; rt_timer_start &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dev_delay
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_increase
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_leave
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_enter
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[8d]"></a>SystemCoreClockUpdate</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, system_stm32l0xx.o(i.SystemCoreClockUpdate))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SystemCoreClockUpdate &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[29]"></a>SystemInit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, system_stm32l0xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(.text)
</UL>
<P><STRONG><a name="[81]"></a>UART_AdvFeatureConfig</STRONG> (Thumb, 202 bytes, Stack size 8 bytes, stm32l0xx_hal_uart.o(i.UART_AdvFeatureConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_AdvFeatureConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[82]"></a>UART_CheckIdleState</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, stm32l0xx_hal_uart.o(i.UART_CheckIdleState))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = UART_CheckIdleState &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[80]"></a>UART_SetConfig</STRONG> (Thumb, 576 bytes, Stack size 24 bytes, stm32l0xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = UART_SetConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_common_switch8
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[84]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 164 bytes, Stack size 40 bytes, stm32l0xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
</UL>

<P><STRONG><a name="[24]"></a>USART1_IRQHandler</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, net.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART1_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>USART2_IRQHandler</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, uart.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART2_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32l072xx.o(RESET)
</UL>
<P><STRONG><a name="[8f]"></a>__0printf</STRONG> (Thumb, 24 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[12b]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[62]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_parsing
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Power_Voltage
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;quick_hardware_test
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_status_monitor
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_error_check
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_diagnostic_info
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[12c]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[12d]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[91]"></a>__0snprintf</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[12e]"></a>__1snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[ad]"></a>__2snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate1
</UL>

<P><STRONG><a name="[12f]"></a>__c89snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[130]"></a>snprintf</STRONG> (Thumb, 0 bytes, Stack size 40 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[92]"></a>__0sprintf</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[131]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[a7]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_battery_voltage
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate1
</UL>

<P><STRONG><a name="[132]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[133]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[45]"></a>__ARM_clz</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, depilogue.o(i.__ARM_clz), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[75]"></a>__ARM_common_ll_muluu</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, stm32l0xx_hal_rcc.o(i.__ARM_common_ll_muluu))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __ARM_common_ll_muluu
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
</UL>

<P><STRONG><a name="[8e]"></a>__ARM_common_switch8</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, pc_protocol.o(i.__ARM_common_switch8))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_common_switch8
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pc_protocol_parsing
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_485_init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[96]"></a>__rt_ffs</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, kservice.o(i.__rt_ffs))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_highest_priority_thread
</UL>

<P><STRONG><a name="[134]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[135]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[136]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[a0]"></a>bsp_board_init</STRONG> (Thumb, 54 bytes, Stack size 32 bytes, bsp_board.o(i.bsp_board_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = bsp_board_init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[a3]"></a>debug_init</STRONG> (Thumb, 102 bytes, Stack size 40 bytes, debug.o(i.debug_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = debug_init &rArr; HAL_UART_Init &rArr; UART_SetConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[a4]"></a>delay_us</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, board.o(i.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = delay_us &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[a5]"></a>display_battery_voltage</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, lcd12864.o(i.display_battery_voltage))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = display_battery_voltage &rArr; lcd_show_string &rArr; get_font_info &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_show_string
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Power_Voltage
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate
</UL>

<P><STRONG><a name="[a9]"></a>display_flow_rate</STRONG> (Thumb, 118 bytes, Stack size 40 bytes, lcd12864.o(i.display_flow_rate))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = display_flow_rate &rArr; display_battery_voltage &rArr; lcd_show_string &rArr; get_font_info &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_show_string
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_refresh
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_battery_voltage
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_rotate
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[ae]"></a>display_rotate</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, lcd12864.o(i.display_rotate))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = display_rotate &rArr; display_flow_rate1 &rArr; lcd_show_string &rArr; get_font_info &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate1
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[d0]"></a>flow_meter_protocol_parsing</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, flow_meter_protocol.o(i.flow_meter_protocol_parsing))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_parsing
</UL>

<P><STRONG><a name="[2b]"></a>fputc</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, debug.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = fputc &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[af]"></a>get_font_info</STRONG> (Thumb, 198 bytes, Stack size 48 bytes, lcd_font.o(i.get_font_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = get_font_info &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_font_size
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_font_type
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_show_string
</UL>

<P><STRONG><a name="[b0]"></a>get_font_size</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, lcd_font.o(i.get_font_size))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_font_info
</UL>

<P><STRONG><a name="[b1]"></a>get_font_type</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lcd_font.o(i.get_font_type))
<BR><BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_show_string
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_font_info
</UL>

<P><STRONG><a name="[b4]"></a>get_water_value</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, daosheng_protocol.o(i.get_water_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = get_water_value &rArr; schedule_commands &rArr; serial_485_send_dat &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;schedule_commands
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_parsing
</UL>

<P><STRONG><a name="[be]"></a>lcd_clear</STRONG> (Thumb, 42 bytes, Stack size 12 bytes, lcd12864.o(i.lcd_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = lcd_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[bb]"></a>lcd_init</STRONG> (Thumb, 170 bytes, Stack size 8 bytes, lcd12864.o(i.lcd_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = lcd_init &rArr; lcd_gpio_init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_clear
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_refresh
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_write_command
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_gpio_init
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[aa]"></a>lcd_refresh</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, lcd12864.o(i.lcd_refresh))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = lcd_refresh &rArr; lcd_write_data
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_write_data
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_write_command
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate1
</UL>

<P><STRONG><a name="[a8]"></a>lcd_show_string</STRONG> (Thumb, 168 bytes, Stack size 56 bytes, lcd12864.o(i.lcd_show_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = lcd_show_string &rArr; get_font_info &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_font_type
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_font_info
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_show_char
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_battery_voltage
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate1
</UL>

<P><STRONG><a name="[c2]"></a>$Super$$main</STRONG> (Thumb, 224 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = $Super$$main &rArr; protocol_parsing &rArr; pc_protocol_parsing &rArr; serial_485_send_dat &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_485_init
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_config
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_parsing
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_clear
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_rotate
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_flow_rate
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;debug_init
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_board_init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_TogglePin
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Power_Voltage
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_Init
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Voltage_Init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;quick_hardware_test
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_status_monitor
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_error_check
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hardware_diagnostic_info
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_thread_entry
</UL>

<P><STRONG><a name="[30]"></a>main_thread_entry</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, components.o(i.main_thread_entry))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = main_thread_entry &rArr; $Super$$main &rArr; protocol_parsing &rArr; pc_protocol_parsing &rArr; serial_485_send_dat &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>
<BR>[Address Reference Count : 1]<UL><LI> components.o(i.rt_application_init)
</UL>
<P><STRONG><a name="[cc]"></a>mbus_crc16</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, daosheng_protocol.o(i.mbus_crc16))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = mbus_crc16
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pc_protocol_parsing
</UL>

<P><STRONG><a name="[a2]"></a>modbus_crc16</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, modbus_rtu.o(i.modbus_crc16))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = modbus_crc16
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;build_read_command
</UL>

<P><STRONG><a name="[cb]"></a>pc_protocol_parsing</STRONG> (Thumb, 554 bytes, Stack size 40 bytes, pc_protocol.o(i.pc_protocol_parsing))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = pc_protocol_parsing &rArr; serial_485_send_dat &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_common_switch8
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_485_send_dat
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mbus_crc16
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_parsing
</UL>

<P><STRONG><a name="[c8]"></a>protocol_parsing</STRONG> (Thumb, 200 bytes, Stack size 128 bytes, protocol_common.o(i.protocol_parsing))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = protocol_parsing &rArr; pc_protocol_parsing &rArr; serial_485_send_dat &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;water_meter_protocol_parsing
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pc_protocol_parsing
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_water_value
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flow_meter_protocol_parsing
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_serial_port
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[c4]"></a>read_config</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, user_config.o(i.read_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = read_config &rArr; EEPROM_WriteBuffer &rArr; EEPROM_WriteByte &rArr; HAL_FLASHEx_DATAEEPROM_Program &rArr; FLASH_WaitForLastOperation &rArr; FLASH_SetErrorCode
</UL>
<BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteBuffer
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_ReadBuffer
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[d2]"></a>rt_application_init</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, components.o(i.rt_application_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = rt_application_init &rArr; rt_thread_create &rArr; _rt_thread_init &rArr; rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_create
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[9a]"></a>rt_assert_handler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, kservice.o(i.rt_assert_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;plug_holes
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_control
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_type
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_malloc
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_init
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_is_systemobject
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_detach
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_delete
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_application_init
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_heap_init
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_stop
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_init
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_detach
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timeout
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_thread_init
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_remove_thread
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_insert_thread
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_scheduler_stack_check
</UL>

<P><STRONG><a name="[d5]"></a>rt_enter_critical</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, scheduler.o(i.rt_enter_critical))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_enter_critical
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
</UL>

<P><STRONG><a name="[d7]"></a>rt_exit_critical</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, scheduler.o(i.rt_exit_critical))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = rt_exit_critical &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
</UL>

<P><STRONG><a name="[d9]"></a>rt_free</STRONG> (Thumb, 196 bytes, Stack size 16 bytes, mem.o(i.rt_free))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = rt_free &rArr; rt_sem_take &rArr; rt_ipc_list_suspend &rArr; rt_thread_suspend &rArr; rt_timer_stop &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;plug_holes
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_get_nest
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_delete
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
</UL>

<P><STRONG><a name="[df]"></a>rt_heap_begin_get</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, board.o(i.rt_heap_begin_get))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[de]"></a>rt_heap_end_get</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, board.o(i.rt_heap_end_get))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[dd]"></a>rt_hw_board_init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, board.o(i.rt_hw_board_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = rt_hw_board_init &rArr; system_clock_init &rArr; HAL_RCC_OscConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_init
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemCoreClockUpdate
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_heap_end_get
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_heap_begin_get
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_heap_init
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[36]"></a>rt_hw_hard_fault_exception</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, cpuport.o(i.rt_hw_hard_fault_exception))
<BR><BR>[Calls]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_hard_fault_exception
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_hard_fault_exception
</UL>

<P><STRONG><a name="[9e]"></a>rt_hw_stack_init</STRONG> (Thumb, 56 bytes, Stack size 20 bytes, cpuport.o(i.rt_hw_stack_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = rt_hw_stack_init
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_thread_init
</UL>

<P><STRONG><a name="[88]"></a>rt_interrupt_enter</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, irq.o(i.rt_interrupt_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_interrupt_enter
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[da]"></a>rt_interrupt_get_nest</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, irq.o(i.rt_interrupt_get_nest))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_interrupt_get_nest
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_malloc
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_heap_init
</UL>

<P><STRONG><a name="[8c]"></a>rt_interrupt_leave</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, irq.o(i.rt_interrupt_leave))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_interrupt_leave
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[e6]"></a>rt_malloc</STRONG> (Thumb, 370 bytes, Stack size 24 bytes, mem.o(i.rt_malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = rt_malloc &rArr; rt_sem_take &rArr; rt_ipc_list_suspend &rArr; rt_thread_suspend &rArr; rt_timer_stop &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_get_nest
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_create
</UL>

<P><STRONG><a name="[9d]"></a>rt_memset</STRONG> (Thumb, 70 bytes, Stack size 12 bytes, kservice.o(i.rt_memset))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = rt_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_thread_init
</UL>

<P><STRONG><a name="[e7]"></a>rt_object_allocate</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, object.o(i.rt_object_allocate))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = rt_object_allocate &rArr; rt_malloc &rArr; rt_sem_take &rArr; rt_ipc_list_suspend &rArr; rt_thread_suspend &rArr; rt_timer_stop &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_strncpy
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_memset
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_malloc
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_get_nest
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_information
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_insert_after
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_create
</UL>

<P><STRONG><a name="[eb]"></a>rt_object_delete</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, object.o(i.rt_object_delete))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = rt_object_delete &rArr; rt_free &rArr; rt_sem_take &rArr; rt_ipc_list_suspend &rArr; rt_thread_suspend &rArr; rt_timer_stop &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_create
</UL>

<P><STRONG><a name="[ed]"></a>rt_object_detach</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, object.o(i.rt_object_detach))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rt_object_detach &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_detach
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
</UL>

<P><STRONG><a name="[e8]"></a>rt_object_get_information</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, object.o(i.rt_object_get_information))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
</UL>

<P><STRONG><a name="[ee]"></a>rt_object_get_type</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, object.o(i.rt_object_get_type))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_control
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_stop
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_detach
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timeout
</UL>

<P><STRONG><a name="[ef]"></a>rt_object_init</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, object.o(i.rt_object_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_strncpy
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_enter_critical
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_information
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_insert_after
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_init
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_init
</UL>

<P><STRONG><a name="[f0]"></a>rt_object_is_systemobject</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, object.o(i.rt_object_is_systemobject))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rt_object_is_systemobject &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_detach
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
</UL>

<P><STRONG><a name="[d8]"></a>rt_schedule</STRONG> (Thumb, 208 bytes, Stack size 32 bytes, scheduler.o(i.rt_schedule))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_context_switch_interrupt
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_context_switch
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_remove_thread
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_insert_thread
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_scheduler_stack_check
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_highest_priority_thread
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_yield
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timeout
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
</UL>

<P><STRONG><a name="[f1]"></a>rt_schedule_insert_thread</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, scheduler.o(i.rt_schedule_insert_thread))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rt_schedule_insert_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timeout
</UL>

<P><STRONG><a name="[f2]"></a>rt_schedule_remove_thread</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, scheduler.o(i.rt_schedule_remove_thread))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_scheduler_start
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
</UL>

<P><STRONG><a name="[f5]"></a>rt_sem_init</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, ipc.o(i.rt_sem_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = rt_sem_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_heap_init
</UL>

<P><STRONG><a name="[dc]"></a>rt_sem_release</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, ipc.o(i.rt_sem_release))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = rt_sem_release &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_type
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_isempty
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_list_resume
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_malloc
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
</UL>

<P><STRONG><a name="[db]"></a>rt_sem_take</STRONG> (Thumb, 244 bytes, Stack size 32 bytes, ipc.o(i.rt_sem_take))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = rt_sem_take &rArr; rt_ipc_list_suspend &rArr; rt_thread_suspend &rArr; rt_timer_stop &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_control
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_type
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_list_suspend
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_get_nest
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_self
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_malloc
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
</UL>

<P><STRONG><a name="[113]"></a>rt_show_version</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, kservice.o(i.rt_show_version))
<BR><BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[fa]"></a>rt_sprintf</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, kservice.o(i.rt_sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = rt_sprintf &rArr; rt_vsprintf &rArr; rt_vsnprintf &rArr; print_number &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_vsprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_init
</UL>

<P><STRONG><a name="[10f]"></a>rt_strlen</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, kservice.o(i.rt_strlen))
<BR><BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_vsnprintf
</UL>

<P><STRONG><a name="[e9]"></a>rt_strncpy</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, kservice.o(i.rt_strncpy))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
</UL>

<P><STRONG><a name="[e0]"></a>rt_system_heap_init</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, mem.o(i.rt_system_heap_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = rt_system_heap_init &rArr; rt_sem_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_get_nest
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[115]"></a>rt_system_scheduler_init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, scheduler.o(i.rt_system_scheduler_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rt_system_scheduler_init
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[fc]"></a>rt_system_scheduler_start</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, scheduler.o(i.rt_system_scheduler_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = rt_system_scheduler_start &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_context_switch_to
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_remove_thread
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_highest_priority_thread
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[114]"></a>rt_system_timer_init</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, timer.o(i.rt_system_timer_init))
<BR><BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[116]"></a>rt_system_timer_thread_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, timer.o(i.rt_system_timer_thread_init))
<BR><BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[d3]"></a>rt_thread_create</STRONG> (Thumb, 68 bytes, Stack size 56 bytes, thread.o(i.rt_thread_create))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = rt_thread_create &rArr; _rt_thread_init &rArr; rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_malloc
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_delete
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_thread_init
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_application_init
</UL>

<P><STRONG><a name="[2e]"></a>rt_thread_exit</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, thread.o(i.rt_thread_exit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = rt_thread_exit &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_is_systemobject
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_detach
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_self
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_detach
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_insert_after
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_remove_thread
</UL>
<BR>[Address Reference Count : 1]<UL><LI> thread.o(i._rt_thread_init)
</UL>
<P><STRONG><a name="[100]"></a>rt_thread_idle_excute</STRONG> (Thumb, 154 bytes, Stack size 16 bytes, idle.o(i.rt_thread_idle_excute))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = rt_thread_idle_excute &rArr; rt_object_delete &rArr; rt_free &rArr; rt_sem_take &rArr; rt_ipc_list_suspend &rArr; rt_thread_suspend &rArr; rt_timer_stop &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_is_systemobject
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_detach
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_delete
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_interrupt_get_nest
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_exit_critical
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_enter_critical
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_has_defunct_thread
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_entry
</UL>

<P><STRONG><a name="[102]"></a>rt_thread_idle_init</STRONG> (Thumb, 70 bytes, Stack size 48 bytes, idle.o(i.rt_thread_idle_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = rt_thread_idle_init &rArr; rt_thread_init &rArr; _rt_thread_init &rArr; rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_init
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sprintf
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtthread_startup
</UL>

<P><STRONG><a name="[103]"></a>rt_thread_init</STRONG> (Thumb, 70 bytes, Stack size 56 bytes, thread.o(i.rt_thread_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = rt_thread_init &rArr; _rt_thread_init &rArr; rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_thread_init
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_init
</UL>

<P><STRONG><a name="[e2]"></a>rt_thread_resume</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, thread.o(i.rt_thread_resume))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = rt_thread_resume &rArr; rt_timer_stop &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_type
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_stop
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_remove
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_insert_thread
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_list_resume
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
</UL>

<P><STRONG><a name="[f7]"></a>rt_thread_self</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, thread.o(i.rt_thread_self))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_startup
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_increase
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
</UL>

<P><STRONG><a name="[d4]"></a>rt_thread_startup</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, thread.o(i.rt_thread_startup))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = rt_thread_startup &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_type
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_self
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_init
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_application_init
</UL>

<P><STRONG><a name="[e4]"></a>rt_thread_suspend</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, thread.o(i.rt_thread_suspend))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = rt_thread_suspend &rArr; rt_timer_stop &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_type
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_self
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_stop
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_remove_thread
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_list_suspend
</UL>

<P><STRONG><a name="[2f]"></a>rt_thread_timeout</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, thread.o(i.rt_thread_timeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = rt_thread_timeout &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_type
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_remove
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule_insert_thread
</UL>
<BR>[Address Reference Count : 1]<UL><LI> thread.o(i._rt_thread_init)
</UL>
<P><STRONG><a name="[106]"></a>rt_thread_yield</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, thread.o(i.rt_thread_yield))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = rt_thread_yield &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_increase
</UL>

<P><STRONG><a name="[108]"></a>rt_tick_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, clock.o(i.rt_tick_get))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
</UL>

<P><STRONG><a name="[89]"></a>rt_tick_increase</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, clock.o(i.rt_tick_increase))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = rt_tick_increase &rArr; rt_timer_check &rArr; rt_timer_start &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_yield
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_self
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[107]"></a>rt_timer_check</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, timer.o(i.rt_timer_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = rt_timer_check &rArr; rt_timer_start &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_get
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_isempty
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_timer_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_increase
</UL>

<P><STRONG><a name="[f8]"></a>rt_timer_control</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, timer.o(i.rt_timer_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = rt_timer_control &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_type
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
</UL>

<P><STRONG><a name="[fe]"></a>rt_timer_detach</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, timer.o(i.rt_timer_detach))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = rt_timer_detach &rArr; rt_object_detach &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_type
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_is_systemobject
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_detach
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_timer_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
</UL>

<P><STRONG><a name="[9f]"></a>rt_timer_init</STRONG> (Thumb, 54 bytes, Stack size 40 bytes, timer.o(i.rt_timer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_timer_init
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_thread_init
</UL>

<P><STRONG><a name="[f9]"></a>rt_timer_start</STRONG> (Thumb, 262 bytes, Stack size 32 bytes, timer.o(i.rt_timer_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = rt_timer_start &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_type
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_tick_get
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_insert_after
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_timer_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
</UL>

<P><STRONG><a name="[105]"></a>rt_timer_stop</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, timer.o(i.rt_timer_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = rt_timer_stop &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_get_type
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_enable
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_rt_timer_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
</UL>

<P><STRONG><a name="[10d]"></a>rt_vsnprintf</STRONG> (Thumb, 658 bytes, Stack size 56 bytes, kservice.o(i.rt_vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = rt_vsnprintf &rArr; print_number &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_strlen
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;skip_atoi
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_number
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_vsprintf
</UL>

<P><STRONG><a name="[fb]"></a>rt_vsprintf</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, kservice.o(i.rt_vsprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = rt_vsprintf &rArr; rt_vsnprintf &rArr; print_number &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sprintf
</UL>

<P><STRONG><a name="[c6]"></a>rtc_init</STRONG> (Thumb, 164 bytes, Stack size 112 bytes, rtc.o(i.rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = rtc_init &rArr; HAL_RCC_OscConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_BKUPWrite
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTCEx_BKUPRead
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWR_EnableBkUpAccess
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_set_time
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[111]"></a>rtc_set_time</STRONG> (Thumb, 60 bytes, Stack size 40 bytes, rtc.o(i.rtc_set_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = rtc_set_time &rArr; HAL_RTC_SetTime &rArr; RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetTime
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_SetDate
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[4c]"></a>rtthread_startup</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, components.o(i.rtthread_startup))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = rtthread_startup &rArr; rt_thread_idle_init &rArr; rt_thread_init &rArr; _rt_thread_init &rArr; rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_timer_thread_init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_timer_init
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_scheduler_start
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_scheduler_init
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_show_version
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_application_init
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c5]"></a>serial_485_init</STRONG> (Thumb, 324 bytes, Stack size 56 bytes, uart.o(i.serial_485_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = serial_485_init &rArr; HAL_UART_Init &rArr; UART_SetConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_common_switch8
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[cd]"></a>serial_485_send_dat</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, uart.o(i.serial_485_send_dat))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = serial_485_send_dat &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;schedule_commands
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pc_protocol_parsing
</UL>

<P><STRONG><a name="[c3]"></a>system_clock_init</STRONG> (Thumb, 116 bytes, Stack size 88 bytes, system.o(i.system_clock_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = system_clock_init &rArr; HAL_RCC_OscConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_board_init
</UL>

<P><STRONG><a name="[d1]"></a>water_meter_protocol_parsing</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, water_meter_protocol.o(i.water_meter_protocol_parsing))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_parsing
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[85]"></a>MX_GPIO_Init</STRONG> (Thumb, 54 bytes, Stack size 32 bytes, main.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[b6]"></a>hardware_diagnostic_info</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, main.o(i.hardware_diagnostic_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = hardware_diagnostic_info &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[b7]"></a>hardware_error_check</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, main.o(i.hardware_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = hardware_error_check &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[b8]"></a>hardware_status_monitor</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, main.o(i.hardware_status_monitor))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = hardware_status_monitor &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[c7]"></a>quick_hardware_test</STRONG> (Thumb, 242 bytes, Stack size 16 bytes, main.o(i.quick_hardware_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = quick_hardware_test &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[b3]"></a>get_serial_port</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, protocol_common.o(i.get_serial_port))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_serial_port
</UL>
<BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;protocol_parsing
</UL>

<P><STRONG><a name="[a1]"></a>build_read_command</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, daosheng_protocol.o(i.build_read_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = build_read_command &rArr; modbus_crc16
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;modbus_crc16
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;schedule_commands
</UL>

<P><STRONG><a name="[b5]"></a>schedule_commands</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, daosheng_protocol.o(i.schedule_commands))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = schedule_commands &rArr; serial_485_send_dat &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_485_send_dat
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;build_read_command
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_water_value
</UL>

<P><STRONG><a name="[ab]"></a>display_flow_rate1</STRONG> (Thumb, 150 bytes, Stack size 144 bytes, lcd12864.o(i.display_flow_rate1))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = display_flow_rate1 &rArr; lcd_show_string &rArr; get_font_info &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_show_string
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Power_Voltage
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_refresh
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_rotate
</UL>

<P><STRONG><a name="[c1]"></a>lcd_draw_pixel</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, lcd12864.o(i.lcd_draw_pixel))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lcd_draw_pixel
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_show_char
</UL>

<P><STRONG><a name="[ba]"></a>lcd_gpio_init</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, lcd12864.o(i.lcd_gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = lcd_gpio_init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
</UL>

<P><STRONG><a name="[c0]"></a>lcd_show_char</STRONG> (Thumb, 128 bytes, Stack size 48 bytes, lcd12864.o(i.lcd_show_char))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = lcd_show_char &rArr; lcd_draw_pixel
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_draw_pixel
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_show_string
</UL>

<P><STRONG><a name="[bd]"></a>lcd_write_command</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, lcd12864.o(i.lcd_write_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lcd_write_command
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_refresh
</UL>

<P><STRONG><a name="[bf]"></a>lcd_write_data</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, lcd12864.o(i.lcd_write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lcd_write_data
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_refresh
</UL>

<P><STRONG><a name="[1]"></a>net_clean</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, net.o(i.net_clean))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = net_clean
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> net.o(.data)
</UL>
<P><STRONG><a name="[3]"></a>net_send_cmd</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, net.o(i.net_send_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = net_send_cmd &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> net.o(.data)
</UL>
<P><STRONG><a name="[2]"></a>net_send_data</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, net.o(i.net_send_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = net_send_data &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> net.o(.data)
</UL>
<P><STRONG><a name="[0]"></a>net_serial_init</STRONG> (Thumb, 164 bytes, Stack size 48 bytes, net.o(i.net_serial_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = net_serial_init &rArr; HAL_UART_Init &rArr; UART_SetConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> net.o(.data)
</UL>
<P><STRONG><a name="[4d]"></a>ADC_ConversionStop</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, stm32l0xx_hal_adc.o(i.ADC_ConversionStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_ConversionStop
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop
</UL>

<P><STRONG><a name="[4f]"></a>ADC_DelayMicroSecond</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, stm32l0xx_hal_adc.o(i.ADC_DelayMicroSecond))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ADC_DelayMicroSecond &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Enable
</UL>

<P><STRONG><a name="[51]"></a>ADC_Disable</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, stm32l0xx_hal_adc.o(i.ADC_Disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ADC_Disable
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop
</UL>

<P><STRONG><a name="[52]"></a>ADC_Enable</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, stm32l0xx_hal_adc.o(i.ADC_Enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = ADC_Enable &rArr; ADC_DelayMicroSecond &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DelayMicroSecond
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start
</UL>

<P><STRONG><a name="[6e]"></a>__NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, stm32l0xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[5f]"></a>FLASH_SetErrorCode</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, stm32l0xx_hal_flash.o(i.FLASH_SetErrorCode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FLASH_SetErrorCode
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>

<P><STRONG><a name="[8b]"></a>dev_delay</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, board.o(i.dev_delay))
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[101]"></a>_has_defunct_thread</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, idle.o(i._has_defunct_thread))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
</UL>

<P><STRONG><a name="[31]"></a>rt_thread_idle_entry</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, idle.o(i.rt_thread_idle_entry))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = rt_thread_idle_entry &rArr; rt_thread_idle_excute &rArr; rt_object_delete &rArr; rt_free &rArr; rt_sem_take &rArr; rt_ipc_list_suspend &rArr; rt_thread_suspend &rArr; rt_timer_stop &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_idle_excute
</UL>
<BR>[Address Reference Count : 1]<UL><LI> idle.o(i.rt_thread_idle_init)
</UL>
<P><STRONG><a name="[e1]"></a>rt_ipc_list_resume</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, ipc.o(i.rt_ipc_list_resume))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = rt_ipc_list_resume &rArr; rt_thread_resume &rArr; rt_timer_stop &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
</UL>

<P><STRONG><a name="[e3]"></a>rt_ipc_list_suspend</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, ipc.o(i.rt_ipc_list_suspend))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = rt_ipc_list_suspend &rArr; rt_thread_suspend &rArr; rt_timer_stop &rArr; rt_object_get_type &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_suspend
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_list_insert_before
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_take
</UL>

<P><STRONG><a name="[e5]"></a>rt_list_insert_before</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, ipc.o(i.rt_list_insert_before))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_ipc_list_suspend
</UL>

<P><STRONG><a name="[f6]"></a>rt_list_isempty</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, ipc.o(i.rt_list_isempty))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_sem_release
</UL>

<P><STRONG><a name="[cf]"></a>print_number</STRONG> (Thumb, 318 bytes, Stack size 72 bytes, kservice.o(i.print_number))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = print_number &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_vsnprintf
</UL>

<P><STRONG><a name="[10e]"></a>skip_atoi</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, kservice.o(i.skip_atoi))
<BR><BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_vsnprintf
</UL>

<P><STRONG><a name="[ce]"></a>plug_holes</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, mem.o(i.plug_holes))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = plug_holes &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_free
</UL>

<P><STRONG><a name="[ea]"></a>rt_list_insert_after</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, object.o(i.rt_list_insert_after))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_allocate
</UL>

<P><STRONG><a name="[ec]"></a>rt_list_remove</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, object.o(i.rt_list_remove))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_detach
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_object_delete
</UL>

<P><STRONG><a name="[95]"></a>_get_highest_priority_thread</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, scheduler.o(i._get_highest_priority_thread))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_highest_priority_thread
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ffs
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_system_scheduler_start
</UL>

<P><STRONG><a name="[99]"></a>_rt_scheduler_stack_check</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, scheduler.o(i._rt_scheduler_stack_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _rt_scheduler_stack_check &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_schedule
</UL>

<P><STRONG><a name="[9c]"></a>_rt_thread_init</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, thread.o(i._rt_thread_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = _rt_thread_init &rArr; rt_timer_init &rArr; rt_object_init &rArr; rt_exit_critical &rArr; rt_schedule &rArr; rt_schedule_remove_thread &rArr; rt_assert_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_memset
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_assert_handler
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_init
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_hw_stack_init
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_init
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_create
</UL>

<P><STRONG><a name="[ff]"></a>rt_list_insert_after</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, thread.o(i.rt_list_insert_after))
<BR><BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_exit
</UL>

<P><STRONG><a name="[104]"></a>rt_list_remove</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, thread.o(i.rt_list_remove))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_resume
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_thread_timeout
</UL>

<P><STRONG><a name="[10b]"></a>_rt_timer_init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, timer.o(i._rt_timer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _rt_timer_init
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_init
</UL>

<P><STRONG><a name="[109]"></a>_rt_timer_remove</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, timer.o(i._rt_timer_remove))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _rt_timer_remove
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_stop
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_detach
</UL>

<P><STRONG><a name="[10c]"></a>rt_list_insert_after</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(i.rt_list_insert_after))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_start
</UL>

<P><STRONG><a name="[10a]"></a>rt_list_isempty</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, timer.o(i.rt_list_isempty))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rt_timer_check
</UL>

<P><STRONG><a name="[93]"></a>_fp_digits</STRONG> (Thumb, 344 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[90]"></a>_printf_core</STRONG> (Thumb, 1760 bytes, Stack size 128 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0snprintf
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[98]"></a>_printf_post_padding</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[97]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 40 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2c]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0snprintf)
</UL>
<P><STRONG><a name="[2d]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3>
<P><STRONG><a name="[35]"></a>__arm_fini_</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown_fini
</UL>
<HR></body></html>
