/*
 *  CMSIS Pack Debug Access Sequence Log
 *  File        : E:\project\source-application\user\driver_Sequences_0032.log
 *  Created     : 14:23:30 (29/08/2025)
 *  Device      : STM32L072CBTx
 *  PDSC File   : D:/software/Keil/Packs/Keil/STM32L0xx_DFP/3.0.0/Keil.STM32L0xx_DFP.pdsc
 *  Config File : E:\project\source-application\user\DebugConfig\driver_STM32L072CBTx.dbgconf
 *
 */

[14:23:30.367]  **********  Sequence "DebugDeviceUnlock"  (Context="Connect", Pname="", info="")
[14:23:30.367]  
[14:23:30.396]  <debugvars>
[14:23:30.425]    // Pre-defined
[14:23:30.445]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:23:30.474]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:23:30.475]    __dp=0x00000000
[14:23:30.475]    __ap=0x00000000
[14:23:30.477]    __traceout=0x00000000      (Trace Disabled)
[14:23:30.477]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:23:30.477]    __FlashAddr=0x00000000
[14:23:30.477]    __FlashLen=0x00000000
[14:23:30.478]    __FlashArg=0x00000000
[14:23:30.478]    __FlashOp=0x00000000
[14:23:30.478]    __Result=0x00000000
[14:23:30.479]    
[14:23:30.479]    // User-defined
[14:23:30.479]    DbgMCU_CR=0x00000007
[14:23:30.479]    DbgMCU_APB1_Fz=0x00000000
[14:23:30.479]    DbgMCU_APB2_Fz=0x00000000
[14:23:30.480]    DoOptionByteLoading=0x00000000
[14:23:30.480]  </debugvars>
[14:23:30.480]  
[14:23:30.481]  <sequence name="DebugDeviceUnlock" Pname="" disable="false" info="">
[14:23:30.481]    <block atomic="false" info="">
[14:23:30.482]      Sequence("CheckID");
[14:23:30.482]        <sequence name="CheckID" Pname="" disable="false" info="">
[14:23:30.482]          <block atomic="false" info="">
[14:23:30.483]            __var pidr1 = 0;
[14:23:30.483]              // -> [pidr1 <= 0x00000000]
[14:23:30.483]            __var pidr2 = 0;
[14:23:30.484]              // -> [pidr2 <= 0x00000000]
[14:23:30.484]            __var jep106id = 0;
[14:23:30.484]              // -> [jep106id <= 0x00000000]
[14:23:30.484]            __var ROMTableBase = 0;
[14:23:30.485]              // -> [ROMTableBase <= 0x00000000]
[14:23:30.485]            __ap = 0;      // AHB-AP
[14:23:30.485]              // -> [__ap <= 0x00000000]
[14:23:30.486]            ROMTableBase = ReadAP(0xF8) & ~0x3;
[14:23:30.491]              // -> [ReadAP(0x000000F8) => 0xF0000003]   (__dp=0x00000000, __ap=0x00000000)
[14:23:30.491]              // -> [ROMTableBase <= 0xF0000000]
[14:23:30.491]            pidr1 = Read32(ROMTableBase + 0x0FE4);
[14:23:30.499]              // -> [Read32(0xF0000FE4) => 0x00000004]   (__dp=0x00000000, __ap=0x00000000)
[14:23:30.500]              // -> [pidr1 <= 0x00000004]
[14:23:30.501]            pidr2 = Read32(ROMTableBase + 0x0FE8);
[14:23:30.506]              // -> [Read32(0xF0000FE8) => 0x0000000A]   (__dp=0x00000000, __ap=0x00000000)
[14:23:30.506]              // -> [pidr2 <= 0x0000000A]
[14:23:30.506]            jep106id = ((pidr2 & 0x7) << 4 ) | ((pidr1 >> 4) & 0xF);
[14:23:30.507]              // -> [jep106id <= 0x00000020]
[14:23:30.507]          </block>
[14:23:30.507]          <control if="jep106id != 0x20" while="" timeout="0" info="">
[14:23:30.508]            // if-block "jep106id != 0x20"
[14:23:30.508]              // =>  FALSE
[14:23:30.508]            // skip if-block "jep106id != 0x20"
[14:23:30.509]          </control>
[14:23:30.509]        </sequence>
[14:23:30.511]    </block>
[14:23:30.511]  </sequence>
[14:23:30.511]  
[14:23:30.613]  **********  Sequence "DebugCoreStart"  (Context="Target Access", Pname="", info="")
[14:23:30.613]  
[14:23:30.615]  <debugvars>
[14:23:30.615]    // Pre-defined
[14:23:30.616]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:23:30.616]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:23:30.616]    __dp=0x00000000
[14:23:30.616]    __ap=0x00000000
[14:23:30.617]    __traceout=0x00000000      (Trace Disabled)
[14:23:30.617]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:23:30.617]    __FlashAddr=0x00000000
[14:23:30.618]    __FlashLen=0x00000000
[14:23:30.618]    __FlashArg=0x00000000
[14:23:30.619]    __FlashOp=0x00000000
[14:23:30.619]    __Result=0x00000000
[14:23:30.619]    
[14:23:30.619]    // User-defined
[14:23:30.619]    DbgMCU_CR=0x00000007
[14:23:30.621]    DbgMCU_APB1_Fz=0x00000000
[14:23:30.621]    DbgMCU_APB2_Fz=0x00000000
[14:23:30.621]    DoOptionByteLoading=0x00000000
[14:23:30.621]  </debugvars>
[14:23:30.621]  
[14:23:30.621]  <sequence name="DebugCoreStart" Pname="" disable="false" info="">
[14:23:30.623]    <block atomic="false" info="">
[14:23:30.623]      Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
[14:23:30.629]        // -> [Write32(0xE000EDF0, 0xA05F0001)]   (__dp=0x00000000, __ap=0x00000000)
[14:23:30.629]    </block>
[14:23:30.629]    <block atomic="false" info="DbgMCU registers">
[14:23:30.629]      Write32(0x40021034, Read32(0x40021034) | 0x00400000);                   // Set RCC_APB2ENR.DBGEN
[14:23:30.636]        // -> [Read32(0x40021034) => 0x00004200]   (__dp=0x00000000, __ap=0x00000000)
[14:23:30.641]        // -> [Write32(0x40021034, 0x00404200)]   (__dp=0x00000000, __ap=0x00000000)
[14:23:30.642]      Write32(0x40015804, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
[14:23:30.648]        // -> [Write32(0x40015804, 0x00000007)]   (__dp=0x00000000, __ap=0x00000000)
[14:23:30.650]      Write32(0x40015808, DbgMCU_APB1_Fz);                                    // DBGMCU_APB1_FZ: Configure APB1 Peripheral Freeze Behavior
[14:23:30.657]        // -> [Write32(0x40015808, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:23:30.659]      Write32(0x4001580C, DbgMCU_APB2_Fz);                                    // DBGMCU_APB1_FZ: Configure APB2 Peripheral Freeze Behavior
[14:23:30.665]        // -> [Write32(0x4001580C, 0x00000000)]   (__dp=0x00000000, __ap=0x00000000)
[14:23:30.666]    </block>
[14:23:30.667]  </sequence>
[14:23:30.667]  
[14:23:47.365]  **********  Sequence "DebugPortStop"  (Context="Disconnect", Pname="", info="")
[14:23:47.365]  
[14:23:47.367]  <debugvars>
[14:23:47.368]    // Pre-defined
[14:23:47.368]    __protocol=0x00010002      (Protocol="SWD", SWJ-DP="True")
[14:23:47.368]    __connection=0x00000202    (Connection Type="Flash", Reset Type="System Reset")
[14:23:47.370]    __dp=0x00000000
[14:23:47.370]    __ap=0x00000000
[14:23:47.371]    __traceout=0x00000000      (Trace Disabled)
[14:23:47.371]    __errorcontrol=0x00000000  (Skip Errors="False")
[14:23:47.371]    __FlashAddr=0x00000000
[14:23:47.372]    __FlashLen=0x00000000
[14:23:47.372]    __FlashArg=0x00000000
[14:23:47.372]    __FlashOp=0x00000000
[14:23:47.373]    __Result=0x00000000
[14:23:47.373]    
[14:23:47.373]    // User-defined
[14:23:47.373]    DbgMCU_CR=0x00000007
[14:23:47.374]    DbgMCU_APB1_Fz=0x00000000
[14:23:47.374]    DbgMCU_APB2_Fz=0x00000000
[14:23:47.374]    DoOptionByteLoading=0x00000000
[14:23:47.375]  </debugvars>
[14:23:47.375]  
[14:23:47.375]  <sequence name="DebugPortStop" Pname="" disable="false" info="">
[14:23:47.376]    <block atomic="false" info="">
[14:23:47.377]      __var connectionFlash = ( __connection & 0xF ) == 2 ;
[14:23:47.377]        // -> [connectionFlash <= 0x00000001]
[14:23:47.377]      __var FLASH_BASE = 0x40022000 ;
[14:23:47.378]        // -> [FLASH_BASE <= 0x40022000]
[14:23:47.378]      __var FLASH_CR = FLASH_BASE + 0x04 ;
[14:23:47.378]        // -> [FLASH_CR <= 0x40022004]
[14:23:47.378]      __var OBL_LAUNCH_BIT = ( 1 << 18 ) ;
[14:23:47.380]        // -> [OBL_LAUNCH_BIT <= 0x00040000]
[14:23:47.380]      __var LOCK_BIT = ( 1 << 0 ) ;
[14:23:47.381]        // -> [LOCK_BIT <= 0x00000001]
[14:23:47.381]      __var OPTLOCK_BIT = ( 1 << 2 ) ;
[14:23:47.381]        // -> [OPTLOCK_BIT <= 0x00000004]
[14:23:47.381]      __var FLASH_KEYR = FLASH_BASE + 0x0C ;
[14:23:47.382]        // -> [FLASH_KEYR <= 0x4002200C]
[14:23:47.382]      __var FLASH_KEY1 = 0x89ABCDEF ;
[14:23:47.382]        // -> [FLASH_KEY1 <= 0x89ABCDEF]
[14:23:47.383]      __var FLASH_KEY2 = 0x02030405 ;
[14:23:47.383]        // -> [FLASH_KEY2 <= 0x02030405]
[14:23:47.383]      __var FLASH_OPTKEYR = FLASH_BASE + 0x14 ;
[14:23:47.383]        // -> [FLASH_OPTKEYR <= 0x40022014]
[14:23:47.383]      __var FLASH_OPTKEY1 = 0xFBEAD9C8 ;
[14:23:47.384]        // -> [FLASH_OPTKEY1 <= 0xFBEAD9C8]
[14:23:47.384]      __var FLASH_OPTKEY2 = 0x24252627 ;
[14:23:47.384]        // -> [FLASH_OPTKEY2 <= 0x24252627]
[14:23:47.385]      __var FLASH_CR_Value = 0 ;
[14:23:47.385]        // -> [FLASH_CR_Value <= 0x00000000]
[14:23:47.385]      __var DoDebugPortStop = 1 ;
[14:23:47.385]        // -> [DoDebugPortStop <= 0x00000001]
[14:23:47.386]      __var DP_CTRL_STAT = 0x4 ;
[14:23:47.386]        // -> [DP_CTRL_STAT <= 0x00000004]
[14:23:47.386]      __var DP_SELECT = 0x8 ;
[14:23:47.387]        // -> [DP_SELECT <= 0x00000008]
[14:23:47.387]    </block>
[14:23:47.387]    <control if="connectionFlash && DoOptionByteLoading" while="" timeout="0" info="">
[14:23:47.388]      // if-block "connectionFlash && DoOptionByteLoading"
[14:23:47.389]        // =>  FALSE
[14:23:47.390]      // skip if-block "connectionFlash && DoOptionByteLoading"
[14:23:47.390]    </control>
[14:23:47.390]    <control if="DoDebugPortStop" while="" timeout="0" info="">
[14:23:47.390]      // if-block "DoDebugPortStop"
[14:23:47.391]        // =>  TRUE
[14:23:47.391]      <block atomic="false" info="">
[14:23:47.392]        WriteDP(DP_SELECT, 0x00000000);
[14:23:47.399]  
[14:23:47.399]  !!! E310 : Debug access failed - cannot write value 0x00000000 to DP register 0x08
[14:23:47.399]  
[14:23:47.403]          // -> [WriteDP(0x00000008, 0x00000000)]   (__dp=0x00000000)
[14:23:47.403]      </block>
[14:23:47.405]      // end if-block "DoDebugPortStop"
[14:23:47.406]    </control>
[14:23:47.406]  </sequence>
[14:23:47.406]  
