/*
串口485接口协议-公共部分（自动识别协议及跳转），设备唤醒后需要先监听485是否有数据，需要等总线空闲的时候才发起请求（同一个总线可能挂在其他厂家采集器，防止干扰）
*/
#include "protocol_common.h"
#include "uart.h"
#include "daoSheng_protocol.h"
#include "flow_meter_protocol.h"
#include "water_meter_protocol.h"
#include "pc_protocol.h"
#include "user_config.h"
#include <stdio.h>
#include "rt_compat.h"
#include <string.h>
static SERIAL serial_data;
 uint32_t CR_time;

static uint8_t get_serial_port(void)
{
  if(serial_485.rx_count && !serial_485.byte_timeout)
  {
    memset(&serial_data,0,sizeof(serial_data));
    memcpy(&serial_data,&serial_485,sizeof(serial_data));
    memset(&serial_485,0,sizeof(serial_485));
    return 1;
  }
  return 0;
}

void protocol_parsing(void)
{
	uint32_t current_time = HAL_GetTick(); // 获取当前系统时间（单位ms）
	if(current_time-CR_time <=5000)         //上电小于5秒
	{
		if(get_serial_port())
    {
       if((serial_data.rx_buffer[0] == 0x7E) && (serial_data.rx_buffer[serial_data.rx_count - 1] == 0xCE))
       {
			
			   	pc_protocol_parsing(serial_data);//上位机配置工具
		  	}
		}
      
   }else if(get_serial_port())
	
    { printf("OK1");
      switch(config.water_meter_info.protocol_type)
      {
        case PROTOCOL_TYPE_DAOSHENG_WATER_METER://道盛协议
         // daoSheng_protocol_parsing(serial_data);
          break;
        case PROTOCOL_TYPE_FLOW_METER://预留1-word文档协议-流量表
          flow_meter_protocol_parsing(serial_data);
          break;
        case PROTOCOL_TYPE_WATER_METER://预留2-水表
          water_meter_protocol_parsing(serial_data);
          break;
      }
    
    memset(&serial_data,0,sizeof(serial_data));
  }
  else{
    //读取水表数据
    get_water_value();
		
  }
}
