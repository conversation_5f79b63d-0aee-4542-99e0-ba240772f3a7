.\objects\device.o: ..\rtthread\src\device.c
.\objects\device.o: ..\rtthread\include\rtthread.h
.\objects\device.o: ..\rtthread\bsp\rtconfig.h
.\objects\device.o: ..\rtthread\include\rtdebug.h
.\objects\device.o: ..\rtthread\include\rtdef.h
.\objects\device.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\device.o: ..\rtthread\include\rtlibc.h
.\objects\device.o: ..\rtthread\include\libc/libc_stat.h
.\objects\device.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\device.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\time.h
.\objects\device.o: ..\rtthread\include\libc/libc_errno.h
.\objects\device.o: ..\rtthread\include\libc/libc_fcntl.h
.\objects\device.o: ..\rtthread\include\libc/libc_ioctl.h
.\objects\device.o: ..\rtthread\include\libc/libc_dirent.h
.\objects\device.o: ..\rtthread\include\libc/libc_signal.h
.\objects\device.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\signal.h
.\objects\device.o: ..\rtthread\include\libc/libc_fdset.h
.\objects\device.o: ..\rtthread\include\rtservice.h
.\objects\device.o: ..\rtthread\include\rtm.h
.\objects\device.o: ..\rtthread\include\rtthread.h
