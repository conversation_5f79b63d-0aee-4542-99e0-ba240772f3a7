#include "net.h"
#include "ec600m_tcpip.h"
#include "ec600m_common.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include "rt_compat.h"

#define NET_MODULE_POWER(x) HAL_GPIO_WritePin(GPIOB, GPIO_PIN_0, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define NET_MODULE_BOOT(x) HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define NET_MODULE_STATUS HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_2)

UART_HandleTypeDef net_handler;
static NET_SERIAL_PORT serial_port;
static void net_clean(void)
{
  serial_port.byte_timeout = 0;
  serial_port.count = 0;
  memset(serial_port.recv,0,sizeof(serial_port.recv));
}

static void net_send_data(uint8_t *data, uint16_t size)
{
  HAL_UART_Transmit(&net_handler, data, size, size * 10);
}

static void net_send_cmd(char *cmd,uint16_t size)
{
  HAL_UART_Transmit(&net_handler, (uint8_t *)cmd, size, size * 10);
  HAL_UART_Transmit(&net_handler, "\r\n", 2, 2 * 10);
}

static void net_serial_init(uint32_t baud_rate)
{
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_USART1_CLK_ENABLE();

  GPIO_InitTypeDef gpio;
  gpio.Pin = GPIO_PIN_0 | GPIO_PIN_1;
  gpio.Mode = GPIO_MODE_OUTPUT_PP;
  gpio.Pull = GPIO_NOPULL;
  gpio.Speed = GPIO_SPEED_LOW;
  HAL_GPIO_Init(GPIOB, &gpio);

  gpio.Pin = GPIO_PIN_2;
  gpio.Mode = GPIO_MODE_INPUT;
  gpio.Pull = GPIO_PULLUP;
  gpio.Speed = GPIO_SPEED_LOW;
  HAL_GPIO_Init(GPIOB, &gpio);

  gpio.Alternate = GPIO_AF0_USART1;
  gpio.Mode = GPIO_MODE_AF_PP;
  gpio.Pin = GPIO_PIN_9 | GPIO_PIN_10;
  gpio.Pull = GPIO_PULLUP;
  gpio.Speed = GPIO_SPEED_LOW;
  HAL_GPIO_Init(GPIOA, &gpio);

  net_handler.Instance = USART1;
  net_handler.Init.BaudRate = baud_rate;
  net_handler.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  net_handler.Init.Mode = UART_MODE_TX_RX;
  net_handler.Init.Parity = UART_PARITY_NONE;
  net_handler.Init.StopBits = UART_STOPBITS_1;
  net_handler.Init.WordLength = UART_WORDLENGTH_8B;

  HAL_UART_Init(&net_handler);
  __HAL_UART_ENABLE_IT(&net_handler, UART_IT_RXNE);
  HAL_NVIC_SetPriority(USART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(USART1_IRQn);
}

void USART1_IRQHandler(void)
{
  if(__HAL_UART_GET_FLAG(&net_handler, UART_FLAG_RXNE) != RESET)
  {
    if(serial_port.count < NET_RECV_BUF_SIZE)
    {
      serial_port.recv[serial_port.count++] = net_handler.Instance->RDR;
    }else
    {
      memset(&serial_port,0,sizeof(serial_port));
    }
    serial_port.byte_timeout = NET_RECV_BYTE_TIMEOUT;
    __HAL_UART_CLEAR_FLAG(&net_handler, UART_FLAG_RXNE);
  }
}

NET_SERIAL_PORT serial_port = {
  .init = net_serial_init,
  .clean = net_clean,
  .send_data = net_send_data,
  .send_cmd = net_send_cmd,
};

uint8_t net_module_reboot(void)
{
  NET_MODULE_BOOT(0);
  NET_MODULE_POWER(0);
  HAL_Delay(1000);
  serial_port.clean();
  NET_MODULE_POWER(1);

  uint16_t timeout = 10000/100;
  while(timeout--)
  {
    if(timeout)timeout--;
    HAL_Delay(100);
    if(strstr((char *)serial_port.recv,"RDY") != NULL)
      break;
  }
  if(timeout)
  {
    HAL_Delay(1000);
    HAL_Delay(1000);
    return 1;
  }
  return 0;
}

static void net_module_init(void)
{
  __HAL_RCC_GPIOB_CLK_ENABLE();
  GPIO_InitTypeDef gpio;
  gpio.Mode = GPIO_MODE_OUTPUT_PP;
  gpio.Pin = GPIO_PIN_0 | GPIO_PIN_1;
  gpio.Pull = GPIO_NOPULL;
  gpio.Speed = GPIO_SPEED_LOW;
  HAL_GPIO_Init(GPIOB, &gpio);
  
  gpio.Mode = GPIO_MODE_INPUT;
  gpio.Pin = GPIO_PIN_2;
  gpio.Pull = GPIO_PULLUP;
  gpio.Speed = GPIO_SPEED_LOW;
  HAL_GPIO_Init(GPIOB, &gpio);
  
  //默认初始化开机
  // NET_MODULE_BOOT(0);
  // rt_thread_mdelay(100);
  // NET_MODULE_POWER(1);
  net_module_reboot();
}

NET_ERR net_init(NET_INFO *info,uint32_t baud_rate)
{
  
  info->serial_port = &serial_port;
  info->serial_port->init(baud_rate);
	net_module_init();
  /*可以更具开机后解析数据，或者获取产商信息针对性注册相关函数，提供统一调用接口*/
  {
    //移远模块注册
		ec600m_base_function_register(&info->base_function);
    ec600m_tcpip_register(&info->tcpip);
    
    //关闭回显
    if(info->base_function->send_cmd("ATE0","OK",500)!= NET_ERROR_NONE)
      return NET_ERROR_FAIL;
  }
  return NET_ERROR_NONE;
}
