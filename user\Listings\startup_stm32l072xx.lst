


ARM Macro Assembler    Page 1 


    1 00000000         ;*******************************************************
                       ***********************
    2 00000000         ;* File Name          : startup_stm32l072xx.s
    3 00000000         ;* Author             : MCD Application Team
    4 00000000         ;* Description        : STM32l072xx Devices vector table
                        for MDK-ARM toolchain.
    5 00000000         ;*                      This module performs:
    6 00000000         ;*                      - Set the initial SP
    7 00000000         ;*                      - Set the initial PC == Reset_Ha
                       ndler
    8 00000000         ;*                      - Set the vector table entries w
                       ith the exceptions ISR address
    9 00000000         ;*                      - Branches to __main in the C lb
                       rary (which eventually
   10 00000000         ;*                        calls main()).
   11 00000000         ;*                      After Reset the Cortex-M0+ proce
                       ssor is in Thread mode,
   12 00000000         ;*                      priority is Privileged, and the 
                       Stack is set to Main.
   13 00000000         ;*******************************************************
                       ***********************
   14 00000000         ;* @attention
   15 00000000         ;*
   16 00000000         ;* Copyright (c) 2016 STMicroelectronics.
   17 00000000         ;* All rights reserved.
   18 00000000         ;*
   19 00000000         ;* This software component is licensed by ST under BSD 3
                       -Clause license,
   20 00000000         ;* the "License"; You may not use this file except in co
                       mpliance with the
   21 00000000         ;* License. You may obtain a copy of the License at:
   22 00000000         ;*                        opensource.org/licenses/BSD-3-
                       Clause
   23 00000000         ;*
   24 00000000         ;*******************************************************
                       ***********************
   25 00000000         
   26 00000000         ; Amount of memory (in bytes) allocated for Stack
   27 00000000         ; Tailor this value to your application needs
   28 00000000         ; <h> Stack Configuration
   29 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   30 00000000         ; </h>
   31 00000000         
   32 00000000 00000300 
                       Stack_Size
                               EQU              0x00000300
   33 00000000         
   34 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   35 00000000         Stack_Mem
                               SPACE            Stack_Size
   36 00000300         __initial_sp
   37 00000300         
   38 00000300         
   39 00000300         ; <h> Heap Configuration
   40 00000300         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   41 00000300         ; </h>
   42 00000300         
   43 00000300 00000000 



ARM Macro Assembler    Page 2 


                       Heap_Size
                               EQU              0x00000000
   44 00000300         
   45 00000300                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   46 00000000         __heap_base
   47 00000000         Heap_Mem
                               SPACE            Heap_Size
   48 00000000         __heap_limit
   49 00000000         
   50 00000000                 PRESERVE8
   51 00000000                 THUMB
   52 00000000         
   53 00000000         
   54 00000000         ; Vector Table Mapped to Address 0 at Reset
   55 00000000                 AREA             RESET, DATA, READONLY
   56 00000000                 EXPORT           __Vectors
   57 00000000                 EXPORT           __Vectors_End
   58 00000000                 EXPORT           __Vectors_Size
   59 00000000         
   60 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   61 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   62 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   63 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   64 00000010 00000000        DCD              0           ; Reserved
   65 00000014 00000000        DCD              0           ; Reserved
   66 00000018 00000000        DCD              0           ; Reserved
   67 0000001C 00000000        DCD              0           ; Reserved
   68 00000020 00000000        DCD              0           ; Reserved
   69 00000024 00000000        DCD              0           ; Reserved
   70 00000028 00000000        DCD              0           ; Reserved
   71 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   72 00000030 00000000        DCD              0           ; Reserved
   73 00000034 00000000        DCD              0           ; Reserved
   74 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   75 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   76 00000040         
   77 00000040         ; External Interrupts
   78 00000040 00000000        DCD              WWDG_IRQHandler 
                                                            ; Window Watchdog
   79 00000044 00000000        DCD              PVD_IRQHandler ; PVD through EX
                                                            TI Line detect
   80 00000048 00000000        DCD              RTC_IRQHandler ; RTC through EX
                                                            TI Line
   81 0000004C 00000000        DCD              FLASH_IRQHandler ; FLASH
   82 00000050 00000000        DCD              RCC_CRS_IRQHandler 
                                                            ; RCC and CRS
   83 00000054 00000000        DCD              EXTI0_1_IRQHandler 
                                                            ; EXTI Line 0 and 1
                                                            
   84 00000058 00000000        DCD              EXTI2_3_IRQHandler 
                                                            ; EXTI Line 2 and 3
                                                            
   85 0000005C 00000000        DCD              EXTI4_15_IRQHandler 



ARM Macro Assembler    Page 3 


                                                            ; EXTI Line 4 to 15
                                                            
   86 00000060 00000000        DCD              TSC_IRQHandler ; TSC
   87 00000064 00000000        DCD              DMA1_Channel1_IRQHandler 
                                                            ; DMA1 Channel 1
   88 00000068 00000000        DCD              DMA1_Channel2_3_IRQHandler ; DM
                                                            A1 Channel 2 and Ch
                                                            annel 3
   89 0000006C 00000000        DCD              DMA1_Channel4_5_6_7_IRQHandler 
                                                            ; DMA1 Channel 4, C
                                                            hannel 5, Channel 6
                                                             and Channel 7
   90 00000070 00000000        DCD              ADC1_COMP_IRQHandler ; ADC1, CO
                                                            MP1 and COMP2 
   91 00000074 00000000        DCD              LPTIM1_IRQHandler ; LPTIM1
   92 00000078 00000000        DCD              USART4_5_IRQHandler 
                                                            ; USART4 and USART5
                                                            
   93 0000007C 00000000        DCD              TIM2_IRQHandler ; TIM2
   94 00000080 00000000        DCD              TIM3_IRQHandler ; TIM3
   95 00000084 00000000        DCD              TIM6_DAC_IRQHandler 
                                                            ; TIM6 and DAC
   96 00000088 00000000        DCD              TIM7_IRQHandler ; TIM7
   97 0000008C 00000000        DCD              0           ; Reserved
   98 00000090 00000000        DCD              TIM21_IRQHandler ; TIM21
   99 00000094 00000000        DCD              I2C3_IRQHandler ; I2C3
  100 00000098 00000000        DCD              TIM22_IRQHandler ; TIM22
  101 0000009C 00000000        DCD              I2C1_IRQHandler ; I2C1
  102 000000A0 00000000        DCD              I2C2_IRQHandler ; I2C2
  103 000000A4 00000000        DCD              SPI1_IRQHandler ; SPI1
  104 000000A8 00000000        DCD              SPI2_IRQHandler ; SPI2
  105 000000AC 00000000        DCD              USART1_IRQHandler ; USART1
  106 000000B0 00000000        DCD              USART2_IRQHandler ; USART2
  107 000000B4 00000000        DCD              RNG_LPUART1_IRQHandler 
                                                            ; RNG and LPUART1
  108 000000B8 00000000        DCD              0           ; Reserved
  109 000000BC 00000000        DCD              USB_IRQHandler ; USB
  110 000000C0         
  111 000000C0         __Vectors_End
  112 000000C0         
  113 000000C0 000000C0 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  114 000000C0         
  115 000000C0                 AREA             |.text|, CODE, READONLY
  116 00000000         
  117 00000000         ; Reset handler routine
  118 00000000         Reset_Handler
                               PROC
  119 00000000                 EXPORT           Reset_Handler                 [
WEAK]
  120 00000000                 IMPORT           __main
  121 00000000                 IMPORT           SystemInit
  122 00000000 4804            LDR              R0, =SystemInit
  123 00000002 4780            BLX              R0
  124 00000004 4804            LDR              R0, =__main
  125 00000006 4700            BX               R0
  126 00000008                 ENDP
  127 00000008         



ARM Macro Assembler    Page 4 


  128 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  129 00000008         
  130 00000008         NMI_Handler
                               PROC
  131 00000008                 EXPORT           NMI_Handler                    
[WEAK]
  132 00000008 E7FE            B                .
  133 0000000A                 ENDP
  135 0000000A         HardFault_Handler
                               PROC
  136 0000000A                 EXPORT           HardFault_Handler              
[WEAK]
  137 0000000A E7FE            B                .
  138 0000000C                 ENDP
  139 0000000C         SVC_Handler
                               PROC
  140 0000000C                 EXPORT           SVC_Handler                    
[WEAK]
  141 0000000C E7FE            B                .
  142 0000000E                 ENDP
  143 0000000E         PendSV_Handler
                               PROC
  144 0000000E                 EXPORT           PendSV_Handler                 
[WEAK]
  145 0000000E E7FE            B                .
  146 00000010                 ENDP
  147 00000010         SysTick_Handler
                               PROC
  148 00000010                 EXPORT           SysTick_Handler                
[WEAK]
  149 00000010 E7FE            B                .
  150 00000012                 ENDP
  151 00000012         
  152 00000012         Default_Handler
                               PROC
  153 00000012         
  154 00000012                 EXPORT           WWDG_IRQHandler                
[WEAK]
  155 00000012                 EXPORT           PVD_IRQHandler                 
[WEAK]
  156 00000012                 EXPORT           RTC_IRQHandler                 
[WEAK]
  157 00000012                 EXPORT           FLASH_IRQHandler               
[WEAK]
  158 00000012                 EXPORT           RCC_CRS_IRQHandler             
[WEAK]
  159 00000012                 EXPORT           EXTI0_1_IRQHandler             
[WEAK]
  160 00000012                 EXPORT           EXTI2_3_IRQHandler             
[WEAK]
  161 00000012                 EXPORT           EXTI4_15_IRQHandler            
[WEAK]
  162 00000012                 EXPORT           TSC_IRQHandler                 
 [WEAK]
  163 00000012                 EXPORT           DMA1_Channel1_IRQHandler       
[WEAK]
  164 00000012                 EXPORT           DMA1_Channel2_3_IRQHandler     
[WEAK]



ARM Macro Assembler    Page 5 


  165 00000012                 EXPORT           DMA1_Channel4_5_6_7_IRQHandler 
[WEAK]
  166 00000012                 EXPORT           ADC1_COMP_IRQHandler           
[WEAK]
  167 00000012                 EXPORT           LPTIM1_IRQHandler              
[WEAK]
  168 00000012                 EXPORT           USART4_5_IRQHandler            
[WEAK]
  169 00000012                 EXPORT           TIM2_IRQHandler                
[WEAK]
  170 00000012                 EXPORT           TIM3_IRQHandler                
[WEAK]
  171 00000012                 EXPORT           TIM6_DAC_IRQHandler            
[WEAK]
  172 00000012                 EXPORT           TIM7_IRQHandler                
[WEAK]
  173 00000012                 EXPORT           TIM21_IRQHandler               
[WEAK]
  174 00000012                 EXPORT           TIM22_IRQHandler               
[WEAK]
  175 00000012                 EXPORT           I2C1_IRQHandler                
[WEAK]
  176 00000012                 EXPORT           I2C2_IRQHandler                
[WEAK]
  177 00000012                 EXPORT           I2C3_IRQHandler                
[WEAK]
  178 00000012                 EXPORT           SPI1_IRQHandler                
[WEAK]
  179 00000012                 EXPORT           SPI2_IRQHandler                
[WEAK]
  180 00000012                 EXPORT           USART1_IRQHandler              
[WEAK]
  181 00000012                 EXPORT           USART2_IRQHandler              
[WEAK]
  182 00000012                 EXPORT           RNG_LPUART1_IRQHandler         
[WEAK]
  183 00000012                 EXPORT           USB_IRQHandler                 
[WEAK]
  184 00000012         
  185 00000012         
  186 00000012         WWDG_IRQHandler
  187 00000012         PVD_IRQHandler
  188 00000012         RTC_IRQHandler
  189 00000012         FLASH_IRQHandler
  190 00000012         RCC_CRS_IRQHandler
  191 00000012         EXTI0_1_IRQHandler
  192 00000012         EXTI2_3_IRQHandler
  193 00000012         EXTI4_15_IRQHandler
  194 00000012         TSC_IRQHandler
  195 00000012         DMA1_Channel1_IRQHandler
  196 00000012         DMA1_Channel2_3_IRQHandler
  197 00000012         DMA1_Channel4_5_6_7_IRQHandler
  198 00000012         ADC1_COMP_IRQHandler
  199 00000012         LPTIM1_IRQHandler
  200 00000012         USART4_5_IRQHandler
  201 00000012         TIM2_IRQHandler
  202 00000012         TIM3_IRQHandler
  203 00000012         TIM6_DAC_IRQHandler
  204 00000012         TIM7_IRQHandler



ARM Macro Assembler    Page 6 


  205 00000012         TIM21_IRQHandler
  206 00000012         TIM22_IRQHandler
  207 00000012         I2C1_IRQHandler
  208 00000012         I2C2_IRQHandler
  209 00000012         I2C3_IRQHandler
  210 00000012         SPI1_IRQHandler
  211 00000012         SPI2_IRQHandler
  212 00000012         USART1_IRQHandler
  213 00000012         USART2_IRQHandler
  214 00000012         RNG_LPUART1_IRQHandler
  215 00000012         USB_IRQHandler
  216 00000012         
  217 00000012 E7FE            B                .
  218 00000014         
  219 00000014                 ENDP
  220 00000014         
  221 00000014                 ALIGN
  222 00000014         
  223 00000014         ;*******************************************************
                       ************************
  224 00000014         ; User Stack and Heap initialization
  225 00000014         ;*******************************************************
                       ************************
  226 00000014                 IF               :DEF:__MICROLIB
  227 00000014         
  228 00000014                 EXPORT           __initial_sp
  229 00000014                 EXPORT           __heap_base
  230 00000014                 EXPORT           __heap_limit
  231 00000014         
  232 00000014                 ELSE
  247                          ENDIF
  248 00000014         
  249 00000014                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M0+ --apcs=inter
work --depend=.\objects\startup_stm32l072xx.d -o.\objects\startup_stm32l072xx.o
 --predefine="__MICROLIB SETA 1" --predefine="__UVISION_VERSION SETA 540" --pre
define="STM32L072xx SETA 1" --list=.\listings\startup_stm32l072xx.lst ..\system
\startup_stm32l072xx.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 34 in file ..\system\startup_stm32l072xx.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 35 in file ..\system\startup_stm32l072xx.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000300

Symbol: __initial_sp
   Definitions
      At line 36 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 60 in file ..\system\startup_stm32l072xx.s
      At line 228 in file ..\system\startup_stm32l072xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 45 in file ..\system\startup_stm32l072xx.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 47 in file ..\system\startup_stm32l072xx.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 46 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 229 in file ..\system\startup_stm32l072xx.s
Comment: __heap_base used once
__heap_limit 00000000

Symbol: __heap_limit
   Definitions
      At line 48 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 230 in file ..\system\startup_stm32l072xx.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 55 in file ..\system\startup_stm32l072xx.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 60 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 56 in file ..\system\startup_stm32l072xx.s
      At line 113 in file ..\system\startup_stm32l072xx.s

__Vectors_End 000000C0

Symbol: __Vectors_End
   Definitions
      At line 111 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 57 in file ..\system\startup_stm32l072xx.s
      At line 113 in file ..\system\startup_stm32l072xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 115 in file ..\system\startup_stm32l072xx.s
   Uses
      None
Comment: .text unused
ADC1_COMP_IRQHandler 00000012

Symbol: ADC1_COMP_IRQHandler
   Definitions
      At line 198 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 90 in file ..\system\startup_stm32l072xx.s
      At line 166 in file ..\system\startup_stm32l072xx.s

DMA1_Channel1_IRQHandler 00000012

Symbol: DMA1_Channel1_IRQHandler
   Definitions
      At line 195 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 87 in file ..\system\startup_stm32l072xx.s
      At line 163 in file ..\system\startup_stm32l072xx.s

DMA1_Channel2_3_IRQHandler 00000012

Symbol: DMA1_Channel2_3_IRQHandler
   Definitions
      At line 196 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 88 in file ..\system\startup_stm32l072xx.s
      At line 164 in file ..\system\startup_stm32l072xx.s

DMA1_Channel4_5_6_7_IRQHandler 00000012

Symbol: DMA1_Channel4_5_6_7_IRQHandler
   Definitions
      At line 197 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 89 in file ..\system\startup_stm32l072xx.s
      At line 165 in file ..\system\startup_stm32l072xx.s

Default_Handler 00000012

Symbol: Default_Handler
   Definitions
      At line 152 in file ..\system\startup_stm32l072xx.s
   Uses
      None
Comment: Default_Handler unused
EXTI0_1_IRQHandler 00000012

Symbol: EXTI0_1_IRQHandler
   Definitions
      At line 191 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 83 in file ..\system\startup_stm32l072xx.s



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 159 in file ..\system\startup_stm32l072xx.s

EXTI2_3_IRQHandler 00000012

Symbol: EXTI2_3_IRQHandler
   Definitions
      At line 192 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 84 in file ..\system\startup_stm32l072xx.s
      At line 160 in file ..\system\startup_stm32l072xx.s

EXTI4_15_IRQHandler 00000012

Symbol: EXTI4_15_IRQHandler
   Definitions
      At line 193 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 85 in file ..\system\startup_stm32l072xx.s
      At line 161 in file ..\system\startup_stm32l072xx.s

FLASH_IRQHandler 00000012

Symbol: FLASH_IRQHandler
   Definitions
      At line 189 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 81 in file ..\system\startup_stm32l072xx.s
      At line 157 in file ..\system\startup_stm32l072xx.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 135 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 63 in file ..\system\startup_stm32l072xx.s
      At line 136 in file ..\system\startup_stm32l072xx.s

I2C1_IRQHandler 00000012

Symbol: I2C1_IRQHandler
   Definitions
      At line 207 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 101 in file ..\system\startup_stm32l072xx.s
      At line 175 in file ..\system\startup_stm32l072xx.s

I2C2_IRQHandler 00000012

Symbol: I2C2_IRQHandler
   Definitions
      At line 208 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 102 in file ..\system\startup_stm32l072xx.s
      At line 176 in file ..\system\startup_stm32l072xx.s

I2C3_IRQHandler 00000012

Symbol: I2C3_IRQHandler



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 209 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 99 in file ..\system\startup_stm32l072xx.s
      At line 177 in file ..\system\startup_stm32l072xx.s

LPTIM1_IRQHandler 00000012

Symbol: LPTIM1_IRQHandler
   Definitions
      At line 199 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 91 in file ..\system\startup_stm32l072xx.s
      At line 167 in file ..\system\startup_stm32l072xx.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 130 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 62 in file ..\system\startup_stm32l072xx.s
      At line 131 in file ..\system\startup_stm32l072xx.s

PVD_IRQHandler 00000012

Symbol: PVD_IRQHandler
   Definitions
      At line 187 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 79 in file ..\system\startup_stm32l072xx.s
      At line 155 in file ..\system\startup_stm32l072xx.s

PendSV_Handler 0000000E

Symbol: PendSV_Handler
   Definitions
      At line 143 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 74 in file ..\system\startup_stm32l072xx.s
      At line 144 in file ..\system\startup_stm32l072xx.s

RCC_CRS_IRQHandler 00000012

Symbol: RCC_CRS_IRQHandler
   Definitions
      At line 190 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 82 in file ..\system\startup_stm32l072xx.s
      At line 158 in file ..\system\startup_stm32l072xx.s

RNG_LPUART1_IRQHandler 00000012

Symbol: RNG_LPUART1_IRQHandler
   Definitions
      At line 214 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 107 in file ..\system\startup_stm32l072xx.s
      At line 182 in file ..\system\startup_stm32l072xx.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols


RTC_IRQHandler 00000012

Symbol: RTC_IRQHandler
   Definitions
      At line 188 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 80 in file ..\system\startup_stm32l072xx.s
      At line 156 in file ..\system\startup_stm32l072xx.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 118 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 61 in file ..\system\startup_stm32l072xx.s
      At line 119 in file ..\system\startup_stm32l072xx.s

SPI1_IRQHandler 00000012

Symbol: SPI1_IRQHandler
   Definitions
      At line 210 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 103 in file ..\system\startup_stm32l072xx.s
      At line 178 in file ..\system\startup_stm32l072xx.s

SPI2_IRQHandler 00000012

Symbol: SPI2_IRQHandler
   Definitions
      At line 211 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 104 in file ..\system\startup_stm32l072xx.s
      At line 179 in file ..\system\startup_stm32l072xx.s

SVC_Handler 0000000C

Symbol: SVC_Handler
   Definitions
      At line 139 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 71 in file ..\system\startup_stm32l072xx.s
      At line 140 in file ..\system\startup_stm32l072xx.s

SysTick_Handler 00000010

Symbol: SysTick_Handler
   Definitions
      At line 147 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 75 in file ..\system\startup_stm32l072xx.s
      At line 148 in file ..\system\startup_stm32l072xx.s

TIM21_IRQHandler 00000012

Symbol: TIM21_IRQHandler
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 205 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 98 in file ..\system\startup_stm32l072xx.s
      At line 173 in file ..\system\startup_stm32l072xx.s

TIM22_IRQHandler 00000012

Symbol: TIM22_IRQHandler
   Definitions
      At line 206 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 100 in file ..\system\startup_stm32l072xx.s
      At line 174 in file ..\system\startup_stm32l072xx.s

TIM2_IRQHandler 00000012

Symbol: TIM2_IRQHandler
   Definitions
      At line 201 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 93 in file ..\system\startup_stm32l072xx.s
      At line 169 in file ..\system\startup_stm32l072xx.s

TIM3_IRQHandler 00000012

Symbol: TIM3_IRQHandler
   Definitions
      At line 202 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 94 in file ..\system\startup_stm32l072xx.s
      At line 170 in file ..\system\startup_stm32l072xx.s

TIM6_DAC_IRQHandler 00000012

Symbol: TIM6_DAC_IRQHandler
   Definitions
      At line 203 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 95 in file ..\system\startup_stm32l072xx.s
      At line 171 in file ..\system\startup_stm32l072xx.s

TIM7_IRQHandler 00000012

Symbol: TIM7_IRQHandler
   Definitions
      At line 204 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 96 in file ..\system\startup_stm32l072xx.s
      At line 172 in file ..\system\startup_stm32l072xx.s

TSC_IRQHandler 00000012

Symbol: TSC_IRQHandler
   Definitions
      At line 194 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 86 in file ..\system\startup_stm32l072xx.s
      At line 162 in file ..\system\startup_stm32l072xx.s




ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

USART1_IRQHandler 00000012

Symbol: USART1_IRQHandler
   Definitions
      At line 212 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 105 in file ..\system\startup_stm32l072xx.s
      At line 180 in file ..\system\startup_stm32l072xx.s

USART2_IRQHandler 00000012

Symbol: USART2_IRQHandler
   Definitions
      At line 213 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 106 in file ..\system\startup_stm32l072xx.s
      At line 181 in file ..\system\startup_stm32l072xx.s

USART4_5_IRQHandler 00000012

Symbol: USART4_5_IRQHandler
   Definitions
      At line 200 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 92 in file ..\system\startup_stm32l072xx.s
      At line 168 in file ..\system\startup_stm32l072xx.s

USB_IRQHandler 00000012

Symbol: USB_IRQHandler
   Definitions
      At line 215 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 109 in file ..\system\startup_stm32l072xx.s
      At line 183 in file ..\system\startup_stm32l072xx.s

WWDG_IRQHandler 00000012

Symbol: WWDG_IRQHandler
   Definitions
      At line 186 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 78 in file ..\system\startup_stm32l072xx.s
      At line 154 in file ..\system\startup_stm32l072xx.s

38 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000000

Symbol: Heap_Size
   Definitions
      At line 43 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 47 in file ..\system\startup_stm32l072xx.s
Comment: Heap_Size used once
Stack_Size 00000300

Symbol: Stack_Size
   Definitions
      At line 32 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 35 in file ..\system\startup_stm32l072xx.s
Comment: Stack_Size used once
__Vectors_Size 000000C0

Symbol: __Vectors_Size
   Definitions
      At line 113 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 58 in file ..\system\startup_stm32l072xx.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 121 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 122 in file ..\system\startup_stm32l072xx.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 120 in file ..\system\startup_stm32l072xx.s
   Uses
      At line 124 in file ..\system\startup_stm32l072xx.s
Comment: __main used once
2 symbols
388 symbols in table
