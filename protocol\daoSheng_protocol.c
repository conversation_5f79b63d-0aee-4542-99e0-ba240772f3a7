/**
 * 道盛协议水表协议解析，详情看协议文档
 *  RS485/MBUS 接口
//波特率 9600 （可设置 300,600,1200,2400,4800,9600）
//校验和 None （可设置 None, Even, Odd）
地址 1
*/
#include "daoSheng_protocol.h"
#include "modbus_rtu.h"
#include "system.h"
#include <stdio.h>
#include"lcd12864.h"
#include <stdlib.h>
#include <math.h>
#include <net_protocol.h>
#include "string.h"
//

#define TXmodDATALEN    16  //发送数据长度
//M-bus 相关定义
#define MBUS_HEADER 0x68
#define MBUS_TRAILER 0x16
#define MBUS_MIN_FRAME_LEN 6                

uint8_t   TxModbusbuf[TXmodDATALEN];
/*读水表数据指令
设备号 功能 起始寄存器 寄存器数目 效验和
  01    03   00 04    00 02    85 CA //（十六进制数字）1 号设备的流速，即读寄存器 5，6 共 2 个寄存器
  01    03   00 18    00 02    44 0C // 读净累积流量，REG25，REG26 两个寄存器命令
  01    03   05 A1    00 1C    15 2D //REG1442 开始至 REG1469 共计 28 个寄存器;读出水表应用时常用的所有寄存器
  01    03   00 34    00 03    44 05  //读 REG0053 开始的时分秒三个寄存器十六进制指令
 //MODBUS－RTU 状态下，每次最多能够读出 125 个寄存器。而在 MODBUS－ASCII 状态下每次只
//能读出 61 个寄存器
在 ASCII 方式下读取 1 号设备的从寄存器 1 开始的 10 个寄存器的命令如下
：01030000000AF2（回车换行）
其中“：”是 ASCII 方式下的引导符，“F2” 是双字节效验和。求法是把除“：”及回车换行以
外的所有字符的二进制 ASCII 码值进行二进制加法得到的。
 * 心跳，定时上报数据
*/
//uint8_t net_heartbeat(uint8_t central_station_address,UPLOAD_DATA data);
//typedef struct
//{
//  uint32_t positive_cumulative_flow;//正累积流量十进制， 单位： 0.01 立方米
//  uint32_t negative_cumulative_flow;//负累积流量十进制， 单位： 0.01 立方米
//  uint32_t net_cumulative_flow_rate;//净累积流量十进制， 单位： 0.01 立方米
//  uint32_t instantaneous_flow;//瞬时流量十进制， 单位： 0.01 立方米/小时
//  uint16_t instantaneous_flow_rate;//瞬时流速十进制， 单位： 0.01 米/秒
//  uint8_t net_sig;//4G信号
//  uint16_t battery;//电池电量十进制， 单位： 0.01V
//  uint32_t water_meter_number;//水表编号
//  uint8_t warning;//空管报警
//}UPLOAD_DATA;

/*
默认熄屏，按键唤醒；
- 显示正累积、负累积、净累计、瞬时流量、流速、网络无线信号强度、
 电池电压显示、终端时钟、终端ID、基表ID、上报数据确认；
- 显示持续 20 秒自动关闭，LCD 电源由 GPIO 控制。
*/


// 定义命令对数组，便于管理和扩展
    typedef struct {
         uint16_t start_reg;
        uint16_t reg_num;   //开始时间
    } Commandreg;

    const Commandreg txcommands[] = {
        {0x0004, 0x0002}, // DEVICE #1 current speed 设备的流速 (寄存器 5,6)
        {0x0104, 0x0002}, // DEVICE #1 streamflow 设备的净流量 (REG25, REG26)
        {0x05A1, 0x001C}, // DEVICE #1 all_reg 所有寄存器 (REG1442-REG1469)
        {0x0034, 0x0003}  // 
   };

// 定义命令发送状态机
typedef enum {
    CMD_SPEED = 0,
    CMD_STREAMFLOW,
    CMD_ALL_REG,
    CMD_TIME_START,
    CMD_IDLE
} CmdState_t;

static CmdState_t cmd_state = CMD_SPEED;
static uint32_t last_send_time = 0;




// 构建读寄存器命令
static void build_read_command(uint8_t slave_addr, uint16_t start_reg, uint16_t reg_num, uint8_t *command) {
    command[0] = slave_addr;
    command[1] = 0x03;
    command[2] = (start_reg >> 8) & 0xFF;
    command[3] = start_reg & 0xFF;
    command[4] = (reg_num >> 8) & 0xFF;
    command[5] = reg_num & 0xFF;
    uint16_t crc = modbus_crc16(command, 6);
    command[6] = crc & 0xFF;
    command[7] = (crc >> 8) & 0xFF;
}

// 解析 IEEE754 单精度浮点数
float parse_ieee754(const uint8_t *data) {
    uint32_t value = ((uint32_t)data[3] << 24) | ((uint32_t)data[2] << 16) | ((uint32_t)data[1] << 8) | data[0];
    return *(float*)&value;
}
// 解析 LONG 型数据并根据小数点位置调整
int32_t parse_long(const uint8_t *data) {
    return ((int32_t)data[3] << 24) | ((int32_t)data[2] << 16) | ((int32_t)data[1] << 8) | data[0];
}



void parse_modbus_data(uint8_t *data, uint16_t len, MeterData *result) {
    // 解析基本数据
    result->flow_rate = parse_ieee754(&data[(1447-1437)*2]);
    result->velocity = parse_ieee754(&data[(1449-1437)*2]);
    
    // 计算净累积量
    int decimal_pos = (int16_t)(data[(1445-1437)*2]<<8 | data[(1445-1437)*2+1]);
    int32_t raw_value = parse_long(&data[(1443-1437)*2]);
    result->net_accumulated = raw_value * pow(10, decimal_pos-3);
    
    // 解析状态信息
    result->work_status = parse_long(&data[(1460-1437)*2]);
    
    // 解析软件版本
    memcpy(result->software_version, &data[(1468-1437)*2], 4);
    result->software_version[4] = '\0';
}

void process_meter_data(const MeterData *data) {
    char buf[32];
    
    // 显示瞬时流量
    sprintf(buf, "流量:%.2fm3/s", data->flow_rate);
    lcd_show_string(0, 0, buf, 1, FONT_DEFAULT);
    
    // 显示累积量
    sprintf(buf, "累积:%.2fm3", data->net_accumulated);
    lcd_show_string(0, 16, buf, 1, FONT_DEFAULT);
    
    // 显示电池电压
    sprintf(buf, "电压:%.2fV", data->battery_voltage);
    lcd_show_string(0, 32, buf, 1, FONT_DEFAULT);
}

/**
 * 调整长整型数值的小数点位置
 * 
 * @param value 需要调整的32位有符号整数值
 * @param decimal_pos 小数点位置参数，决定数值的缩放因子
 * 
 * @return 调整后的双精度浮点数值
 * 
 * 该函数通过计算缩放因子来调整整数数值的小数点位置。
 * 缩放因子基于decimal_pos参数计算，以10的幂次形式进行缩放。
 */
double adjust_long_value(int32_t value, int8_t decimal_pos) {
    double factor = 1.0;
    
    // 当decimal_pos > 3时，计算正向缩放因子（乘以10的幂次）
    for (int i = 0; i < decimal_pos - 3; i++) {
        factor *= 10;
    }
    
    // 当decimal_pos < 3时，计算负向缩放因子（除以10的幂次）
    for (int i = 0; i > decimal_pos - 3; i--) {
        factor /= 10;
    }
    
    // 应用缩放因子并返回结果
    return value * factor;
}


// 假设的 CRC 校验函数，需要根据实际 M-bus 协议实现
/**
 * @brief 计算给定数据的M-Bus CRC16校验码
 *
 * 根据M-Bus协议计算给定数据的CRC16校验码。
 *
 * @param data 待计算CRC16校验码的数据指针
 * @param length 待计算CRC16校验码的数据长度
 *
 * @return 计算得到的CRC16校验码
 *
 * @note 此处仅为示例，需根据M-Bus协议实际CRC算法实现
 */
uint16_t mbus_crc16( uint8_t *data, uint16_t length) {
    // 此处仅为示例，需根据 M-bus 协议实际 CRC 算法实现
    uint16_t crc = 0xFFFF;
    for (uint16_t i = 0; i < length; i++) {
        crc ^= (uint16_t)data[i];
        for (uint8_t j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc >>= 1;
            }
        }
    }
    return crc;
}




static uint32_t last_reset_time = 0;

static void schedule_commands(void) {
    uint32_t current_time =  HAL_GetTick();//HAL_GetTick();// 获取当前系统时间（单位ms）

    // 每秒重置状态机
    if (current_time - last_reset_time >= 1000) {
			if(cmd_state==CMD_IDLE)
			{
				cmd_state = CMD_SPEED;
			}
       // 
        last_reset_time = current_time;
			
    }
	
    // 检查是否达到分时间隔（建议≥20ms）
    if ((current_time - last_send_time < 20) && (cmd_state != CMD_SPEED)) return;//发第一个指令不延时

    switch (cmd_state) {
        case CMD_SPEED:
						
            build_read_command(0x01, txcommands[0].start_reg, txcommands[0].reg_num, TxModbusbuf);
            serial_485_send_dat(TxModbusbuf, 0x08);
            cmd_state = CMD_STREAMFLOW;
				
            break;
        case CMD_STREAMFLOW:
            build_read_command(0x01, txcommands[1].start_reg, txcommands[1].reg_num, TxModbusbuf);
            serial_485_send_dat(TxModbusbuf, 0x08);
            cmd_state = CMD_ALL_REG;
            break;
        case CMD_ALL_REG:
            build_read_command(0x01, txcommands[2].start_reg, txcommands[2].reg_num, TxModbusbuf);
            serial_485_send_dat(TxModbusbuf, 0x08);
            cmd_state = CMD_TIME_START;
            break;
        case CMD_TIME_START:
            build_read_command(0x01, txcommands[3].start_reg, txcommands[3].reg_num, TxModbusbuf);
            serial_485_send_dat(TxModbusbuf, 0x08);
            cmd_state = CMD_IDLE; // 等待下一周期
            break;
        default:
            break;
    }
    last_send_time = current_time;
}


void get_water_value(void)

{
    // TODO: implement water value reading logic
    schedule_commands();    // 调度命令发送
}

 // 解析函数
void parseWaterMeterData(const uint8_t *buf, int len,
                         int *netFlow,
                         int *posFlow,
                         int *negFlow,
                         int *instFlow,
                         int *velocity)
{
    if (len < 5) {
        printf("数据长度不足！\n");
        return;
    }

    // 跳过 Modbus RTU 帧头 (地址1 + 功能码1 + 字节数1)
    const uint8_t *data = buf + 3;
    int dataLen = len - 5; // 去掉头3和CRC2

    // 宏：取寄存器 (大端字节，高字节在前；32位组合时低字在前)
    #define REG16(i)   ((data[(i)*2] << 8) | data[(i)*2+1])
    #define REG32(i)   ((REG16(i+1) << 16) | REG16(i))
    
    // 净累计流量 (REG1443–1444 → offset=1,2)
    int netRaw = REG32(1);
    
    // 正累计流量 (REG1464–1465 → offset=22,23)
    int posRaw = REG32(22);


    // 负累计流量 = 正 - 净
    int negRaw = posRaw - netRaw;

    // 瞬时流量 (IEEE754 float, REG1447–1448 → offset=5,6)
    uint32_t instRaw = ((uint32_t)REG16(5) << 16) | REG16(6);
    float instVal;
    memcpy(&instVal, &instRaw, sizeof(float));

    // 流速 (IEEE754 float, REG1449–1450 → offset=7,8)
    uint32_t velRaw = ((uint32_t)REG16(7) << 16) | REG16(8);
    float velVal;
    memcpy(&velVal, &velRaw, sizeof(float));

    // 输出整数（取整）
    *netFlow = netRaw;
    *posFlow = posRaw;
    *negFlow = negRaw;
    *instFlow = (int)instVal;
    *velocity = (int)velVal;
}

UPLOAD_DATA dpload_data={0};
 

    int netFlow, posFlow, negFlow, instFlow, velocity;
/*===============================================================
函数名：daoSheng_protocol_parsing(SERIAL serial);
功   能：超声波水表/热量表通讯协议解析
参数:     SERIAL serial
输   出:  无
修   改:
 ===============================================================*/ 
 void daoSheng_protocol_parsing(SERIAL serial)
 {
     // 你接收到的字节数据 (示例)
//    uint8_t recvData[] = {
//        0x01,0x03,0x38,0x00,0x01,0x03,0x34,0x00,0x00,0x00,0x03,0x00,0x00,0x00,0x00,0x00,
//        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x80,0x00,0x00,0x44,0x4D,0x62,0x40,0x40,
//        0x64,0x00,0x34,0x00,0x34,0x00,0x00,0x01,0x7E,0x00,0x30,0x08,0x59,0x00,0x00,0x03,
//        0x34,0x00,0x00,0x00,0x00,0x90,0x11,0x00,0x13,0x00,0x00,0x7F,0xB7
//    };
    uint16_t CRC_Data;
     int len = serial.rx_count;
    uint16_t calc_crc= (serial.rx_buffer[serial.rx_count-2] | (serial.rx_buffer[serial.rx_count-1] << 8));  // 接收的CRC校验码

    CRC_Data = mbus_crc16(serial.rx_buffer, serial.rx_count - 2);  //不包含校验码的剩余部分计算CRC校验码

    if(calc_crc!=CRC_Data)

    {
       // Serial_print((uint8_t *)"\r\nM-bus数据接收失败\r\n");
        return;  //返回失败

    }
    else
    {
			parseWaterMeterData(serial.rx_buffer, len, &netFlow, &posFlow, &negFlow, &instFlow, &velocity);
				     printf("净累计流量: %d\n", netFlow);
             printf("正累计流量: %d\n", posFlow);
             printf("负累计流量: %d\n", negFlow);
             printf("瞬时流量: %d\n", instFlow);
             printf("流速: %d\n", velocity);
            
       
       // Serial_print((uint8_t *)"\r\nM-bus数据接收成功\r\n");
         //M-bus数据解析
//       int32_t streamflow_value = 0;
//			 int32_t total_flow_value=0;
//		   int32_t time_start_value=0;
//           // 1. 声明 MeterData 变量
//      static  MeterData meter_data;
   /*
     CMD_SPEED = 0,
    CMD_STREAMFLOW,
    CMD_ALL_REG,
    CMD_TIME_START,
    CMD_IDLE
    */
     // 调用 Modbus RTU 解析函数
//     switch (cmd_state) {
//        case CMD_SPEED: // 流速命令处理

//            streamflow_value = parse_long((serial.rx_buffer + 4));
//            float streamflow = adjust_long_value(streamflow_value, 3);
//            dpload_data.instantaneous_flow_rate = streamflow;
//            printf("实时流速 = %.3f", streamflow);           
//            printf(" m³/s\r\n");
//            break;
//        case CMD_STREAMFLOW: // 正累计流量命令处理
//            total_flow_value = parse_long(serial.rx_buffer + 4);
//            float total_flow = adjust_long_value(total_flow_value, 3);
//            dpload_data.positive_cumulative_flow = total_flow;//累计流量
//            printf("累计流量 = %f", total_flow);
//            printf("累计流量单位 = m³\r\n");
//            //lcd_show_string(0, 1, "累计流量: 2.4 m³", 1, FONT_DEFAULT);
//            break;
//        case CMD_ALL_REG://整包数据28个寄存器数据解析
            
             

//            break;
//        case CMD_TIME_START: 
//  
//             parse_modbus_data(serial.rx_buffer+2, serial.rx_count-2,&meter_data);
//             process_meter_data( &meter_data);
//           //  lcd_show_string(0, 3, "解析到整包数据", 1, FONT_DEFAULT);   
//       
//        default:
//            break;
//        }                           
                    
        

    }
 }
 
