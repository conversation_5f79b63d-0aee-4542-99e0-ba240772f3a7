#include "uart.h"
#include <stdio.h>
#include "rt_compat.h"
#include <string.h>

SERIAL serial_485;
UART_HandleTypeDef serial_485_handler;

void serial_485_send_dat(uint8_t *dat,uint8_t len)
{
  HAL_GPIO_WritePin(GPIOB,GPIO_PIN_8,GPIO_PIN_SET);
	for(uint8_t i = 0;i < len;i++)
		HAL_UART_Transmit(&serial_485_handler,&dat[i],1,50);	
	
  HAL_GPIO_WritePin(GPIOB,GPIO_PIN_8,GPIO_PIN_RESET);
}

void serial_485_init(WATER_METER_INFO info) 
{ 
  __HAL_RCC_GPIOA_CLK_ENABLE();
	__HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_USART2_CLK_ENABLE();	///////

 GPIO_InitTypeDef GPIO_InitStruct = {0};

  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pin = GPIO_PIN_8;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_MEDIUM;
  HAL_GPIO_Init(GPIOB,&GPIO_InitStruct);
  HAL_GPIO_WritePin(GPIOB,GPIO_PIN_8,GPIO_PIN_RESET);

  

  // USART2 TX/RX��������
  GPIO_InitStruct.Pin = GPIO_PIN_2|GPIO_PIN_3;
  GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH ;//GPIO_SPEED_FREQ_VERY_HIGH;
  GPIO_InitStruct.Alternate = GPIO_AF4_USART2;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  serial_485_handler.Instance = USART2;
  switch(info.baud_rate_id)
  {
    case BAUD_RATE_ID_1200: serial_485_handler.Init.BaudRate = 1200;break;
    case BAUD_RATE_ID_2400: serial_485_handler.Init.BaudRate = 2400;break;
    case BAUD_RATE_ID_4800: serial_485_handler.Init.BaudRate = 4800;break;
    case BAUD_RATE_ID_9600: serial_485_handler.Init.BaudRate = 9600;break;
    case BAUD_RATE_ID_19200: serial_485_handler.Init.BaudRate = 19200;break;
    case BAUD_RATE_ID_38400: serial_485_handler.Init.BaudRate =38400;break;
    default: serial_485_handler.Init.BaudRate = 9600;break;
	} 
	
  switch(info.parity_id)
  {
    case PARITY_ID_NONE: serial_485_handler.Init.Parity = UART_PARITY_NONE;break;
    case PARITY_ID_ODD: serial_485_handler.Init.Parity = UART_PARITY_ODD;break;
    case PARITY_ID_EVEN: serial_485_handler.Init.Parity = UART_PARITY_EVEN;break;
    default: serial_485_handler.Init.Parity = UART_PARITY_NONE;break;
  }
  switch(info.stop_bit_id)
  {
    case STOP_BIT_ID_1: serial_485_handler.Init.StopBits = UART_STOPBITS_1;break;
    case STOP_BIT_ID_2: serial_485_handler.Init.StopBits = UART_STOPBITS_2;break;
    default: serial_485_handler.Init.StopBits = UART_STOPBITS_1;break;
  }
  switch(info.data_bit_id)
  {
    case DATA_BIT_ID_7: serial_485_handler.Init.WordLength = UART_WORDLENGTH_7B;break;
    case DATA_BIT_ID_8: serial_485_handler.Init.WordLength = UART_WORDLENGTH_8B;break;
    case DATA_BIT_ID_9: serial_485_handler.Init.WordLength = UART_WORDLENGTH_9B;break;
    default: serial_485_handler.Init.WordLength = UART_WORDLENGTH_8B;break;
  }
  serial_485_handler.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  serial_485_handler.Init.Mode = UART_MODE_TX_RX;
	
  HAL_UART_Init(&serial_485_handler);
	__HAL_UART_ENABLE_IT(&serial_485_handler,UART_IT_RXNE);
	
  HAL_NVIC_EnableIRQ(USART2_IRQn);
	HAL_NVIC_SetPriority(USART2_IRQn,3,3);
}

void USART2_IRQHandler(void)
{
  if(__HAL_UART_GET_FLAG(&serial_485_handler,UART_FLAG_RXNE) != RESET)
  {
    if(serial_485.rx_count < SERIAL_BUFFER_SIZE)
    {
      serial_485.rx_buffer[serial_485.rx_count++] = serial_485_handler.Instance->RDR;
    }else
    {
      memset(&serial_485,0,sizeof(serial_485));
    }
    serial_485.byte_timeout = SERIAL_TIMEOUT;
    __HAL_UART_CLEAR_FLAG(&serial_485_handler,UART_FLAG_RXNE);
  }
}
