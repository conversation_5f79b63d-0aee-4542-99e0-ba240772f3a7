.\objects\clock.o: ..\rtthread\src\clock.c
.\objects\clock.o: ..\rtthread\include\rthw.h
.\objects\clock.o: ..\rtthread\include\rtthread.h
.\objects\clock.o: ..\rtthread\bsp\rtconfig.h
.\objects\clock.o: ..\rtthread\include\rtdebug.h
.\objects\clock.o: ..\rtthread\include\rtdef.h
.\objects\clock.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\clock.o: ..\rtthread\include\rtlibc.h
.\objects\clock.o: ..\rtthread\include\libc/libc_stat.h
.\objects\clock.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\clock.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\time.h
.\objects\clock.o: ..\rtthread\include\libc/libc_errno.h
.\objects\clock.o: ..\rtthread\include\libc/libc_fcntl.h
.\objects\clock.o: ..\rtthread\include\libc/libc_ioctl.h
.\objects\clock.o: ..\rtthread\include\libc/libc_dirent.h
.\objects\clock.o: ..\rtthread\include\libc/libc_signal.h
.\objects\clock.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\signal.h
.\objects\clock.o: ..\rtthread\include\libc/libc_fdset.h
.\objects\clock.o: ..\rtthread\include\rtservice.h
.\objects\clock.o: ..\rtthread\include\rtm.h
.\objects\clock.o: ..\rtthread\include\rtthread.h
