#ifndef __RT_COMPAT_H__
#define __RT_COMPAT_H__

/* 
 * RT-Thread compatibility definitions for bare metal
 * This file provides basic definitions to replace RT-Thread macros
 */

#include <stdint.h>
#include <stddef.h>

// RT-Thread compatibility macros
#define NULL         ((void*)0)
#define RT_TRUE         1
#define RT_FALSE        0

// Error codes
#define RT_EOK          0
#define RT_ERROR        1
#define RT_ETIMEOUT     2
#define RT_EFULL        3
#define RT_EEMPTY       4
#define RT_ENOMEM       5
#define RT_ENOSYS       6
#define RT_EBUSY        7
#define RT_EIO          8
#define RT_EINTR        9
#define RT_EINVAL       10

// Basic type aliases
typedef uint8_t     rt_uint8_t;
typedef uint16_t    rt_uint16_t;
typedef uint32_t    rt_uint32_t;
typedef int8_t      rt_int8_t;
typedef int16_t     rt_int16_t;
typedef int32_t     rt_int32_t;
typedef uint32_t    rt_size_t;
typedef int32_t     rt_err_t;
typedef uint32_t    rt_tick_t;
typedef uint32_t    rt_base_t;
typedef uint32_t    rt_ubase_t;

// Alignment macro
#define RT_ALIGN_SIZE   4
#define RT_ALIGN(size, align)   (((size) + (align) - 1) & ~((align) - 1))

#endif /* __RT_COMPAT_H__ */
