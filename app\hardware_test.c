/**
 * 硬件资源测试函数
 * 用于验证所有硬件资源的正常工作状态
 */

#include "hardware_test.h"
#include "main.h"
#include "stm32l0xx_hal.h"
#include <stdio.h>
#include <string.h>
#include "adc.h"
#include "rtc.h"
#include "lcd12864.h"
#include "uart.h"
#include "debug.h"
#include "stm32_flash.h"
#include "bsp_board.h"
#include "user_config.h"
#include "net.h"

// 测试结果统计
static uint8_t test_pass_count = 0;
static uint8_t test_fail_count = 0;
static uint8_t total_tests = 0;

// 测试结果宏定义
#define TEST_PASS   1
#define TEST_FAIL   0

// 测试结果记录函数
static void record_test_result(const char* test_name, uint8_t result)
{
    total_tests++;
    if(result == TEST_PASS) {
        test_pass_count++;
        printf("[PASS] %s 测试通过\r\n", test_name);
    } else {
        test_fail_count++;
        printf("[FAIL] %s 测试失败\r\n", test_name);
    }
}

/**
 * 系统时钟测试
 */
static uint8_t test_system_clock(void)
{
    printf("\n=== 系统时钟测试 ===\r\n");
    
    uint32_t hclk_freq = HAL_RCC_GetHCLKFreq();
    uint32_t pclk1_freq = HAL_RCC_GetPCLK1Freq();
    uint32_t pclk2_freq = HAL_RCC_GetPCLK2Freq();
    uint32_t sysclk_freq = HAL_RCC_GetSysClockFreq();
    
    printf("HCLK频率: %lu Hz\r\n", hclk_freq);
    printf("PCLK1频率: %lu Hz\r\n", pclk1_freq);
    printf("PCLK2频率: %lu Hz\r\n", pclk2_freq);
    printf("系统时钟频率: %lu Hz\r\n", sysclk_freq);
    
    // 检查时钟频率是否在合理范围内
    if(hclk_freq > 1000000 && hclk_freq < 50000000) {
        return TEST_PASS;
    }
    return TEST_FAIL;
}

/**
 * GPIO测试
 */
static uint8_t test_gpio(void)
{
    printf("\n=== GPIO测试 ===\r\n");
    
    // 测试LED GPIO (PB12)
    printf("测试LED GPIO (PB12)...\r\n");
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_SET);
    HAL_Delay(100);
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_RESET);
    printf("LED GPIO 切换完成\r\n");
    
    // 测试CAT1电源控制GPIO (PB1)
    printf("测试CAT1电源控制GPIO (PB1)...\r\n");
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, GPIO_PIN_SET);
    HAL_Delay(50);
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, GPIO_PIN_RESET);
    printf("CAT1电源控制GPIO 切换完成\r\n");
    
    // 测试按键GPIO (PC13)
    printf("测试按键GPIO (PC13)...\r\n");
    GPIO_PinState key_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_13);
    printf("按键状态: %s\r\n", key_state == GPIO_PIN_SET ? "未按下" : "按下");
    
    return TEST_PASS;
}

/**
 * ADC测试
 */
static uint8_t test_adc(void)
{
    printf("\n=== ADC测试 ===\r\n");
    
    // 初始化ADC
    ADC_Voltage_Init();
    printf("ADC初始化完成\r\n");
    
    // 获取电源电压
    float voltage = Get_Power_Voltage();
    printf("电源电压: %.2f V\r\n", voltage);
    
    // 检查电压是否在合理范围内 (2.0V - 4.0V)
    if(voltage > 2.0f && voltage < 4.0f) {
        return TEST_PASS;
    }
    
    printf("警告: 电源电压超出正常范围 (2.0V - 4.0V)\r\n");
    return TEST_FAIL;
}

/**
 * RTC测试
 */
static uint8_t test_rtc(void)
{
    printf("\n=== RTC测试 ===\r\n");
    
    // 初始化RTC
    uint8_t rtc_result = rtc_init();
    if(rtc_result != 0) {
        printf("RTC初始化失败\r\n");
        return TEST_FAIL;
    }
    printf("RTC初始化成功\r\n");
    
    // 设置测试时间
    rtc_set_time(2024, 1, 1, 12, 0, 0);
    printf("设置测试时间: 2024-01-01 12:00:00\r\n");
    
    HAL_Delay(1000); // 等待1秒
    
    // 读取时间
    uint16_t year;
    uint8_t month, day, hour, minute, second;
    rtc_get_calendar(&year, &month, &day, &hour, &minute, &second);
    
    printf("当前时间: %04d-%02d-%02d %02d:%02d:%02d\r\n", 
           year, month, day, hour, minute, second);
    
    // 检查时间是否合理
    if(year == 2024 && month == 1 && day == 1 && hour == 12) {
        return TEST_PASS;
    }
    
    return TEST_FAIL;
}

/**
 * UART测试
 */
static uint8_t test_uart(void)
{
    printf("\n=== UART测试 ===\r\n");
    
    // 测试调试串口
    printf("测试调试串口输出...\r\n");
    printf("如果您能看到这条消息，说明调试串口工作正常\r\n");
    
    // 测试485串口初始化
    printf("测试485串口初始化...\r\n");
    // 注意：这里使用默认配置，实际使用时应该使用正确的配置
    uint8_t default_config[8] = {0x01, 0x03, 0x00, 0x00, 0x00, 0x02, 0xC4, 0x0B};
    serial_485_init(default_config);
    printf("485串口初始化完成\r\n");
    
    return TEST_PASS;
}

/**
 * LCD测试
 */
static uint8_t test_lcd(void)
{
    printf("\n=== LCD测试 ===\r\n");
    
    // 初始化LCD
    lcd_init();
    printf("LCD初始化完成\r\n");
    
    // 清屏测试
    lcd_clear(0);
    printf("LCD清屏完成\r\n");
    
    // 显示测试
    display_flow_rate();
    printf("LCD显示测试完成\r\n");
    
    return TEST_PASS;
}

/**
 * EEPROM测试
 */
static uint8_t test_eeprom(void)
{
    printf("\n=== EEPROM测试 ===\r\n");
    
    // 初始化EEPROM
    EEPROM_Init();
    printf("EEPROM初始化完成\r\n");
    
    // 读取配置测试
    read_config(0);
    printf("EEPROM配置读取完成\r\n");
    
    return TEST_PASS;
}

/**
 * 系统Tick测试
 */
static uint8_t test_systick(void)
{
    printf("\n=== 系统Tick测试 ===\r\n");
    
    uint32_t tick_start = HAL_GetTick();
    printf("开始Tick: %lu\r\n", tick_start);
    
    HAL_Delay(1000); // 延时1秒
    
    uint32_t tick_end = HAL_GetTick();
    printf("结束Tick: %lu\r\n", tick_end);
    
    uint32_t elapsed = tick_end - tick_start;
    printf("经过时间: %lu ms\r\n", elapsed);
    
    // 检查时间是否在合理范围内 (950ms - 1050ms)
    if(elapsed >= 950 && elapsed <= 1050) {
        return TEST_PASS;
    }
    
    return TEST_FAIL;
}

/**
 * 网络模块测试
 */
static uint8_t test_network(void)
{
    printf("\n=== 网络模块测试 ===\r\n");
    
    // 测试CAT1模块电源控制
    printf("测试CAT1模块电源控制...\r\n");
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, GPIO_PIN_SET);  // CAT1_POWER(1)
    HAL_Delay(500);
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, GPIO_PIN_RESET); // CAT1_POWER(0)
    printf("CAT1模块电源控制测试完成\r\n");
    
    // 测试CAT1模块复位控制
    printf("测试CAT1模块复位控制...\r\n");
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_10, GPIO_PIN_RESET); // CAT1_RST(0)
    HAL_Delay(100);
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_10, GPIO_PIN_SET);   // CAT1_RST(1)
    printf("CAT1模块复位控制测试完成\r\n");
    
    return TEST_PASS;
}

/**
 * 执行所有硬件资源测试
 */
void hardware_test_all(void)
{
    printf("\n\n");
    printf("========================================\r\n");
    printf("       硬件资源综合测试开始\r\n");
    printf("========================================\r\n");
    
    // 重置测试计数器
    test_pass_count = 0;
    test_fail_count = 0;
    total_tests = 0;
    
    // 执行各项测试
    record_test_result("系统时钟", test_system_clock());
    record_test_result("GPIO", test_gpio());
    record_test_result("ADC", test_adc());
    record_test_result("RTC", test_rtc());
    record_test_result("UART", test_uart());
    record_test_result("LCD", test_lcd());
    record_test_result("EEPROM", test_eeprom());
    record_test_result("系统Tick", test_systick());
    record_test_result("网络模块", test_network());
    
    // 输出测试总结
    printf("\n========================================\r\n");
    printf("       硬件资源测试总结\r\n");
    printf("========================================\r\n");
    printf("总测试项目: %d\r\n", total_tests);
    printf("通过测试: %d\r\n", test_pass_count);
    printf("失败测试: %d\r\n", test_fail_count);
    printf("成功率: %.1f%%\r\n", (float)test_pass_count * 100.0f / total_tests);
    
    if(test_fail_count == 0) {
        printf("🎉 所有硬件资源测试通过！\r\n");
    } else {
        printf("⚠️  有 %d 项测试失败，请检查硬件连接\r\n", test_fail_count);
    }
    
    printf("========================================\r\n\n");
}
