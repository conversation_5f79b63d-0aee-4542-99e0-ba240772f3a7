/**
 * stm32l072cbt6 32MHz 128kb flash + 16kb ram + 6K eeprom
 */
#include "main.h"
#include "stm32l0xx_hal.h"
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include "net.h"
#include "rtc.h"
#include "adc.h"//peripheral/include/
#include "protocol_common.h"
#include "net_protocol.h"
#include "auto_ctrl_task.h"
#include "bsp_board.h"
#include "lcd12864.h"
#include "uart.h"
#include "debug.h"
#include "stm32_flash.h"
#include "adc.h"
#include "rtc.h"
#include "user_config.h"
#include "daoSheng_protocol.h"

NET_INFO net_info = {0,};
float vdda_voltage = 0;
uint8_t measure_complete = 0;

#define CAT1_POWER(x) HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define CAT1_RST(x) HAL_GPIO_WritePin(GPIOB, GPIO_PIN_10, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)

CALENDAR calendar;

// 任务调度相关变量
uint32_t net_task_timer = 0;
uint32_t auto_ctrl_timer = 0;
uint32_t main_task_timer = 0;
uint8_t net_initialized = 0;

static void net_task_init(void)
{
	char ip[20];
	/*初始化网络模块*/
	if(net_init(&net_info,115200) == NET_ERROR_NONE)
	{
		memset(ip,0,sizeof(ip));
		sprintf(ip,"%d.%d.%d.%d",config.net_param.ip[0][0],config.net_param.ip[0][1],config.net_param.ip[0][2],config.net_param.ip[0][3]);
		if(net_info.tcpip->connect(ip, config.net_param.port[0]) == NET_ERROR_NONE)
		{
			/*连接成功，同步时间*/
			CALENDAR calendar_temp;
			memset(&calendar_temp, 0, sizeof(calendar_temp));
			if(net_info.base_function->sync_time(&calendar_temp.year, &calendar_temp.month, &calendar_temp.day, &calendar_temp.hour, &calendar_temp.minute, &calendar_temp.second) == NET_ERROR_NONE)
			{
				rtc_set_time(calendar_temp.year, calendar_temp.month, calendar_temp.day, calendar_temp.hour, calendar_temp.minute, calendar_temp.second);
			}
			net_initialized = 1;
		}
	}
}

static void net_task_process(void)
{
	if(net_initialized)
	{
		/*获取当前时间*/
		rtc_get_calendar(&calendar.year, &calendar.month, &calendar.day, &calendar.hour, &calendar.minute, &calendar.second);
		net_protocol_parsing();
	}
}

static void auto_ctrl_task_process(void)
{
	auto_ctrl();
}
static void MX_GPIO_Init(void) {
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  __HAL_RCC_GPIOB_CLK_ENABLE();
	
  
  GPIO_InitStruct.Pin = GPIO_PIN_12|GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2;//PB12_LED  //PB0_CAT1_POWER
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
	
}



int main(void)
{
	// 系统初始化
	HAL_Init();

	// 时钟配置
	system_clock_init();

	// 初始化系统tick
	HAL_SYSTICK_Config(HAL_RCC_GetHCLKFreq() / 1000);

	__HAL_RCC_PWR_CLK_ENABLE();

	/* 初始化EEPROM */
	EEPROM_Init();
	read_config(0);

	// 板级初始化
	bsp_board_init();
	serial_485_init(config.water_meter_info);
	debug_init(115200);
	rtc_init();

	// LCD初始化
	lcd_init();
	lcd_clear(0);
	ADC_Voltage_Init();
	display_flow_rate();

	// GPIO初始化
	MX_GPIO_Init();
	CAT1_POWER(1);
	HAL_Delay(500);
	CAT1_POWER(0);

	// 网络初始化
	// net_task_init();

	uint32_t CR_time = HAL_GetTick();
	printf("CR_Time = %d\r\n", CR_time);

	// 初始化任务定时器
	// net_task_timer = HAL_GetTick();
	// auto_ctrl_timer = HAL_GetTick();
	main_task_timer = HAL_GetTick();

	while(1)
	{
		uint32_t current_time = HAL_GetTick();

		// // 网络任务处理 - 每100ms执行一次
		// if(current_time - net_task_timer >= 100)
		// {
		// 	net_task_process();
		// 	net_task_timer = current_time;
		// }

		// // 自动控制任务处理 - 每500ms执行一次
		// if(current_time - auto_ctrl_timer >= 500)
		// {
		// 	auto_ctrl_task_process();
		// 	auto_ctrl_timer = current_time;
		// }

		// 主任务处理 - 每5000ms执行一次
		if(current_time - main_task_timer >= 5000)
		{
			protocol_parsing();
			HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_12);

			vdda_voltage = Get_Power_Voltage();
			display_rotate();

			printf("电源电压 = %.2f\r\n", vdda_voltage);
			main_task_timer = current_time;
		}

		// 短暂延时，避免CPU占用过高
		HAL_Delay(1);
	}
}
