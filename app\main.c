/**
 * stm32l072cbt6 32MHz 128kb flash + 16kb ram + 6K eeprom
 */
#include "main.h"
#include "stm32l0xx_hal.h"
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include "net.h"
#include "rtc.h"
#include "adc.h"//peripheral/include/
#include "protocol_common.h"
#include "net_protocol.h"
#include "auto_ctrl_task.h"
#include "bsp_board.h"
#include "lcd12864.h"
#include "uart.h"
#include "debug.h"
#include "stm32_flash.h"
#include "adc.h"
#include "rtc.h"
#include "user_config.h"
#include "daoSheng_protocol.h"

NET_INFO net_info = {0,};
float vdda_voltage = 0;
uint8_t measure_complete = 0;

#define CAT1_POWER(x) HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define CAT1_RST(x) HAL_GPIO_WritePin(GPIOB, GPIO_PIN_10, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)

CALENDAR calendar;

// 任务调度相关变量
uint32_t net_task_timer = 0;
uint32_t auto_ctrl_timer = 0;
uint32_t main_task_timer = 0;
uint8_t net_initialized = 0;

// 硬件测试相关变量
static uint32_t last_monitor_time = 0;

static void net_task_init(void)
{
	char ip[20];
	/*初始化网络模块*/
	if(net_init(&net_info,115200) == NET_ERROR_NONE)
	{
		memset(ip,0,sizeof(ip));
		sprintf(ip,"%d.%d.%d.%d",config.net_param.ip[0][0],config.net_param.ip[0][1],config.net_param.ip[0][2],config.net_param.ip[0][3]);
		if(net_info.tcpip->connect(ip, config.net_param.port[0]) == NET_ERROR_NONE)
		{
			/*连接成功，同步时间*/
			CALENDAR calendar_temp;
			memset(&calendar_temp, 0, sizeof(calendar_temp));
			if(net_info.base_function->sync_time(&calendar_temp.year, &calendar_temp.month, &calendar_temp.day, &calendar_temp.hour, &calendar_temp.minute, &calendar_temp.second) == NET_ERROR_NONE)
			{
				rtc_set_time(calendar_temp.year, calendar_temp.month, calendar_temp.day, calendar_temp.hour, calendar_temp.minute, calendar_temp.second);
			}
			net_initialized = 1;
		}
	}
}

static void net_task_process(void)
{
	if(net_initialized)
	{
		/*获取当前时间*/
		rtc_get_calendar(&calendar.year, &calendar.month, &calendar.day, &calendar.hour, &calendar.minute, &calendar.second);
		net_protocol_parsing();
	}
}

static void auto_ctrl_task_process(void)
{
	auto_ctrl();
}

/**
 * 快速硬件资源测试
 */
static void quick_hardware_test(void)
{
    printf("\n========== 快速硬件测试开始 ==========\r\n");

    // 1. 系统时钟检查
    printf("1. 系统时钟检查:\r\n");
    uint32_t sysclk = HAL_RCC_GetSysClockFreq();
    uint32_t hclk = HAL_RCC_GetHCLKFreq();
    printf("   系统时钟: %lu Hz\r\n", sysclk);
    printf("   HCLK: %lu Hz\r\n", hclk);
    printf("   状态: %s\r\n", (hclk > 1000000) ? "正常" : "异常");

    // 2. GPIO基本测试
    printf("2. GPIO基本测试:\r\n");
    printf("   LED GPIO (PB12) 闪烁测试...\r\n");
    for(int i = 0; i < 3; i++) {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_SET);
        HAL_Delay(200);
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_12, GPIO_PIN_RESET);
        HAL_Delay(200);
    }
    printf("   LED闪烁完成\r\n");

    // 3. 系统Tick测试
    printf("3. 系统Tick测试:\r\n");
    uint32_t tick_start = HAL_GetTick();
    HAL_Delay(1000);
    uint32_t tick_end = HAL_GetTick();
    uint32_t elapsed = tick_end - tick_start;
    printf("   延时1000ms，实际用时: %lu ms\r\n", elapsed);
    printf("   状态: %s\r\n", (elapsed >= 950 && elapsed <= 1050) ? "正常" : "异常");

    // 4. 串口输出测试
    printf("4. 串口输出测试:\r\n");
    printf("   如果您能看到这条消息，说明调试串口工作正常\r\n");
    printf("   状态: 正常\r\n");

    // 5. 按键状态检查
    printf("5. 按键状态检查:\r\n");
    GPIO_PinState key_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_13);
    printf("   按键状态: %s\r\n", key_state == GPIO_PIN_SET ? "未按下" : "按下");
    printf("   状态: 正常\r\n");

    printf("========== 快速硬件测试完成 ==========\r\n\n");
}

/**
 * 硬件状态监控函数
 */
static void hardware_status_monitor(void)
{
    uint32_t current_time = HAL_GetTick();

    // 每10秒监控一次
    if(current_time - last_monitor_time >= 10000) {
        printf("\n--- 硬件状态监控 ---\r\n");

        // 检查系统运行时间
        printf("系统运行时间: %lu ms\r\n", current_time);

        // 检查电源电压
        if(vdda_voltage > 0) {
            printf("电源电压: %.2f V", vdda_voltage);
            if(vdda_voltage < 2.5f) {
                printf(" [警告: 电压偏低]");
            } else if(vdda_voltage > 3.6f) {
                printf(" [警告: 电压偏高]");
            }
            printf("\r\n");
        }

        // 检查按键状态
        GPIO_PinState key_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_13);
        if(key_state == GPIO_PIN_RESET) {
            printf("按键被按下\r\n");
        }

        printf("--- 监控完成 ---\r\n\n");
        last_monitor_time = current_time;
    }
}

/**
 * 硬件错误检测函数
 */
static void hardware_error_check(void)
{
    // 检查系统时钟
    uint32_t hclk = HAL_RCC_GetHCLKFreq();
    if(hclk < 1000000) {
        printf("[错误] 系统时钟异常: %lu Hz\r\n", hclk);
    }

    // 检查电源电压
    if(vdda_voltage > 0) {
        if(vdda_voltage < 2.0f) {
            printf("[错误] 电源电压过低: %.2f V\r\n", vdda_voltage);
        } else if(vdda_voltage > 4.0f) {
            printf("[错误] 电源电压过高: %.2f V\r\n", vdda_voltage);
        }
    }
}

/**
 * 硬件诊断信息输出
 */
static void hardware_diagnostic_info(void)
{
    printf("\n========== 硬件诊断信息 ==========\r\n");

    // 系统信息
    printf("MCU型号: STM32L072CBT6\r\n");
    printf("Flash大小: 128KB\r\n");
    printf("RAM大小: 20KB\r\n");
    printf("EEPROM大小: 6KB\r\n");

    // 时钟信息
    printf("\n时钟配置:\r\n");
    printf("  系统时钟: %lu Hz\r\n", HAL_RCC_GetSysClockFreq());
    printf("  HCLK: %lu Hz\r\n", HAL_RCC_GetHCLKFreq());
    printf("  PCLK1: %lu Hz\r\n", HAL_RCC_GetPCLK1Freq());
    printf("  PCLK2: %lu Hz\r\n", HAL_RCC_GetPCLK2Freq());

    // GPIO配置信息
    printf("\nGPIO配置:\r\n");
    printf("  PB12: LED指示灯\r\n");
    printf("  PB1: CAT1电源控制\r\n");
    printf("  PB10: CAT1复位控制\r\n");
    printf("  PC13: 按键输入\r\n");
    printf("  PA4: ADC电压检测\r\n");

    // 外设状态
    printf("\n外设状态:\r\n");
    printf("  RTC: 已初始化\r\n");
    printf("  ADC: 已初始化\r\n");
    printf("  LCD: 已初始化\r\n");
    printf("  UART: 已初始化\r\n");
    printf("  EEPROM: 已初始化\r\n");

    // 运行状态
    printf("\n运行状态:\r\n");
    printf("  系统运行时间: %lu ms\r\n", HAL_GetTick());
    if(vdda_voltage > 0) {
        printf("  电源电压: %.2f V\r\n", vdda_voltage);
    }

    printf("========== 诊断信息结束 ==========\r\n\n");
}
static void MX_GPIO_Init(void) {
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  __HAL_RCC_GPIOB_CLK_ENABLE();
	
  
  GPIO_InitStruct.Pin = GPIO_PIN_12|GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2;//PB12_LED  //PB0_CAT1_POWER
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
	
}



int main(void)
{
	// 系统初始化
	HAL_Init();

	// 时钟配置
	system_clock_init();

	// 初始化系统tick
	HAL_SYSTICK_Config(HAL_RCC_GetHCLKFreq() / 1000);

	__HAL_RCC_PWR_CLK_ENABLE();

	/* 初始化EEPROM */
	EEPROM_Init();
	read_config(0);

	// 板级初始化
	bsp_board_init();
	serial_485_init(config.water_meter_info);
	debug_init(115200);
	rtc_init();

	// LCD初始化
	lcd_init();
	lcd_clear(0);
	ADC_Voltage_Init();
	display_flow_rate();

	// GPIO初始化
	MX_GPIO_Init();
	CAT1_POWER(1);
	HAL_Delay(500);
	CAT1_POWER(0);

	// 网络初始化
	// net_task_init();

	uint32_t CR_time = HAL_GetTick();
	printf("CR_Time = %d\r\n", CR_time);

	// 执行快速硬件资源测试
	quick_hardware_test();

	// 输出硬件诊断信息
	hardware_diagnostic_info();

	// 初始化任务定时器
	// net_task_timer = HAL_GetTick();
	// auto_ctrl_timer = HAL_GetTick();
	main_task_timer = HAL_GetTick();

	while(1)
	{
		uint32_t current_time = HAL_GetTick();

		// // 网络任务处理 - 每100ms执行一次
		// if(current_time - net_task_timer >= 100)
		// {
		// 	net_task_process();
		// 	net_task_timer = current_time;
		// }

		// // 自动控制任务处理 - 每500ms执行一次
		// if(current_time - auto_ctrl_timer >= 500)
		// {
		// 	auto_ctrl_task_process();
		// 	auto_ctrl_timer = current_time;
		// }

		// 主任务处理 - 每5000ms执行一次
		if(current_time - main_task_timer >= 5000)
		{
			protocol_parsing();
			HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_12);

			vdda_voltage = Get_Power_Voltage();
			display_rotate();

			printf("电源电压 = %.2f\r\n", vdda_voltage);

			// 执行硬件错误检测
			hardware_error_check();

			main_task_timer = current_time;
		}

		// 硬件状态监控
		hardware_status_monitor();

		// 短暂延时，避免CPU占用过高
		HAL_Delay(1);
	}
}
